﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientConsultDetailRepository : IPatientConsultDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientConsultDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<AssessContentValue>> GetAssessValueByCareMainID(string ID, string recordsCode)
        {
            return await _medicalDbContext.PatientConsultDetailInfos
                    .Where(m => m.PatientConsultID == ID && m.RecordsCode == recordsCode && m.DeleteFlag != "*")
                    .Select(m => new AssessContentValue
                    {
                        AssessListID = m.AssessListID,
                        AssessValue = m.AssessValue
                    })
                    .ToListAsync();
        }

        public async Task<List<PatientConsultDetailInfo>> GetDetailByCareMainID(string mainID, string recordsCode)
        {
            return await _medicalDbContext.PatientConsultDetailInfos.Where(m => m.PatientConsultID == mainID && m.RecordsCode == recordsCode && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
