﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SpecialMappingRepository : ISpecialMappingRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public SpecialMappingRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<SpecialMappingInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.SpecialMappingInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public string GetCacheType()
        {
            return CacheType.SpecialMapping.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
        /// <summary>
        /// 根据表名、记录码获取回写配置
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="recordsCode">记录码</param>
        /// <returns></returns>
        public async Task<List<SpecialMappingInfo>> GetSpecialMappingsByTableAndRecordCode(string tableName, string recordsCode)
        {
            var data = await GetCacheAsync() as List<SpecialMappingInfo>;
            return data.Where(m => m.TableInfoName == tableName &&
            (m.RecordsCode == null || m.RecordsCode == recordsCode)).ToList();
        }
    }
}