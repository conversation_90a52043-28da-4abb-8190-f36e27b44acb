﻿using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IInpatientService
    {
        /// <summary>
        /// 同步住院患者数据
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        Task<bool> SyncInpatientData(List<InPatientDataView> inpatientListView);
        /// <summary>
        /// 同步出院患者数据
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        Task<bool> SyncDischargeInpatientData(List<InPatientDataView> inpatientListView);
        /// <summary>
        /// 同步患者主诉信息
        /// </summary>
        /// <param name="stringKeyValue">主诉信息</param>
        /// <returns></returns>
        Task<bool> SyncInpatientChiefComplaintAsync(StringKeyValueView stringKeyValue);
        /// <summary>
        /// 同步无费出院信息
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        Task<bool> SyncNoFeeDischargeInpatientData(List<InPatientDataView> inpatientListView);
        /// <summary>
        /// 同步住院转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        Task<bool> SyncInpatientTransferDeptData(InpatientTransferDataView inpatientTransferDataView);
        /// <summary>
        /// 同步住院转科取消数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        Task<bool> SyncCancelInpatientTransferDeptData(InpatientTransferDataView inpatientTransferDataView);

        /// <summary>
        /// 同步新生儿信息
        /// </summary>
        /// <param name="newBornRecordViews"></param>
        /// <returns></returns>
        Task<bool> SyncNewBornData(List<NewBornRecordView> newBornRecordViews);
        /// <summary>
        /// 同步患者机器数据
        /// </summary>
        /// <param name="clinicDataList">临床数据视图列表</param>
        /// <returns>返回操作是否成功</returns>
        Task<bool> SyncPatientMachineDataAsync(List<ViewModels.ClinicDataView> clinicDataList);
    }
}
