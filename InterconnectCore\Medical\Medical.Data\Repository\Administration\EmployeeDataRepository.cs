﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeDataRepository : IEmployeeDataRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public EmployeeDataRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<EmployeeDataInfo>> GetEmployeeListByEmployeeIDsAsync(List<string> employeeIDs)
        {
            return await _medicalDbContext.EmployeeDataInfos.Where(m => employeeIDs.Contains(m.EmployeeID)).ToListAsync();
        }
        
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.EmployeeDataInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EmployeeDataInfo>>(key, GetDataBaseListData);
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeData.ToString();
        }

    }
}