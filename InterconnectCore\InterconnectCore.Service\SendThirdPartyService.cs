﻿using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Newtonsoft.Json;

namespace InterconnectCore.Service
{
    /// <summary>
    /// 第三方服务发送服务
    /// </summary>
    public class SendThirdPartyService : ISendThirdPartyService
    {
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="appConfigSettingRepository">应用配置设置仓库</param>
        public SendThirdPartyService(
            IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        /// <summary>
        /// 绑定解绑仪器信息
        /// </summary>
        /// <param name="instrumentParams">获取到的仪器信息</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> UnOrBindInstrumentAsync(Dictionary<string, object> instrumentParams)
        {
            // 检查参数有效性
            if (instrumentParams == null || instrumentParams.Count == 0 || !instrumentParams.TryGetValue("isBind", out object isBind) || !instrumentParams.TryGetValue("params", out object instrumentViewObj))
            {
                return false;
            }

            // 获取配置的URL
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "SendUnOrBindInstrument");

            // 检查URL有效性
            if (string.IsNullOrEmpty(url))
            {
                return false;
            }

            // 反序列化仪器视图
            InstrumentView instrumentView = JsonConvert.DeserializeObject<InstrumentView>(instrumentViewObj.ToString());

            // 构造请求头
            var headers = new Dictionary<string, string>
                {
                    {"domain", "SHZHY_HLCCC" },
                    { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
                };

            string responseStr;
            // 根据isBind值执行不同操作
            if (!(bool)isBind)
            {
                var view = new { instrumentView.pid, instrumentView.deviceid };
                responseStr = await HttpHelper.HttpPostAsync(url + "/dc", ListToJson.ToJson(view), "application/json", 30, headers);
                return responseStr.Contains("success");
            }

            // 绑定操作：发送POST请求
            responseStr =  await HttpHelper.HttpPostAsync(url, ListToJson.ToJson(instrumentView), "application/json",30,headers);
            return responseStr.Contains("success");
        }
    }
}
