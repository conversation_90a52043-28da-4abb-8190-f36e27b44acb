﻿using System.ComponentModel;
using System.Reflection;

namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// 锁类型
    /// </summary>
    public enum LockType
    {
        /// <summary>
        /// redis分布式锁
        /// </summary>
        [Description("redis")]
        Redis,

        /// <summary>
        /// 本地锁
        /// </summary>
        [Description("local")]
        Local
    }

    /// <summary>
    /// 枚举扩展
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取枚举描述
        /// </summary>
        /// <param name="enum"></param>
        /// <returns></returns>
        public static string GetDescription(this Enum @enum)
        {
            Type type = @enum.GetType();
            string name = Enum.GetName(type, @enum);
            if (name == null)
            {
                return null;
            }
            FieldInfo field = type.GetField(name);
            DescriptionAttribute attribute = System.Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
            if (attribute == null)
            {
                return name;
            }
            return attribute?.Description;
        }
    }
}