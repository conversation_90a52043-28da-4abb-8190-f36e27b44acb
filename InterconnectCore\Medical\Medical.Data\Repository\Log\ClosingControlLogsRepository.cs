﻿using Medical.Data.Context;
using Medical.Data.Interface.Log;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.Log
{
    public class ClosingControlLogsRepository: IClosingControlLogsRepository
    {

        private readonly MedicalDbContext _dbContext;

        public ClosingControlLogsRepository(MedicalDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<ClosingControlLogsInfo> GetLogByIdAsync(int logID)
        {
            return await _dbContext.ClosingControlLogsInfos.Where(m=>m.LogID == logID ).FirstOrDefaultAsync();
        }

        public async Task<List<ClosingControlLogsInfo>> GetLogsByIdAsync(List<int> logIds)
        {
            return await _dbContext.ClosingControlLogsInfos.Where(m => logIds.Contains(m.LogID)).ToListAsync();

        }
    }
}
