﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.ViewModels;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.Extensions.Options;
using NLog;

namespace InterconnectCore.Service
{
    public class DataTableEditListService
    {
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IOptions<SystemConfig> _options;
        private readonly IEMRListRepository _eMRListRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        public DataTableEditListService(
              IUnitOfWork<MedicalDbContext> unitOfWork
            , IEMRListRepository eMRListRepository
            , ISettingDescriptionRepository settingDescriptionRepository
            )
        {
            _unitOfWork = unitOfWork;
            _eMRListRepository = eMRListRepository;
            _settingDescriptionRepository = settingDescriptionRepository;

        }
        /// <summary>
        /// 添加异动记录
        /// </summary>
        /// <param name="inpatientID">患者主键</param>
        /// <param name="stationID">病区主键</param>
        /// <param name="tableName">表名</param>
        /// <param name="fileClass">病历码</param>
        /// <param name="serialNumber">序列号</param>
        /// <param name="recordListID">风险表主键</param>
        /// <returns></returns>
        public async Task AddEditLog(string inpatientID, int stationID, string tableName, int fileClass, string serialNumber, int? recordListID)
        {
            var emrInfo = await _eMRListRepository.GetEmrByFileClass(fileClass);
            if (emrInfo == null)
            {
                _logger.Warn($"未找到fileClass为{fileClass}的病历信息");
                return;
            }
            stationID = emrInfo.MergeFlag ? 0 : stationID;
            if (fileClass == 11)
            {
                //单独导管报表(True:单独/False:合并)
                var setting = await _settingDescriptionRepository.GetTypeValue("IndividualTubeReport");
                serialNumber = (string.IsNullOrEmpty(setting) || setting != "True") ? "" : serialNumber;
            }
            if (fileClass == 14)
            {
                serialNumber = "";
            }
            SetDataTableEditListInfo(tableName, stationID, inpatientID, recordListID, fileClass, serialNumber);
        }
        /// <summary>
        /// 创建异动实体
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="inpatientID">患者主键</param>
        /// <param name="recordListID">记录表主键</param>
        /// <param name="fileClass">电子病历序号</param>
        /// <param name="serialNumber">序列号</param>
        /// <returns></returns>
        private void SetDataTableEditListInfo(string tableName, int stationID, string inpatientID, int? recordListID, int fileClass, string serialNumber)
        {
            var DataTableEditInfo = new DataTableEditListInfo()
            {
                TableName = tableName,
                EditDateTime = DateTime.Now,
                DataPumpFlag = "",
                ID = null,
                StationID = stationID,
                InpatientID = inpatientID,
                RecordListID = recordListID,
                FileClass = fileClass,
                SerialNumber = serialNumber ?? ""
            };
            _unitOfWork.GetRepository<DataTableEditListInfo>().Insert(DataTableEditInfo);
        }
    }
}
