﻿using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Medical.ViewModels.View;
using NLog;

namespace InterconnectCore.Service
{
    public class SyncBackPatientScoreService 
    {
        private Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IPatientScoreMainRepository _patientScoreMainRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IEmployeelDataRepository _employeelDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IAssessScoreRangeRepository _assessScoreRangeRepository;
        private readonly IRequestApiService _requestApiService;

        public SyncBackPatientScoreService(
            IPatientScoreMainRepository patientScoreMainRepository,
            IHospitalListRepository hospitalListRepository,
            IStationListRepository stationListRepository,
            IInpatientDataRepository inpatientDataRepository,
            IPatientBasicDataRepository patientBasicDataRepository,
            IEmployeelDataRepository employeelDataRepository,
            IDepartmentListRepository departmentListRepository,
            IAssessScoreRangeRepository assessScoreRangeRepository,
            IRequestApiService requestApiService)
        {
            _patientScoreMainRepository = patientScoreMainRepository;
            _hospitalListRepository = hospitalListRepository;
            _stationListRepository = stationListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _employeelDataRepository = employeelDataRepository;
            _departmentListRepository = departmentListRepository;
            _assessScoreRangeRepository = assessScoreRangeRepository;
            _requestApiService = requestApiService;
        }


        /// <summary>
        /// 同步风险相关数据给HIS
        /// </summary>
        /// <param name="syncData"></param>
        /// <param name="language"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncDataAsync(SyncDataBackLog syncData, int language, string hospitalID)
        {
            var dataView = await _patientScoreMainRepository.GetPatientScoreViewByID(syncData.DataKey);
            if (dataView == null)
            {
                return false;
            }
            var hospital = await _hospitalListRepository.GetHospital(hospitalID);
            if (hospital == null)
            {
                _logger.Error($"获取医院信息失败hospitalID={hospitalID}");
                return false;
            }
            var station = await _stationListRepository.GetAsync(dataView.StationID);
            if (station == null)
            {
                _logger.Error($"获取病区信息失败hospitalID={dataView.StationID}");
                return false;
            }
            var patientName = await _patientBasicDataRepository.GetPatientNameAsync(dataView.ChartNo);
            var employee = await _employeelDataRepository.GetEmployeeByEmployeeIDs([dataView.ModifyPersonID,dataView.AddEmployeeID],hospitalID);
            var dept = await _departmentListRepository.GetDepartmentNameByID(dataView.DepartmentListID);
            var times = await _inpatientDataRepository.GetNumberOfAdmissionsByInpatientID(dataView.InpatientID);
            var assessScoreRanges = await _assessScoreRangeRepository.GetAsync();

            var syncPatientScoreView = new SyncPatientScoreView
            {
                PatientScoreMainID = dataView.PatientScoreMainID,
                HospitalCode = hospital.HospitalCode,
                HospitalName = hospital.HospitalName,
                HospitalID = hospital.HospitalID,
                InpatientID = dataView.InpatientID,
                ChartNo = dataView.ChartNo,
                CaseNumber = dataView.CaseNumber,
                PatientName = patientName,
                BedNumber = dataView.BedNumber,
                NumberOfAdmissions = times,
                RecordListID = dataView.RecordListID,
                StationCode = dataView.StationCode,
                StationName = station.StationName,
                DepartmentCode = dataView.DepartmentListID.ToString(),
                DepartmentName = dept?.Department ?? "",
                AssessDate = dataView.AssessDate,
                AssessTime = dataView.AssessTime,
                ScorePoint = dataView.ScorePoint,
                ScoreRangeID = dataView.ScoreRangeID,
                ScoreRangeContent = assessScoreRanges.Find(m => m.RecordListID == dataView.RecordListID && m.AssessScoreRangeID == dataView.ScoreRangeID)?.RangeContent,
                AddEmployeeID = dataView.AddEmployeeID,
                AddEmployeeName = employee.Find(m => m.EmployeeID == dataView.AddEmployeeID)?.EmployeeName,
                ModifyEmployeeID = dataView.ModifyPersonID,
                ModifyEmployeeName = employee.Find(m => m.EmployeeID == dataView.ModifyPersonID)?.EmployeeName,
                DeleteFlag = dataView.DeleteFlag,
            };
            return await _requestApiService.RequestInterconnect( "SyncBackPatientScore", syncPatientScoreView, syncPatientScoreView.InpatientID, syncPatientScoreView.CaseNumber);
        }

    }
}
