﻿using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using NLog;
using System.Data;

namespace InterconnectCore.Service
{
    public class SyncBackPatientAssessService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAssessMainRepository _assessMainRepository;
        private readonly IAssessDetailRepository _assessDetailRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IEmployeelDataRepository _employeelDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IAssessContentRepository _assessContentRepository;
        private readonly IAssessListRepository _assessListRepository;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;

        public SyncBackPatientAssessService(
            IAssessMainRepository assessMainRepository,
            IAssessDetailRepository assessDetailRepository,
            IHospitalListRepository hospitalListRepository,
            IStationListRepository stationListRepository,
            IInpatientDataRepository inpatientDataRepository,
            IPatientBasicDataRepository patientBasicDataRepository,
            IEmployeelDataRepository employeelDataRepository,
            IDepartmentListRepository departmentListRepository,
            IRequestApiService requestApiService,
            IAssessContentRepository assessContentRepository,
            IAssessListRepository assessListRepository,
            ISettingDescriptionRepository settingDescriptionRepository)
        {
            _assessMainRepository = assessMainRepository;
            _assessDetailRepository = assessDetailRepository;
            _hospitalListRepository = hospitalListRepository;
            _stationListRepository = stationListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _employeelDataRepository = employeelDataRepository;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _assessContentRepository = assessContentRepository;
            _assessListRepository = assessListRepository;
            _settingDescriptionRepository = settingDescriptionRepository;
        }
        /// <summary>
        /// AssessList字典
        /// </summary>
        private Dictionary<int, string> AssessListDict = new();
        /// <summary>
        /// 面色对应的配置
        /// </summary>
        private List<int> AssessListID_FaceColor = [];
        /// <summary>
        /// 儿童年龄上限
        /// </summary>
        private const int UPPER_AGE_LIMIT_FOR_CHILDREN = 14;
        /// <summary>
        /// 回传数据
        /// </summary>
        /// <param name="syncData">同步调用信息</param>
        /// <param name="language">语言编码</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        public async Task<bool> SyncDataAsync(SyncDataBackLog syncData, int language, string hospitalID)
        {
            var assessMain = await _assessMainRepository.GetByID(syncData.DataKey);
            if (assessMain == null)
            {
                _logger.Info($"评估记录为空，没有数据可以回传,请求参数为：{ListToJson.ToJson(syncData)}");
                return false;
            }
            var assessDetails = await _assessDetailRepository.GetDetailAsync(syncData.DataKey);
            if (assessDetails.Count == 0)
            {
                _logger.Info($"评估明细为空，没有数据可以回传,请求参数为：{ListToJson.ToJson(syncData)}");
                return false;
            }
            var hospital = await _hospitalListRepository.GetHospital(hospitalID);
            if (hospital == null)
            {
                _logger.Error($"获取医院信息失败hospitalID={hospitalID}");
                return false;
            }
            var station = await _stationListRepository.GetAsync(assessMain.StationID);
            if (station == null)
            {
                _logger.Error($"获取病区信息失败hospitalID={assessMain.StationID}");
                return false;
            }
            var assessContents = await _assessContentRepository.GetByRecordsCode(assessMain.RecordsCode);
            var assessLists = await _assessListRepository.GetAllAsync<AssessListInfo>();
            AssessListDict = assessLists.DistinctBy(m => m.ID).ToDictionary(m => m.ID, n => n.Description);
            var syncVitalSignsView = await CreateSyncView(hospitalID, assessMain, station);
            if (syncVitalSignsView == null)
            {
                return false;
            }
            syncVitalSignsView = SetFieldValueByDetail(syncVitalSignsView, assessDetails, assessContents);
            return await _requestApiService.RequestInterconnect("SyncBackBodySurface", syncVitalSignsView, syncVitalSignsView.InpatientID, syncVitalSignsView.CaseNumber);
        }
        /// <summary>
        /// 创建view实例
        /// </summary>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="assessMain">评估主表记录</param>
        /// <param name="station">病区信息</param>
        /// <returns></returns>
        private async Task<SyncVitalSignsView> CreateSyncView(string hospitalID, PatientAssessMainInfo assessMain, StationListInfo station)
        {
            var inpatient = await _inpatientDataRepository.GetInPatientData(assessMain.InpatientID);
            if (inpatient == null)
            {
                _logger.Error($"获取患者信息失败InpatientID={assessMain.InpatientID},CaseNumber={assessMain.CaseNumber}");
                return null;
            }

            var inDays = (assessMain.AssessDate - inpatient.AdmissionDate).Days;
            var countAdmissionDay = await _settingDescriptionRepository.GetSettingSwitch("CountAdmissionDay", hospitalID);
            var patientName = await _patientBasicDataRepository.GetPatientNameAsync(assessMain.ChartNo);
            var department = assessMain.DepartmentListID.HasValue ? await _departmentListRepository.GetAsync(assessMain.DepartmentListID.Value) : null;
            var employee = await _employeelDataRepository.GetEmployeeByEmployeeIDs([assessMain.ModifyPersonID, assessMain.AddEmployeeID], hospitalID);
            SyncVitalSignsView syncVitalSignsView = new()
            {
                PatientAssessMainID = assessMain.ID,
                HospitalID = hospitalID,
                InpatientID = assessMain.InpatientID,
                ChartNo = assessMain.ChartNo,
                CaseNumber = assessMain.CaseNumber,
                StationID = assessMain.StationID,
                PatientName = patientName,
                BedNumber = assessMain.BedNumber,
                NumberOfAdmissions = inpatient.NumberOfAdmissions,
                StationCode = station.StationCode,
                StationName = station.StationName,
                DepartmentCode = department?.DepartmentCode,
                DepartmentName = department?.Department,
                AssessDate = assessMain.AssessDate,
                AssessTime = assessMain.AssessTime,
                InDay = (countAdmissionDay ? inDays + 1 : inDays).ToString(),
                AddEmployeeID = assessMain.AddEmployeeID,
                AddEmployeeName = employee.Find(m => m.EmployeeID == assessMain.AddEmployeeID)?.EmployeeName,
                ModifyEmployeeID = assessMain.ModifyPersonID,
                ModifyEmployeeName = employee.Find(m => m.EmployeeID == assessMain.ModifyPersonID)?.EmployeeName,
                DeleteFlag = assessMain.DeleteFlag,
                IsChild = inpatient.Age.HasValue && inpatient.Age.Value <= UPPER_AGE_LIMIT_FOR_CHILDREN
            };
            return syncVitalSignsView;
        }

        /// <summary>
        /// 根据评估明细给字段赋值
        /// </summary>
        /// <param name="syncVitalSignsView"></param>
        /// <param name="assessDetails"></param>
        /// <param name="assessContents"></param>
        /// <returns></returns>
        public SyncVitalSignsView SetFieldValueByDetail(SyncVitalSignsView syncVitalSignsView, List<PatientAssessDetailInfo> assessDetails, List<AssessContentInfo> assessContents)
        {
            // 构建字典
            var assessContentDict = assessContents
                .Where(ac => ac.GroupID != null && ac.ControlerType != "L").DistinctBy(ac => ac.AssessListID)
                .ToDictionary(
                    ac => ac.AssessListID,
                    ac => ac
                );

            foreach (var detailItem in assessDetails)
            {
                if (!assessContentDict.TryGetValue(detailItem.AssessListID, out var assessContent))
                {
                    continue;
                }
                switch (detailItem.AssessListID)
                {
                    case 1295:
                        syncVitalSignsView.Temperature = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.TemperatureUnit = assessContent.Unit;
                        break;
                    case 1338:
                        syncVitalSignsView.Pulse = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.PulseUnit = assessContent.Unit;
                        break;
                    case 1943:
                        syncVitalSignsView.HeartRate = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.HeartRateUnit = assessContent.Unit;
                        break;
                    case 1296:
                        syncVitalSignsView.Breath = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.BreathUnit = assessContent.Unit;
                        break;
                    case 1297:
                        syncVitalSignsView.Sbp = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.PresessUnit = assessContent.Unit;
                        break;
                    case 1298:
                        syncVitalSignsView.Dbp = GetAssessValue(assessContent, detailItem);
                        break;
                    case 1299:
                        syncVitalSignsView.PainScore = GetAssessValue(assessContent, detailItem);
                        break;
                    case 1344:
                        syncVitalSignsView.BloodOxygen = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.BloodOxygenUnit = assessContent.Unit;
                        break;
                    case 1954:
                        syncVitalSignsView.Height = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.HeightUnit = assessContent.Unit;
                        break;
                    case 1340:
                        syncVitalSignsView.Weight = GetAssessValue(assessContent, detailItem);
                        syncVitalSignsView.WeightUnit = assessContent.Unit;
                        break;
                    case 1750:
                        syncVitalSignsView.Bmi = GetAssessValue(assessContent, detailItem);
                        break;
                    case 4570:
                        syncVitalSignsView.AllergyHistory = GetAssessValue(assessContent, detailItem);
                        break;
                    //限制卧床
                    case 3054:
                        break;

                    default:
                        if (AssessListID_FaceColor.Contains(detailItem.AssessListID))
                        {
                            if (!string.IsNullOrEmpty(syncVitalSignsView.FaceColor))
                            {
                                syncVitalSignsView.FaceColor += "、" + GetAssessValue(assessContent, detailItem);
                            }
                            syncVitalSignsView.FaceColor = GetAssessValue(assessContent, detailItem);
                        }
                        break;
                }
            }

            return syncVitalSignsView;
        }
        /// <summary>
        /// 获取评估值
        /// </summary>
        /// <param name="assessContent"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        string GetAssessValue(AssessContentInfo assessContent, PatientAssessDetailInfo detail)
        {
            if (assessContent.ControlerType == "R" || assessContent.ControlerType == "C")
            {
                if (detail.AssessValue == "1")
                {
                    return AssessListDict.TryGetValue(assessContent.AssessListID, out var value) ? value : null;
                }
            }
            if (assessContent.ControlerType == "T" || assessContent.ControlerType == "TN" || assessContent.ControlerType == "B")
            {
                return detail.AssessValue;
            }
            return null;
        }
    }
}
