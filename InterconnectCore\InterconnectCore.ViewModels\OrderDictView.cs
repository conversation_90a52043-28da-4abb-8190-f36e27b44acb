﻿namespace Medical.Models
{
    /// <summary>
    /// 医嘱字典视图
    /// </summary>
    public class OrderDictView
    {
        /// <summary>
        /// 医嘱ID
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 医嘱ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        ///医嘱类别
        ///</summary>
        public string OrderType { get; set; }
        /// <summary>
        ///类别名称
        ///</summary>
        public string TypeName { get; set; }
        /// <summary>
        ///医嘱代码
        ///</summary>
        public string OrderCode { get; set; }
        /// <summary>
        ///医嘱名称
        ///</summary>
        public string OrderName { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>     
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime ModifyDate { get; set; }
    }
}