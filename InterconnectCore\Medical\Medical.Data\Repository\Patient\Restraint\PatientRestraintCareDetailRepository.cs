﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRestraintCareDetailRepository : IPatientRestraintCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRestraintCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PatientRestraintCareDetailInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientRestraintCareDetailInfos.Where(t => t.PatientRestraintCareDetailID == id && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        public async Task<List<PatientRestraintCareDetailInfo>> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientRestraintCareDetailInfos.Where(t => t.PatientRestraintCareMainID == careMainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主表记录ID集合获取明细数据
        /// </summary>
        /// <param name="careMainIDs">主表ID集合</param>
        /// <returns></returns>
        public async Task<List<SpecialListDetailView>> GetViewsByCareMainIDs(params string[] careMainIDs)
        {
            return await _medicalDbContext.PatientRestraintCareDetailInfos.Where(t => careMainIDs.Contains(t.PatientRestraintCareMainID)
            && t.DeleteFlag != "*")
                .Select(m => new SpecialListDetailView
                {
                    MainID = m.PatientRestraintCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue,
                    AssessListGroupID = m.AssessListGroupID,
                }).ToListAsync();
        }
    }
}