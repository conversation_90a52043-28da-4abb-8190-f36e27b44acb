﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 自定义报表表
        /// </summary>
        public DbSet<StatisticsReportInfo> StatisticsReports { get; set; }
        /// <summary>
        /// 自定义报表明细表
        /// </summary>
        public DbSet<StatisticsReportDetailInfo> StatisticsReportDetails { get; set; }
        /// <summary>
        /// 报表链接表
        /// </summary>
        public DbSet<StatisticsReportLinkInfo> StatisticsReportLinks { get; set; }
        /// <summary>
        /// 角色报表配置表
        /// </summary>
        public DbSet<StatisticsReportRoleInfo> StatisticsReportRoles { get; set; }
        /// <summary>
        /// 报表维度配置表
        /// </summary>
        public DbSet<StatisticsReportDimensionInfo> StatisticsReportDimensions { get; set; }

        /// <summary>
        /// 护理目标结局字典
        /// </summary>
        public DbSet<ProblemClassdictInfo> ProblemClassdictInfos { get; set; }

        /// <summary>
        /// 风险统计临时表
        /// </summary>
        public DbSet<StatisticsPressInfo> StatisticsPressInfos { get; set; }

        /// <summary>
        /// 指標統計
        /// </summary>
        public DbSet<IndicatorFactorInfo> IndicatorFactorInfos { get; set; }

        /// <summary>
        /// 交班统计
        /// </summary>
        public DbSet<StatisticsPatientInfo> StatisticsPatientInfos { get; set; }

        /// <summary>
        /// 统计下载
        /// </summary>
        public DbSet<StasticsDownloadInfo> StasticsDownloadInfo { get; set; }

        /// <summary>
        /// 慢病延续护理病人主档
        /// </summary>
        public DbSet<ContinuousCarePatientMainInfo> ContinuousCarePatientMainInfos { get; set; }

        /// <summary>
        /// 慢病延续护理细项
        /// </summary>
        public DbSet<ContinuousCarePatientDetailInfo> ContinuousCarePatientDetailInfos { get; set; }

        /// <summary>
        /// 诊断问题统计
        /// </summary>
        public DbSet<StatisticsDiagnosisInfo> StatisticsDiagnosisInfos { get; set; }
        /// <summary>
        /// 用户统计
        /// </summary>
        public DbSet<InpatientNurseOperationStatisticsInfo> CountWardNumberInfos { get; set; }
        /// <summary>
        /// 住院患者敏感统计数据表日志
        /// </summary>
        public DbSet<StatisticsInpatientDataLogInfo> StatisticsInpatientDataLogInfos { get; set; }

    }
}