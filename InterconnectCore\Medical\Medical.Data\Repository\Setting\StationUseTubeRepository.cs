﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class StationUseTubeRepository : IStationUseTubeRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public StationUseTubeRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取病区对导管的最大主键
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            var data = await _medicalDbContext.StationUseTubes.Select(m => m.StationUseTubeID).MaxAsync();
            return data;
        }

        /// <summary>
        /// 获取病区对导管的最大索sort值
        /// </summary>
        /// <returns></returns>
        public async Task<short> GetMaxSort()
        {
            var data = (await GetCacheAsync()) as List<StationUseTubeInfo>;
            return data.Max(m => m.Sort);
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<StationUseTubeInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.StationUseTubes.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.StationUseTube.ToString();
        }

        public async Task<StationUseTubeInfo> GetStationUseTubeByID(int stationUseTubeID)
        {
            var data = (await GetCacheAsync()) as List<StationUseTubeInfo>;
            return data.FirstOrDefault(m => m.StationUseTubeID == stationUseTubeID);
        }
    }
}