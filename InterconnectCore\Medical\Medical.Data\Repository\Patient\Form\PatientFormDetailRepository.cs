﻿using Medical.Data.Context;
using Medical.Data.Interface.Patient.Form;
using Medical.Models.Patient;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.Patient.Form
{
    public class PatientFormDetailRepository : IPatientFormDetailRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientFormDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据评估记录ID获取评估明细数据 组装评估模版使用
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<AssessContentValue>> GetViewsByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFormDetailInfos
                .Where(m => m.PatientFormRecordID == recordID && m.DeleteFlag != "*")
                .Select(m => new AssessContentValue()
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }

        /// <summary>
        /// 根据评估记录ID获取评估明细数据
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientFormDetailInfo>> GetByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFormDetailInfos
                .Where(m => m.PatientFormRecordID == recordID && m.DeleteFlag != "*")
                .ToListAsync();
        }
    }
}
