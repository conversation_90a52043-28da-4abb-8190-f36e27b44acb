﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class MappingRepository : IMappingRepository
    {
        private ExtendDbContext _dbContext = null;

        public MappingRepository(ExtendDbContext db)
        {
            _dbContext = db;
        }

        public async Task<MappingInfo> GetAsync(string system, string item)
        {
            return await _dbContext.Mappings
                .Where(m => m.System == system && m.Item == item).SingleOrDefaultAsync();
        }
    }
}