﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ButtonListRepository : IButtonListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public ButtonListRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<ButtonView>> GetButtonViewAsync()
        {
            var datas = await GetAsync();
            return datas.Select(m => new ButtonView()
            {
                ButtonListID = m.ButtonListID,
                ButtonName = m.ButtonName,
                ButtonClass = m.ButtonClass
            }).ToList();
        }
        public async Task<List<ButtonListInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<ButtonListInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ButtonListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.ButtonListInfos.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.ButtonList.GetKey(_sessionCommonServer);
        }
    }
}