﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class FilesDBContext : DbContext
    {
        public FilesDBContext(DbContextOptions<FilesDBContext> options)
           : base(options)
        { }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<OrderExecLogInfo>().<PERSON><PERSON><PERSON>(t => (new { t.InpatientID, t.OrderNO, t.OrderSubNO, t.SourceID, t.SourceType }));
            builder.Entity<DocumentChangeLogInfo>().<PERSON><PERSON><PERSON>(t => (new { t.ChangeLogID }));
            builder.Entity<DocumentChangeNursingrecordLog>().<PERSON><PERSON><PERSON>(t => (new { t.ChangeLogID }));
            builder.Entity<DocumentChangeProblemLogInfo>().<PERSON><PERSON><PERSON>(t => (new { t.ChangeLogID }));
            base.OnModelCreating(builder);
        }
        public DbSet<CCCDataInfo> CCCDataInfos { get; set; }

        public DbSet<InpatientVitalSignInfo> InpatientVitalSignInfos { get; set; }
    }
}
