﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientArteriovenousFistulaRecordRepository : IPatientArteriovenousFistulaRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientArteriovenousFistulaRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据主记录ID获取主记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientArteriovenousFistulaRecordInfo> GetRecordInfoByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaRecordInfos.Where(m => m.PatientArteriovenousFistulaRecordID == recordID && m.DeleteFlag != "*")
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取主记录ByInpatientID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<ArteriovenousFistulaRecordView>> GetRecordInfosByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new ArteriovenousFistulaRecordView
                {
                    PatientArteriovenousFistulaRecordID = m.PatientArteriovenousFistulaRecordID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    UserID = m.AddEmployeeID,
                    BodyPartID = m.BodyPartID,
                    Source = m.Source,
                    BodyPartName = m.BodyPartName,
                    BodyPartSort = m.BodyPartSort
                }).ToListAsync();
        }
        /// <summary>
        /// 获取主记录中的身体部位描述(recordID,BodyPartName)
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<ArteriovenousFistulaRecordView>> GetBodyShowNameOfRecordsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new ArteriovenousFistulaRecordView
                {
                    PatientArteriovenousFistulaRecordID = m.PatientArteriovenousFistulaRecordID,
                    BodyPartName = m.BodyPartName,
                }).ToListAsync();
        }
    }
}
