﻿using Medical.Data.Context;
using Medical.Data.Interface.Patient.Form;
using Medical.Models.Patient;
using Medical.ViewModels;
using Medical.ViewModels.View.Patient.Form;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.Patient.Form
{
    public class PatientFormRecordRepository : IPatientFormRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientFormRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据inpatientID 和时间获取数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="date">评估时间</param>
        /// <returns></returns>
        public async Task<List<PatientFormTableView>> GetViewByInpatientID(string inpatientID, DateTime? date = null)
        {
            IQueryable<PatientFormRecordInfo> data = null;
            if (date.HasValue)
            {
                data = _medicalDbContext.PatientFormRecordInfos
                    .Where(m => m.InPatientID == inpatientID && m.AssessDateTime.Date == date.Value && m.DeleteFlag != "*");
            }
            else
            {
                data = _medicalDbContext.PatientFormRecordInfos
                    .Where(m => m.InPatientID == inpatientID && m.DeleteFlag != "*");
            }

            return await data.Select(m => new PatientFormTableView()
            {
                PatientFormRecordID = m.PatientFormRecordID,
                PatientFormType = m.PatientFormType,
                FromRecordsCode = m.FromRecordsCode,
                StationID = m.StationID,
                DepartmentListID = m.DepartmentListID,
                AssessDate = m.AssessDateTime.Date,
                AssessTime = m.AssessDateTime.TimeOfDay,
                RecordContent = m.RecordContent,
                UserID = m.ModifyPersonID,
                BringToShift = m.BringToShift,
                BringToNursingRecord = m.BringToNursingRecord,
                InformPhysician = m.NotifyFlag
            }).ToListAsync();
        }

        /// <summary>
        /// 根据RecordID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientFormRecordInfo> GetByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFormRecordInfos.Where(m => m.PatientFormRecordID == recordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据时间获取交班内容
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientFormShiftView>> GetShiftViewsByDate(string inpatientID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientFormRecordInfos.Where(m => m.InPatientID == inpatientID && m.AssessDateTime >= startDate && m.AssessDateTime <= endDate
            && m.DeleteFlag != "*" && m.BringToShift == true)
                .Select(m => new PatientFormShiftView
                {
                    PatientFormType = m.PatientFormType,
                    SignificantContent = m.SignificantContent
                }).ToListAsync();
        }
    }
}
