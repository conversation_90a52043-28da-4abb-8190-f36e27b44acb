﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InpatientVitalSignRepository : IInpatientVitalSignRepository
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly FilesDBContext _filesDBContext = null;

        public InpatientVitalSignRepository(FilesDBContext filesDBContext)
        {
            _filesDBContext = filesDBContext;
        }

        public void Delete(InpatientVitalSignInfo vitalSign)
        {
            try
            {
                vitalSign.DeleteFlag = "*";
                vitalSign.DataPumpFlag = "";
                _filesDBContext.Update(vitalSign);
            }
            catch (Exception ex)
            {
                _logger.Error("Delete异常InpatientVitalSign" + ex.ToString());
            }
        }

        public async Task Insert(InpatientVitalSignInfo vitalSign)
        {
            try
            {
                await _filesDBContext.AddAsync(vitalSign);
            }
            catch (Exception ex)
            {
                _logger.Error("Insert异常InpatientVitalSign" + ex.ToString());
            }
        }

        public void Update(InpatientVitalSignInfo vitalSign)
        {
            try
            {
                _filesDBContext.Update(vitalSign);
            }
            catch (Exception ex)
            {
                _logger.Error("Update异常InpatientVitalSign" + ex.ToString());
            }
        }

        public async Task<bool> Save(string saveErrorMessage)
        {
            try
            {
                return await _filesDBContext.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error(saveErrorMessage + ex.ToString());
                return false;
            }
        }

        public async Task<List<InpatientVitalSignInfo>> GetSourceIDs(List<string> list)
        {
            return await _filesDBContext.InpatientVitalSignInfos.Where(m => list.Contains(m.SourceID)).ToListAsync();
        }
    }
}