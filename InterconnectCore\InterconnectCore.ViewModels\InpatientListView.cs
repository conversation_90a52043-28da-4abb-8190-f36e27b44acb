﻿namespace InterconnectCore.ViewModels
{
    /// <summary>
    /// 入院记录
    /// </summary>   
    public class InPatientDataView
    {
        /// <summary>
        ///住院号码
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号码
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///住院次数
        ///</summary>
        public int? NumberOfAdmissions { get; set; }
        /// <summary>
        ///科别
        ///</summary>
        public string Department { get; set; }
        /// <summary>
        ///科别代码
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///病区(护理单元)

        ///</summary>
        public string StationName { get; set; }
        /// <summary>
        ///病区(护理单元)代码
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///床位号码
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///床位代码
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///ICU注记
        ///</summary>
        public string ICUFlag { get; set; }
        /// <summary>
        ///诊断码
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断
        ///</summary>
        public string Diagnosis { get; set; }
        /// <summary>
        ///主治医师工号
        ///</summary>
        public string AttendingPhysicianID { get; set; }
        /// <summary>
        ///护理级别
        ///</summary>
        public string NursingLevel { get; set; }
        /// <summary>
        ///护理等级代码
        ///</summary>
        public string NursingLevelCode { get; set; }
        /// <summary>
        ///费用类型
        ///</summary>
        public string BillingPattern { get; set; }
        /// <summary>
        ///入院日期
        ///</summary>
        public DateTime AdmissionDateTime { get; set; }
        /// <summary>
        ///出院日期
        ///</summary>
        public DateTime? DischargeDateTime { get; set; }

        /// <summary>
        /// 患者在院状态。
        /// 30在科\入科；
        /// 40预出院；50出科\不在科；60实际出院；
        /// 70出院未结算；80出院已结算；90出院召回；
        /// （30、40在患者清单显示）
        /// </summary>
        public int InHospitalStatus { get; set; }
        /// <summary>
        /// 年龄 int
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 年龄明细
        /// </summary>
        public string AgeDetail { get; set; }

        //public string AgeDetail { get; set; }
        /// <summary>
        ///病人姓名
        ///</summary>
        public string PatientName { get; set; }
        /// <summary>
        ///性别

        ///</summary>
        public string Gender { get; set; }
        /// <summary>
        ///出生日期

        ///</summary>
        public DateTime? DateOfBirth { get; set; }
        /// <summary>
        ///出生时间
        ///</summary>
        public TimeSpan? TimeOfBirth { get; set; }
        /// <summary>
        ///血型
        ///</summary>
        public string BloodType { get; set; }
        /// <summary>
        ///籍贯
        ///</summary>
        public string NativePlace { get; set; }
        /// <summary>
        ///籍贯码
        ///</summary>
        public string NativePlaceCode { get; set; }
        /// <summary>
        ///病人主诉
        ///</summary>
        public string ChiefComplaint { get; set; }
        /// <summary>
        /// 入科时间
        /// </summary>
        public DateTime? EnterWardTime { get; set; }
        /// <summary>
        /// 表示属于哪个科室的病人（原因:某病区可能存在妇科病人和产科病人，用此字段区分）
        /// </summary>
        public string SpecialRemark { get; set; }
        /// <summary>
        /// 床位标识
        /// </summary>
        public string BedRemark { get; set; }
        /// <summary>
        /// 是否为虚床 
        /// </summary>
        public bool IsVirtualBed { get; set; }
        /// <summary>
        ///死亡时间
        ///</summary>
        public DateTime? DeathTime { get; set; }
        /// <summary>
        ///胎儿序号
        ///</summary>
        public int BabySN { get; set; }
        /// <summary>
        /// 体重(克)
        ///</summary>
        public int? Weight { get; set; }
        /// <summary>
        /// 身高
        ///</summary>
        public Decimal? Height { get; set; }
        /// <summary>
        /// 父母住院流水号
        ///</summary>
        public string ParentCaseNumber { get; set; }
    }
}