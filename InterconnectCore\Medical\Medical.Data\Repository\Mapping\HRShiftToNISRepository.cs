﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HRShiftToNISRepository : IHRShiftToNISRepository
    {
        private MedicalDbContext _dbContext = null;

        public HRShiftToNISRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        public async Task<List<HRShiftToNISInfo>> GetAllHRShiftToNISAsync()
        {
            return await _dbContext.HRShiftToNISInfos.Where(m => m.DeleteFlag != "*").ToListAsync();

        }
    }
}
