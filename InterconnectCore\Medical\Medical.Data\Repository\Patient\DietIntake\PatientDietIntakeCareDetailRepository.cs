﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDietIntakeCareDetailRepository : IPatientDietIntakeCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDietIntakeCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据主记录获取照护明细
        /// </summary>
        /// <param name="mainID">分娩照护主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientDietIntakeCareDetailInfo>> GetByMainID(string mainID)
        {
            return await _medicalDbContext.PatientDietIntakeCareDetailInfos.Where(t => t.PatientDietIntakeCareMainID == mainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据患者住院ID获取明细
        /// </summary>
        /// <param name="inpaitentID">患者住院ID</param>
        /// <returns></returns>
        public async Task<List<PatientDietIntakeCareDetailInfo>> GetByInpatientID(string inpaitentID)
        {
            return await _medicalDbContext.PatientDietIntakeCareDetailInfos.Where(t => t.InpatientID == inpaitentID && t.DeleteFlag != "*").ToListAsync();
        }

    }
}
