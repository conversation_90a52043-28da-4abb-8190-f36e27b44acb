﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ReportToInterventionMainRepository : IReportToInterventionMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public ReportToInterventionMainRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ReportToInterventionMain>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.ReportToInterventionMains.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public string GetCacheType()
        {
            return CacheType.ReportToInterventionMain.ToString();
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        public async Task<ReportToInterventionMain> GetByID(int ID)
        {
            var datas = await GetCacheAsync() as List<ReportToInterventionMain>;
            return datas.Where(t => t.ID == ID).FirstOrDefault();
        }



    }
}
