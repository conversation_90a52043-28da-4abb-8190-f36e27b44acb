﻿using System;

namespace Medical.Common
{
    public class AgeCalculat
    {
        /// <summary>
        /// 获取年龄
        /// </summary>
        /// <param name="birthdate">生日</param>
        /// <param name="calculatDate">计算日期</param>
        /// <returns></returns>
        public static int GetAge(DateTime birthDate, DateTime calculatDate)
        {
            return GetBirthYear(birthDate, calculatDate);
        }

        /// <summary>
        /// 获取年龄加月
        /// </summary>
        /// <param name="birthdate">生日</param>
        /// <param name="calculatDate">计算日期</param>
        /// <returns></returns>
        public static string GetAgeMonth(DateTime birthDate, DateTime calculatDate)
        {
            return GetAge(birthDate, calculatDate).ToString() + "-" + (GetBirthMonth(birthDate, calculatDate) % 12).ToString();
        }

        /// <summary>
        /// 获取完整年龄
        /// </summary>
        /// <param name="birthdate">生日</param>
        /// <param name="calculatDate">计算日期</param>
        /// <returns></returns>
        public static string GetFullAge(DateTime birthDate, DateTime calculatDate)
        {
            //获取总月份
            int totalMonth = GetBirthMonth(birthDate, calculatDate);
            return GetAgeMonth(birthDate, calculatDate) + "-" + (calculatDate - birthDate.AddMonths(totalMonth)).Days.ToString();
        }

        /// <summary>
        /// 获取出生总天数
        /// </summary>
        /// <returns></returns>
        public static int GetBirthDay(DateTime birthdate, DateTime calculatDate)
        {
            TimeSpan birthDay = calculatDate - birthdate;
            return birthDay.Days;
        }

        /// <summary>
        ///获取出生几个月
        /// </summary>
        /// <param name="birthdate"></param>
        /// <param name="calculatDate"></param>
        /// <returns></returns>
        public static int GetBirthMonth(DateTime birthDate, DateTime calculatDate)
        {
            int birthMonth = calculatDate.Year * 12 + calculatDate.Month - birthDate.Year * 12 - birthDate.Month;

            return birthMonth;
        }

        /// <summary>
        /// 获取出生几年
        /// </summary>
        public static int GetBirthYear(DateTime birthDate, DateTime calculatDate)
        {
            int birhYear = GetBirthMonth(birthDate, calculatDate) / 12;
            return birhYear;
        }
        public static int GetMonths(DateTime birthDate, DateTime calculatDate)
        {
            return GetBirthMonth(birthDate, calculatDate) % 12;
        }

        public static int GetAgeByDays(DateTime birthDate, DateTime calculaDate)
        {
            int age = calculaDate.Year - birthDate.Year;

            if (birthDate > calculaDate.AddYears(-age))
                age--;
            return age;
        }

        /// <summary>
        /// 根据指定日期和出生日期获取string年龄
        /// </summary>
        /// <param name="dt_birth_date"></param>
        /// <returns></returns>
        public static string GetAgeDetail(DateTime dateTimeOfBirth, DateTime? dateTime)
        {
            string strAge = string.Empty; // 年龄的字符串表示
            int intYear = 0; // 岁
            int intMonth = 0; // 月
            int intDay = 0; // 天
            var now = DateTime.Now;

            if (dateTime.HasValue)
            {
                now = dateTime.Value;
            }

            if (dateTimeOfBirth > now)
            {
                return "";
            }

            // 计算天数
            intDay = now.Day - dateTimeOfBirth.Day;
            if (intDay < 0)
            {
                now = now.AddMonths(-1);
                intDay = intDay + DateTime.DaysInMonth(now.Year, now.Month);
            }
            // 计算月数
            intMonth = now.Month - dateTimeOfBirth.Month;
            if (intMonth < 0)
            {
                intMonth += 12;
                now = now.AddYears(-1);
            }
            // 计算年数
            intYear = now.Year - dateTimeOfBirth.Year;

            //18岁以上显示X岁
            if (intYear >= 18)
            {
                strAge = intYear.ToString() + "岁";
            }
            // 1至18岁显示X岁X月
            if (intMonth >= 0 && intYear >= 1 && intYear < 18)
            {
                strAge = intYear.ToString() + "岁" + intMonth.ToString() + "月";
            }
            //1岁内显示X月X天
            if (intDay >= 0 && intMonth >= 0 && intYear < 1)
            {
                if (intMonth > 0)
                {
                    strAge = intMonth.ToString() + "月" + intDay.ToString() + "天";
                }
                else
                {
                    strAge = intDay.ToString() + "天";
                }
            }
            return strAge;
        }

    }
}
