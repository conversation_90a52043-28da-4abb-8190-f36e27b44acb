﻿namespace InterconnectCore.Common.SessionCommon
{
    public class Session
    {
        /// <summary>
        /// 授权口令
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// 员工流水号
        /// </summary>
        public int EmployeeID { get; set; }

        /// <summary>
        /// 用户工号
        /// </summary>
        public string UserID { get; set; }

        /// <summary>
        /// 登录时间
        /// </summary>
        public DateTime LoginTime { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 命令码
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 病区序号
        /// </summary>
        public int StationID { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 人员角色序号
        /// </summary>
        public List<int> Roles { get; set; }

        /// <summary>
        /// 客户端类别 1:PC（web） 2:PC/PDA/手机（android端） 3:iPad（web）
        /// </summary>
        public string ClientType { get; set; }

        /// <summary>
        /// 电子签名用户序号
        /// </summary>
        public string CAUserID { get; set; }

        /// <summary>
        /// 自动电子签名token
        /// </summary>
        public string SignToken { get; set; }

        /// <summary>
        /// 人事部门ID
        /// </summary>
        public string HRdepartmentID { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 解构方法
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        public void Deconstruct(out string hospitalID, out int language)
        {
            hospitalID = HospitalID;
            language = Language;
        }
    }
}