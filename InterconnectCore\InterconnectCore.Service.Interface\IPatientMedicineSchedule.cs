﻿using InterconnectCore.ViewModels;

namespace InterconnectCore.Service.Interface
{
    public interface IPatientMedicineSchedule
    {
        /// <summary>
        /// 同步单患者用药信息
        /// </summary>
        /// <param name="patientMedicineScheduleViews"></param>
        /// <returns></returns>
        Task<bool> SyncSinglePatientMedicineSchedule(List<PatientMedicineScheduleView> patientMedicineScheduleViews);
    }
}