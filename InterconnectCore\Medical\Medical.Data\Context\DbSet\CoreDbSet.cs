﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 措施主档数据集
        /// </summary>
        public DbSet<NursingInterventionMainInfo> NursingInterventionMain { get; set; }

        /// <summary>
        /// 措施名细数据集
        /// </summary>
        public DbSet<NursingInterventionDetailInfo> NursingInterventionDetail { get; set; }

        /// <summary>
        /// 评估主记录数据集
        /// </summary>
        public DbSet<PatientAssessMainInfo> AssessMains { get; set; }

        /// <summary>
        /// 评估明细记录数据集
        /// </summary>
        public DbSet<PatientAssessDetailInfo> AssessDetails { get; set; }

        /// <summary>
        /// 评估项目数据集
        /// </summary>
        public DbSet<AssessContentInfo> AssessContents { get; set; }

        /// 护理目标数据集
        /// </summary>
        public DbSet<NursingGoalInfo> NursingGoals { get; set; }

        /// <summary>
        /// 护理问题数据集
        /// </summary>
        public DbSet<NursingProblemInfo> NursingProblems { get; set; }

        /// <summary>
        /// 相关因素数据集
        /// </summary>
        public DbSet<RelatedFactorInfo> RelatedFactors { get; set; }

        /// <summary>
        /// 分类数据集
        /// </summary>
        public DbSet<ProblemCategoryInfo> ProblemCategories { get; set; }

        /// <summary>
        /// 护理实际目标数据集
        /// </summary>
        public DbSet<NursingOutComeInfo> NursingOutComes { get; set; }

        /// <summary>
        /// 医嘱数据集
        /// </summary>
        public DbSet<PhysicianOrderInfo> PhysicianOrders { get; set; }

        /// <summary>
        /// 临床数据据集
        /// </summary>
        public DbSet<ClinicDataInfo> ClinicDatas { get; set; }

        //注释原因： DataSwitchCenter 表不再使用
        /// <summary>
        /// 临床数据据集
        /// </summary>
        //public DbSet<DataSwitchCenterInfo> DataSwitchCenters { get; set; }

        //操作人员： 梁宝华 2020-03-17
        /// <summary>
        /// 临床数据据集
        /// </summary>
        //public DbSet<RecordTableDictInfo> RecordTableDicts { get; set; }

        /// <summary>
        /// 临床数据据集
        /// </summary>
        public DbSet<SwitchRecordFormatInfo> SwitchRecordFormats { get; set; }

        /// <summary>
        /// 获取班次统计数据
        /// </summary>
        public DbSet<NurseShiftDataInfo> NurseShiftDatas { get; set; }

        /// <summary>
        /// 获取业务表数据是否变化记录，数据抽档使用D
        /// </summary>
        public DbSet<DataTableEditListInfo> DataTableEditListInfos { get; set; }

        /// <summary>
        /// 获取业务表数据是否变化记录，数据抽档使用D
        /// </summary>
        public DbSet<DataTableEditListBakInfo> DataTableEditListBakInfos { get; set; }

        public DbSet<HelpListInfo> HelpListInfos { get; set; }

        /// <summary>
        ///  自动更新表
        /// </summary>
        public DbSet<MobileVersionInfo> MobileVersionInfos { get; set; }

        /// <summary>
        /// 盘点字典表
        /// </summary>
        public DbSet<StockSettingInfo> StockSettingInfos { get; set; }

        /// <summary>
        /// 盘点主表
        /// </summary>
        public DbSet<ShiftStockMainInfo> ShiftStockMainInfos { get; set; }

        /// <summary>
        /// 盘点明细表
        /// </summary>
        public DbSet<ShiftStockDetailInfo> ShiftStockDetailInfos { get; set; }
    }
}