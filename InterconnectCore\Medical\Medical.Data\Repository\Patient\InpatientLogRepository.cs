﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InpatientLogRepository : IInpatientLogRepository
    {
        private MedicalDbContext _dbContext = null;

        public InpatientLogRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<DateTime?> GetPostOPDateTimeByInpatientIDAsync(string inpatientID, int stationID, DateTime startDateTime, DateTime endDateTime)
        {
            var result = await _dbContext.InpatientLogInfos.Where(m =>
              m.InpatientID == inpatientID && m.StationID == stationID && m.LogDateTime >= startDateTime
                                  && m.LogDateTime < endDateTime && m.DeleteFlag != "*").Select(m => new { m.LogCode, m.LogDateTime }).ToListAsync();
            result = result.Where(m => m.LogCode != "PreOP" && !m.LogCode.Contains("bed")).ToList();
            if (result.Count == 0)
            {
                return null;
            }

            return result.OrderByDescending(m => m.LogDateTime).First().LogDateTime;
        }

        public async Task<List<InpatientLogInfo>> GetByInpatientIDAsync(string inpatientID)
        {
            return await _dbContext.InpatientLogInfos.Where(m =>
             m.InpatientID == inpatientID &&
             m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InpatientLogInfo>> GetByCaseNumberAsync(string caseNumber)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.CaseNumber == caseNumber
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<InpatientLogInfo> GetByCaseNumberAndStationID(string caseNumber, int stationID)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.CaseNumber == caseNumber && m.StationID == stationID
            && m.DeleteFlag != "*").OrderByDescending(n => n.ModifyDate).FirstOrDefaultAsync();
        }

        public async Task<List<InpatientLogInfo>> GetByCaseNumbersAsync(string[] caseNumbers)
        {
            var inpatientLogList = new List<InpatientLogInfo>();
            for (int i = 0; i < caseNumbers.Length; i++)
            {
                var tempList = await _dbContext.InpatientLogInfos.Where(m => m.CaseNumber == caseNumbers[i] && m.DeleteFlag != "*").ToListAsync();
                inpatientLogList = inpatientLogList.Union(tempList).ToList();
            }
            return inpatientLogList;
        }

        public async Task<List<InpatientLogInfo>> GetOperatorLogAsync(string caseNumber, string opCode)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.CaseNumber == caseNumber
            && m.OPCode == opCode
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InpatientLogInfo>> GetByStationID(int stationID)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<InpatientLogInfo> GetLastData()
        {
            return await _dbContext.InpatientLogInfos.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 通过病区和LogDateTime获取数据
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<InpatientLogInfo>> GetByStationAndLogDateTime(int stationID, DateTime startTime, DateTime endTime)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.LogDateTime > startTime && m.LogDateTime < endTime && m.DeleteFlag != "*" && (m.LogCode == "Discharge" || m.LogCode == "TransOut")).OrderBy(m => m.LogDateTime).ToListAsync();
        }


        /// <summary>
        /// 根据病人在院ID集合 日志代码获取异动记录
        /// </summary>
        /// <param name="inpatientIDs">病人在院ID集合</param>
        /// <param name="logCode">日志代码</param>
        /// <returns></returns>
        public List<InpatientLogInfo> Get(List<string> inpatientIDs, string logCode)
        {
            var list = (from inpatientID in inpatientIDs
                        join log in _dbContext.InpatientLogInfos
                        on inpatientID equals log.InpatientID
                        where log.DeleteFlag != "*" && log.LogCode == logCode
                        select log).ToList();
            return list;
        }

        public async Task<List<InpatientLogInfo>> GetByTime(int stationID, DateTime startTime, DateTime endTime)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID
            && m.LogDateTime >= startTime
            && m.LogDateTime <= endTime
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<InpatientLogInfo> GetFlagDataAsync(string flagCode)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.LogCode == flagCode).SingleOrDefaultAsync();
        }

        public async Task<List<InpatientLogInfo>> GetAsync(DateTime startTime)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.LogDateTime >= startTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InpatientLogInfo>> GetNeedMakeUpAsync(DateTime startTime)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.LogDateTime > startTime && m.InpatientID == "-1" && m.CaseNumber != "-1" && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<InpatientLogInfo>> GetByStationAndLogDateTimeAndFlag(int stationID, DateTime startTime, DateTime endTime, string flag)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.LogDateTime >= startTime && m.LogDateTime <= endTime && m.DeleteFlag != "*" && m.LogCode == flag).OrderBy(m => m.LogDateTime).ToListAsync();
        }

        public async Task<List<InpatientQuery>> GetByStationAndLogDateTimeAndFlag(int stationID, DateTime date, string flag)
        {
            var query = await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.LogDateTime.Date == date && m.LogCode == flag)
                .Join(_dbContext.PatientBasicDatas, u => u.PatientID, d => d.PatientID, (u, d) => new InpatientQuery
                {
                    InpatientID = u.InpatientID,
                    PatientID = u.PatientID,
                    CaseNumber = u.CaseNumber,
                    ChartNo = u.ChartNo,
                    StationID = u.StationID,
                    BedID = u.BedID,
                    BedNumber = u.BedNumber,
                    PatientName = d.PatientName,
                }).ToListAsync();

            return query;
        }
        /// <summary>
        /// 根据InpatientID集合获取记录
        /// </summary>
        /// <param name="inpatientIDs">InpatientID集合</param>
        /// <returns></returns>
        public async Task<List<InpatientLogInfo>> Get(List<string> inpatientIDs)
        {
            return await _dbContext.InpatientLogInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<InpatientLogInfo> GetInpatientLogInfoByOPCode(string opCode, string LogCode)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.OPCode == opCode && m.LogCode == LogCode && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<InpatientLogInfo>> GetLogByInpatientIDAsync(string inpatientID, string logCode)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.InpatientID == inpatientID && m.LogCode == logCode && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<bool> GetLogByInpatientIDExistAsync(string inpatientID, string logCode, DateTime occurDateTime, string logCongent)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.InpatientID == inpatientID && m.LogCode == logCode && m.DeleteFlag != "*" && m.LogDateTime == occurDateTime && m.LogContent == logCongent).CountAsync() > 0;
        }

        public async Task<List<InpatientLogInfo>> GetByStationIDAndInDate(int stationID, DateTime date)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.LogDateTime.Date == date && m.DeleteFlag != "*" && (m.LogCode == "Admission" || m.LogCode == "TransIn")).ToListAsync();
        }

        public async Task<List<InpatientLogInfo>> GetByStationIDAndOutDate(int stationID, DateTime date)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.StationID == stationID && m.LogDateTime.Date == date && m.DeleteFlag != "*" && (m.LogCode == "Discharge" || m.LogCode == "TransOut")).ToListAsync();
        }

        public async Task<List<SimpleInpatientLog>> GetSimpleByCaseNumberAsync(string caseNumber)
        {
            return await _dbContext.InpatientLogInfos.Where(m => m.CaseNumber == caseNumber
            && m.DeleteFlag != "*").Select(m => new SimpleInpatientLog
            {
                InpatientID = m.InpatientID,
                StationID = m.StationID,
                LogCode = m.LogCode,
                LogDateTime = m.LogDateTime
            }).ToListAsync();
        }
    }
}
