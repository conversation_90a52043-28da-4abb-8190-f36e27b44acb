﻿namespace InterconnectCore.ViewModels
{
    public class PatientDiagnosisItemView
    {
        /// <summary>
        /// 病人住院序号
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 病人序号
        /// </summary>
        public string PatientID { get; set; }
        /// <summary>
        /// 单位序号
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 科别序号
        /// </summary>
        public int DepartmentListID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// HIS诊断码
        /// </summary>
        public string DiagnosisCode { get; set; }
        /// <summary>
        /// 诊断名称
        /// </summary>
        public string DiagnosisName { get; set; }
        /// <summary>
        /// ICD诊断码
        /// </summary>
        public string ICDCode { get; set; }
        /// <summary>
        /// ICD版本，参见Setting
        /// </summary>
        public string ICDVersion { get; set; }
        /// <summary>
        /// 显示排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 诊断医生
        /// </summary>
        public string DoctorNo { get; set; }
        /// <summary>
        /// 诊断时间
        /// </summary>
        public DateTime? DiagnosisTime { get; set; }
        /// <summary>
        /// 是否主诊断
        /// </summary>
        public string MainFlag { get; set; }
        /// <summary>
        /// 出院时间
        /// </summary>
        public DateTime? OutTime { get; set; }
        /// <summary>
        /// 诊断类别 A入院/P住院中/D出院
        /// </summary>
        public string DiagnosisType { get; set; }
        /// <summary>
        /// 诊断类别说明
        /// </summary>
        public string DiagnosisHISNote { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDate { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>   
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>  
        public string DeleteFlag { get; set; }
    }
}