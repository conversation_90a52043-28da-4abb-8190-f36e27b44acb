﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.Services.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Setting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;

namespace InterconnectCore.Service
{
    public class DictionaryService : IDictionaryService
    {
        #region 引用
        public readonly IBedListRepository _bedListRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationListRepository _stationListRepository;
        private readonly IUserRepository _userRepository;
        private readonly IUserRoleRepository _userRoleRepository;
        private CommonHelper _commonHelper;
        private readonly IAppConfigSettingService _appConfigSettingService;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IStationToDeptInfoRepository _stationToDepartmentRepository;
        private readonly IPhysicianOrderRepository _physicianOrderRepository;
        private readonly IDrugListRepository _drugListRepository;
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IEmployeeCADataRepository _employeeCADataRepository;

        #endregion

        #region 常量
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 配置码
        /// </summary>
        private string MODIFYPERSONID = "";
        /// <summary>
        /// 医疗院代码
        /// </summary>
        private string HOSPITALID = "";
        /// <summary>
        /// 更新缓存API
        /// </summary>
        private string CACHEUPDATEAPI = "";
        #endregion

        #region 构造函数
        public DictionaryService(
            IBedListRepository bedListRepository
            , IOptions<SystemConfig> config
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IStationListRepository stationListRepository
            , IUserRepository userRepository
            , IUserRoleRepository userRoleRepository
            , IAppConfigSettingService appConfigSettingService
            , CommonHelper commonHelper
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IDepartmentListRepository departmentListRepository
            , IStationToDeptInfoRepository stationToDepartmentRepository
            , IPhysicianOrderRepository physicianOrderRepository
            , IDrugListRepository drugListRepository
            , MedicalDbContext medicalDbContext
            , IEmployeeCADataRepository employeeCADataRepository
            )
        {
            _bedListRepository = bedListRepository;
            _config = config;
            _unitOfWork = unitOfWork;
            _stationListRepository = stationListRepository;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
            _appConfigSettingService = appConfigSettingService;
            _commonHelper = commonHelper;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _departmentListRepository = departmentListRepository;
            _stationToDepartmentRepository = stationToDepartmentRepository;
            _physicianOrderRepository = physicianOrderRepository;
            _drugListRepository = drugListRepository;
            _medicalDbContext = medicalDbContext;
            GetSettingConfig();
            _employeeCADataRepository = employeeCADataRepository;
        }
        #endregion

        /// <summary>
        /// 获取配置
        /// </summary>
        private void GetSettingConfig()
        {
            HOSPITALID = _config.Value.HospitalID;
            MODIFYPERSONID = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
            CACHEUPDATEAPI = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CacheUpdateAPI").Result;
        }

        #region 床位字典
        /// <summary>
        /// 同步床位字典数据
        /// </summary>
        /// <param name="bedListInfos">床位数据</param>
        /// <returns></returns>
        public async Task<bool> SyncBedListAsync(List<BedListInfo> bedListInfos)
        {
            var addBeds = new List<BedListInfo>();
            try
            {
                var oldBeds = await _bedListRepository.GetAllAsyncNoCache(_config.Value.HospitalID);
                int maxID =  oldBeds.Count == 0 ? 1 : _bedListRepository.GetMaxID();
                foreach (var info in bedListInfos)
                {
                    var oldBed = oldBeds.Find(m => m.StationID == info.StationID && m.DepartmentListID == info.DepartmentListID && m.BedNumber == info.BedNumber);
                    if (oldBed == null)
                    {
                        info.ID = maxID;
                        addBeds.Add(info);
                        maxID++;
                    }
                    else
                    {
                        UpdateBedList(oldBed, info);
                    }
                }
                if (addBeds.Count > 0)
                {
                    await _unitOfWork.GetRepository<BedListInfo>().InsertAsync(addBeds);
                }
                if (_unitOfWork.SaveChanges() >= 0)
                {
                    _bedListRepository.UpdateAsync();
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.Bed}", null);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步床位字典数据失败{ex}");
                return false;
            }
        }
        /// <summary>
        /// 更新床位字典数据
        /// </summary>
        /// <param name="oldBed">旧床位数据</param>
        /// <param name="info">新数据</param>
        private static void UpdateBedList(BedListInfo oldBed, BedListInfo info)
        {
            oldBed.ICUFlag = info.ICUFlag;
            oldBed.DisableFlag = info.DisableFlag;
            oldBed.NewBornFlag = info.NewBornFlag;
            oldBed.AdditionFlag = info.AdditionFlag;
            oldBed.BedLocation = info.BedLocation;
            oldBed.RoomCode = info.RoomCode;
            oldBed.WardBuilding = info.WardBuilding;
            oldBed.WardFloor = info.WardFloor;
            oldBed.ModifyPersonID = info.ModifyPersonID;
            oldBed.ModifyDate = info.ModifyDate;
            oldBed.DeleteFlag = info.DeleteFlag;
        }
        #endregion

        #region 病区科室数据
        /// <summary>
        /// 同步科室和病区数据
        /// </summary>
        /// <param name="stationAndDepartmentInfos">清洗后的病区科室数据</param>
        /// <returns></returns>
        public async Task<bool> SyncStationAndDepartmentAsync(StationAndDepartmentView stationAndDepartmentInfos)
        {
            try
            {
                await SyncStationsAsync(stationAndDepartmentInfos.StationList);
                await SyncDepartmentsAsync(stationAndDepartmentInfos.DepartmentList);
                await SyncStationToDepartmentMappingsAsync(stationAndDepartmentInfos.StationToDepartmentList);

                if (await SaveChangesAndRefreshCacheAsync())
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步病区字典数据失败{ex}");
                return false;
            }
        }

        /// <summary>
        /// 同步病区信息
        /// </summary>
        /// <param name="stationList">病区数据</param>
        /// <returns></returns>
        private async Task SyncStationsAsync(List<StationListInfo> stationList)
        {
            if (stationList == null || stationList.Count == 0)
            {
                return;
            }
            var oldStations = await _stationListRepository.GetStationListNoCache();
            var addStations = new List<StationListInfo>();
            foreach (var station in stationList)
            {
                var oldStation = oldStations.Find(m => m.StationCode == station.StationCode && m.DeleteFlag != "*");
                if (oldStation == null)
                {
                    addStations.Add(station);
                }
                else
                {
                    UpdateStation(oldStation, station);
                }
            }
            if (addStations.Any())
            {
                await _unitOfWork.GetRepository<StationListInfo>().InsertAsync(addStations);
            }
        }

        /// <summary>
        /// 更新病区信息
        /// </summary>
        /// <param name="oldStation">旧数据</param>
        /// <param name="newStation">新数据</param>
        private void UpdateStation(StationListInfo oldStation, StationListInfo newStation)
        {
            oldStation.StationName = newStation.StationName;
            oldStation.VirtalFlag = newStation.VirtalFlag;
            oldStation.AssessToProblemPoint = newStation.AssessToProblemPoint;
            oldStation.HeadNurse = newStation.HeadNurse;
            oldStation.StationPattern = newStation.StationPattern;
            oldStation.StationShortName = newStation.StationShortName;
            oldStation.ModifyDate = newStation.ModifyDate;
            oldStation.DeleteFlag = newStation.DeleteFlag;
        }

        /// <summary>
        /// 同步科室信息
        /// </summary>
        /// <param name="departmentList">科室信息</param>
        /// <returns></returns>
        private async Task SyncDepartmentsAsync(List<DepartmentListInfo> departmentList)
        {
            if (departmentList == null || departmentList.Count == 0) return;

            var oldDepartments = await _departmentListRepository.GetAllAsync<DepartmentListInfo>();
            var addDepartments = new List<DepartmentListInfo>();

            foreach (var department in departmentList)
            {
                var oldDepartment = oldDepartments.Find(m => m.DepartmentCode == department.DepartmentCode);
                if (oldDepartment == null)
                {
                    addDepartments.Add(department);
                }
                else
                {
                    UpdateDepartment(oldDepartment, department);
                }
            }

            if (addDepartments.Any())
            {
                await _unitOfWork.GetRepository<DepartmentListInfo>().InsertAsync(addDepartments);
            }
        }

        /// <summary>
        /// 更新科室信息
        /// </summary>
        /// <param name="oldDepartment"></param>
        /// <param name="newDepartment"></param>
        private void UpdateDepartment(DepartmentListInfo oldDepartment, DepartmentListInfo newDepartment)
        {
            oldDepartment.Department = newDepartment.Department;
            oldDepartment.DepartmentPattern = newDepartment.DepartmentPattern;
            oldDepartment.SpecialListType = newDepartment.SpecialListType;
            oldDepartment.ModifyDate = newDepartment.ModifyDate;
            oldDepartment.DeleteFlag = newDepartment.DeleteFlag;
        }

        /// <summary>
        /// 同步病区科室关系数据
        /// </summary>
        /// <param name="stationToDepartmentList"></param>
        /// <returns></returns>
        private async Task SyncStationToDepartmentMappingsAsync(List<StationToDeptInfo> stationToDepartmentList)
        {
            if (stationToDepartmentList == null || stationToDepartmentList.Count == 0) return;

            var oldStationToDepartments = await _stationToDepartmentRepository.GetAllAsync<StationToDeptInfo>();
            var addStationToDepartments = new List<StationToDeptInfo>();

            foreach (var mapping in stationToDepartmentList)
            {
                var oldMapping = oldStationToDepartments.Find(m => m.StationCode == mapping.StationCode && m.DepartmentCode == mapping.DepartmentCode);
                if (oldMapping == null)
                {
                    addStationToDepartments.Add(mapping);
                }
                else
                {
                    oldMapping.ModifyDate = mapping.ModifyDate;
                    oldMapping.DeleteFlag = mapping.DeleteFlag;
                }
            }

            if (addStationToDepartments.Any())
            {
                await _unitOfWork.GetRepository<StationToDeptInfo>().InsertAsync(addStationToDepartments);
            }
        }

        /// <summary>
        /// 保存更改并刷新缓存
        /// </summary>
        /// <returns></returns>
        private async Task<bool> SaveChangesAndRefreshCacheAsync()
        {
            if (_unitOfWork.SaveChanges() >= 0)
            {
                _stationListRepository.UpdateAsync();
                _departmentListRepository.UpdateAsync();
                _stationListRepository.UpdateAsync();

                // 刷新缓存
                await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.StationToDepartment}", null);
                await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.Station}", null);
                await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.DepartmentList}", null);

                return true;
            }
            return false;
        }
        #endregion

        #region 人员同步
        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="userList">员工列表</param>
        /// <returns></returns>
        public async Task<bool> SyncAllEmployeeData(List<UserView> userList)
        {
            if (userList == null || userList.Count() <= 0)
            {
                _logger.Error("人员同步失败，传入人员列表信息参数为空；");
                return false;
            }
            var nowDateTime = DateTime.Now;
            var stationList = await _stationListRepository.GetAllAsync();
            var stationToDepartments = await _stationToDepartmentRepository.GetAllAsync<StationToDeptInfo>();
            var users = await _userRepository.GetAllUser();
            var userRoleList = _userRoleRepository.NoCacheGetAllRole();
            var employeeDepartmentSwitchList = await _employeeDepartmentSwitchRepository.GetAllAsync<EmployeeDepartmentSwitchInfo>();
            var userInfos = new List<UserInfo>();
            foreach (var item in userList)
            {
                var user = users.Where(m => m.UserID.Trim() == item.UserID).FirstOrDefault();
                var stationToDepartment = stationToDepartments.FirstOrDefault(m => m.DepartmentCode == item.DepartmentCode);
                var station = stationList.FirstOrDefault(m => m.StationCode == item.DepartmentCode);
                var stationID = stationToDepartment?.StationID ?? station?.ID ?? 0;
                if (user == null)
                {
                    //新增
                    var userInfo = CreateUserInfo(item, stationToDepartment, stationID);
                    userInfos.Add(userInfo);
                }
                else
                {
                    UpdateUserInfo(stationList, item, user, stationToDepartment, stationID);
                }
                AddEmployeeRole(item, userRoleList, nowDateTime);
                await UpdateEmployeeDeptSwitch(item.DepartmentCodes, item.UserID, employeeDepartmentSwitchList, stationToDepartments, stationList);
            }
            if (userInfos.Any())
            {
                _unitOfWork.GetRepository<UserInfo>().Insert(userInfos);
            }
            try
            {
                if (_unitOfWork.SaveChanges() >= 0)
                {
                    // 更新当前上下文缓存
                    _userRepository.UpdateAsync();
                    _userRoleRepository.UpdateAsync();
                    _employeeDepartmentSwitchRepository.UpdateAsync();
                    //更新缓存
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.EmployeelData}", null);
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.EmployeeDepartmentSwitch}", null);
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.UserRole}", null);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("EmployeelData表信息同步失败，【错误信息】" + ex.ToString());
            }
            return false;
        }
        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="stationList">病区列表</param>
        /// <param name="userView">新人员信息</param>
        /// <param name="userInfo">旧人员信息</param>
        /// <param name="stationToDept">病区对科室</param>
        /// <param name="compareLevelFlag">是否比较职称与层级标记</param>
        /// <returns></returns>
        private void UpdateUserInfo(List<StationListInfo> stationList, UserView userView, UserInfo userInfo, StationToDeptInfo stationToDept,int stationID, bool compareLevelFlag = true)
        {
            //有特殊标记不处理
            if (userInfo.SpecialFlag == 1)
            {
                return;
            }
            //更新
            if (userInfo.StationID != stationID)
            {
                userInfo.StationID = stationID;
            }
            if (userView.DeleteFlag == "*" && userInfo.ExpirationDate != userView.ExpirationDate)
            {
                userInfo.ExpirationDate = userView.ExpirationDate;
            }
            userInfo.PhysicianID = userView.PhysicianID;
            userInfo.DepartmentID = stationToDept?.DepartmentListID ?? 0;
            if (compareLevelFlag)
            {
                userInfo.Title = userView.Title;
                userInfo.Rank = userView.Rank;
            }
            userInfo.DeleteFlag = userView.DeleteFlag;
            userInfo.PhoneNumber = userView.PhoneNumber;
            userInfo.JobID = userView.JobID;
            userInfo.HREmployeeID = userView.HREmployeeID;
            userInfo.EmployeeType = userView.EmployeeType;
            userInfo.PinYinCode = userView.PinYinCode;
            userInfo.CapabilityLevelID = userView.CapabilityLevelID;
            if (_unitOfWork.DbContext.Entry(userInfo).State == EntityState.Modified)
            {
                userInfo.Modify(MODIFYPERSONID);
            }
        }

        /// <summary>
        /// 创建人员信息
        /// </summary>
        /// <param name="userView">人员信息</param>
        /// <param name="stationToDept">科室对病区信息</param>
        /// <returns></returns>
        private UserInfo CreateUserInfo(UserView userView, StationToDeptInfo stationToDept, int stationID)
        {
            var userInfo = new UserInfo()
            {
                HospitalID = userView.HospitalID,
                UserID = userView.UserID,
                PhysicianID = userView.PhysicianID,
                Password = userView.Password,
                Name = userView.Name,
                StationID = stationID,
                Title = userView.Title,
                Rank = userView.Rank,
                SpecialFlag = userView.SpecialFlag,
                DeleteFlag = userView.DeleteFlag,
                DepartmentID = stationToDept?.DepartmentListID ?? 0,
                PhoneNumber = userView.PhoneNumber,
                JobID = userView.JobID,
                HREmployeeID = userView.HREmployeeID,
                ExpirationDate = userView.DeleteFlag == "*" ? userView.ExpirationDate : null,
                EmployeeType = userView.EmployeeType,
                PinYinCode = userView.PinYinCode,
                CapabilityLevelID = userView.CapabilityLevelID,
            };
            userInfo.Modify(MODIFYPERSONID);
            return userInfo;
        }

        /// <summary>
        /// 添加人员权限
        /// </summary>
        /// <param name="userView">人员信息</param>
        /// <param name="userRoleList">人员权限</param>
        /// <param name="nowDateTime">当前时间</param>
        private void AddEmployeeRole(UserView userView, List<UserRoleInfo> userRoleList, DateTime nowDateTime)
        {
            if (userView.UserRoles.Count() <= 0)
            {
                _logger.Warn($"未获取到员工权限信息，员工编号：{userView.UserID}");
                return;
            }
            foreach (var userRoleID in userView.UserRoles)
            {
                var userRole = userRoleList.FirstOrDefault(m => m.EmployeeID == userView.UserID && m.AuthorityRoleListID == userRoleID);
                if (userRole != null)
                {
                    continue;
                }
                var userRoleInfo = new UserRoleInfo()
                {
                    EmployeeID = userView.UserID,
                    HospitalID = userView.HospitalID,
                    AuthorityRoleListID = userRoleID,
                    AddDate = nowDateTime,
                    AddEmployeeID = MODIFYPERSONID,
                    ModifyDate = nowDateTime,
                    ModifyPersonID = MODIFYPERSONID,
                    DeleteFlag = "",
                };
                _unitOfWork.GetRepository<UserRoleInfo>().Insert(userRoleInfo);
            }
        }
        /// <summary>
        /// 更新用户科室权限
        /// </summary>
        /// <param name="departmentCodes"></param>
        /// <param name="userID"></param>
        /// <param name="employeeDepartmentSwitchList"></param>
        /// <param name="stationToDeptList"></param>
        /// <returns></returns>
        private async Task UpdateEmployeeDeptSwitch(List<string> departmentCodes, string userID,List<EmployeeDepartmentSwitchInfo> employeeDepartmentSwitchList,List<StationToDeptInfo> stationToDeptList
            , List<StationListInfo> stationList)
        {
            var orginalEmpDeptSwitchs = employeeDepartmentSwitchList.Where(m => m.EmployeeID == userID).ToList();
            var stationCodes = stationToDeptList.Where(m => departmentCodes.Contains(m.DepartmentCode) && !string.IsNullOrEmpty(m.StationCode)).Select(m => m.StationCode).Distinct().ToList();
            var stationListCodes = stationList.Where(m => !string.IsNullOrEmpty(m.StationCode) && departmentCodes.Contains(m.StationCode) && !stationCodes.Contains(m.StationCode)).Select(m => m.StationCode).Distinct().ToList();
            if (stationListCodes.Count > 0)
            {
                stationCodes.AddRange(stationListCodes);
            }
            if (!orginalEmpDeptSwitchs.Any())
            {
                await AddNewSwitchesAsync(stationCodes, userID);
                return;
            }
            var orginalStationCodes = orginalEmpDeptSwitchs.Select(m => m.DepartmentCode).Distinct().ToList();
            // 找出需要新增的：在 stationCodes 中但不在 orginalStationCodes 中
            var codesToAdd = stationCodes.Except(orginalStationCodes).ToList();
            await AddNewSwitchesAsync(codesToAdd, userID);

            // 找出需要删除的：在 orginalStationCodes 中但不在 stationCodes 中
            var codesToRemove = orginalStationCodes.Except(stationCodes).ToList();
            RemoveOldSwitchesAsync(orginalEmpDeptSwitchs, codesToRemove);
        }
        /// <summary>
        /// 新增人员病区权限
        /// </summary>
        /// <param name="stationCodes"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        private async Task AddNewSwitchesAsync(List<string> stationCodes, string userID)
        {
            if (stationCodes == null || !stationCodes.Any()) return;
            var newSwitchList = stationCodes.Select(code => new EmployeeDepartmentSwitchInfo
            {
                EmployeeID = userID,
                DepartmentCode = code,
                ModifyDate = DateTime.Now,
                ModifyPersonID = MODIFYPERSONID,
                DeleteFlag = "",
                HospitalID = HOSPITALID
            }).ToList();
            await _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().InsertAsync(newSwitchList);
        }
        /// <summary>
        /// 删除人员病区权限
        /// </summary>
        /// <param name="orginalEmpDeptSwitchs"></param>
        /// <param name="codesToRemove"></param>
        /// <returns></returns>
        private void RemoveOldSwitchesAsync(List<EmployeeDepartmentSwitchInfo> orginalEmpDeptSwitchs, List<string> codesToRemove)
        {
            if (codesToRemove == null || !codesToRemove.Any()) return;
            //避免删除手工维护的，只删除同步程序修改的
            var deleteOrginalEmpDeptSwitchs = orginalEmpDeptSwitchs.Where(m => codesToRemove.Contains(m.DepartmentCode) && m.ModifyPersonID == MODIFYPERSONID).ToList();
            foreach (var deleteOrginalEmpDeptSwitch in deleteOrginalEmpDeptSwitchs)
            {
                deleteOrginalEmpDeptSwitch.Delete(MODIFYPERSONID);
                _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().Update(deleteOrginalEmpDeptSwitch);
            }
        }
        /// <summary>
        /// 同步单个人员
        /// </summary>
        /// <param name="user">人员信息</param>
        /// <returns></returns>
        public async Task<bool> SyncOneEmployeeData(UserView user)
        {
            if (user == null)
            {
                _logger.Error("人员同步失败，传入人员列表信息参数为空；");
                return false;
            }
            var nowDateTime = DateTime.Now;
            var stationList = await _stationListRepository.GetAllAsync();
            var userInfos = await _userRepository.GetAllUser();
            var userRoleList = _userRoleRepository.NoCacheGetAllRole();
            var stationToDepartments = await _stationToDepartmentRepository.GetAllAsync<StationToDeptInfo>();
            var employeeDepartmentSwitchList = await _employeeDepartmentSwitchRepository.GetAllAsync<EmployeeDepartmentSwitchInfo>();
            try
            {
                var userInfo = userInfos.FirstOrDefault(m => m.UserID.Trim() == user.UserID);
                var stationToDepartment = stationToDepartments.FirstOrDefault(m => m.DepartmentCode == user.DepartmentCode);
                var station = stationList.FirstOrDefault(m => m.StationCode == user.DepartmentCode);
                var stationID = stationToDepartment?.StationID ?? station?.ID ?? 0;
                if (userInfo == null)
                {
                    //新增
                    var insertUserInfo = CreateUserInfo(user, stationToDepartment, stationID);
                    _unitOfWork.GetRepository<UserInfo>().Insert(insertUserInfo);
                }
                else
                {
                    UpdateUserInfo(stationList, user, userInfo, stationToDepartment, stationID, false);
                }
                AddEmployeeRole(user, userRoleList, nowDateTime);
                await UpdateEmployeeDeptSwitch(user.DepartmentCodes, userInfo.UserID, employeeDepartmentSwitchList, stationToDepartments, stationList);
                if (_unitOfWork.SaveChanges() >= 0)
                {
                    // 更新当前上下文EmployeelData缓存
                    _userRepository.UpdateAsync();
                    _userRoleRepository.UpdateAsync();
                    _employeeDepartmentSwitchRepository.UpdateAsync();
                    //更新缓存
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.EmployeelData}", null);
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.EmployeeDepartmentSwitch}", null);
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.UserRole}", null);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"异常信息：{ex.ToString()}");
            }
            return false;
        }
        #endregion


        #region 医嘱字典同步

        /// <summary>
        /// 同步医嘱字典
        /// </summary>
        /// <param name="orderDictViewList"></param>
        /// <returns></returns>
        public async Task<bool> SyncPhysionOrder(List<OrderDictView> orderDictViewList)
        {
            _logger.Info("开始同步医嘱字典" + orderDictViewList.Count + "条数据");
            var medicalOrderList = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            List<PhysicianOrderInfo> insertList = new List<PhysicianOrderInfo>();

            foreach (var orderDictView in orderDictViewList)
            {
                var medicalOrder = medicalOrderList.FirstOrDefault(m => m.OrderCode == orderDictView.OrderCode);

                if (medicalOrder == null)
                {
                    // 创建新医嘱
                    insertList.Add(new PhysicianOrderInfo
                    {
                        ID = orderDictView.ID,
                        HospitalID = _config.Value.HospitalID,
                        OrderType = orderDictView.OrderType,
                        TypeName = orderDictView.TypeName ?? string.Empty,
                        OrderCode = orderDictView.OrderCode,
                        OrderName = orderDictView.OrderName ?? string.Empty,
                        ModifyPersonID = MODIFYPERSONID,
                        ModifyDate = DateTime.Now,
                        DeleteFlag = string.Empty
                    });
                }
                else
                {
                    var updateRequired = UpdateMedicalOrder(medicalOrder, orderDictView);
                    if (updateRequired)
                    {
                        medicalOrder.ModifyDate = DateTime.Now;
                        medicalOrder.ModifyPersonID = MODIFYPERSONID;
                    }
                }
            }
            try
            {
                _unitOfWork.GetRepository<PhysicianOrderInfo>().Insert(insertList);
                return _unitOfWork.SaveChanges() > 0;
            }
            catch (Exception ex)
            {
                _logger.Info("||同步医嘱字典失败||" + ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 更新医嘱字典信息
        /// </summary>
        /// <param name="medicalOrder"></param>
        /// <param name="orderDictView"></param>
        /// <returns></returns>
        private bool UpdateMedicalOrder(PhysicianOrderInfo medicalOrder, OrderDictView orderDictView)
        {

            if (medicalOrder.OrderType != orderDictView.OrderType)
            {
                medicalOrder.OrderType = orderDictView.OrderType;
            }

            if (medicalOrder.TypeName != orderDictView.TypeName)
            {
                medicalOrder.TypeName = orderDictView.TypeName;
            }

            if (medicalOrder.OrderName != orderDictView.OrderName)
            {
                medicalOrder.OrderName = orderDictView.OrderName;
            }
            //确认是否进行了更新
            return _medicalDbContext.Entry(medicalOrder).State != Microsoft.EntityFrameworkCore.EntityState.Unchanged;
        }

        #endregion

        #region 医嘱字典同步

        /// <summary>
        /// 同步药品字典
        /// </summary>
        /// <param name="drugDictViewList"></param>
        /// <returns></returns>
        public async Task<bool> SyncDrugList(List<DrugDictView> drugDictViewList)
        {
            _logger.Info("开始同步药品字典" + drugDictViewList.Count + "条数据");
            var medicalDrugList = await _drugListRepository.GetAllDrugLists();
            List<DrugListInfo> insertList = new List<DrugListInfo>();
            foreach (var drugDictView in drugDictViewList)
            {
                var medicalDrug = medicalDrugList.FirstOrDefault(m => m.DrugCode == drugDictView.DrugCode);
                if (medicalDrug == null)
                {
                    // 创建新医嘱
                    insertList.Add(new DrugListInfo
                    {
                        HospitalID = drugDictView.HospitalID,
                        DrugCode = drugDictView.DrugCode,
                        DrugName = drugDictView.DrugName,
                        DrugSpec = drugDictView.DrugSpec,
                        Units = drugDictView.Units,
                        DrugType = drugDictView.DrugType,
                        DrugForm = drugDictView.DrugForm,
                        DoseUnits = drugDictView.DoseUnits,
                        DosePreUnit = drugDictView.DosePreUnit,
                        PinYinCode = drugDictView.PinYinCode,
                        HighRiskFlag = drugDictView.HighRiskFlag,
                        ModifyPersonID = MODIFYPERSONID,
                        ModifyDate = DateTime.Now,
                        DeleteFlag = ""
                    });
                }
                else
                {
                    var updateRequired = UpdateMedicalDrug(medicalDrug, drugDictView);
                    if (updateRequired)
                    {
                        medicalDrug.ModifyDate = DateTime.Now;
                        medicalDrug.ModifyPersonID = MODIFYPERSONID;
                    }
                }
            }

            try
            {
                if (insertList.Count > 0)
                {
                    _unitOfWork.GetRepository<DrugListInfo>().Insert(insertList);
                }
                return _unitOfWork.SaveChanges() > 0;
            }
            catch (Exception ex)
            {
                _logger.Info("||同步医嘱字典失败||" + ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 更新药品字典信息
        /// </summary>
        /// <param name="medicalDrug"></param>
        /// <param name="drugDictView"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private bool UpdateMedicalDrug(DrugListInfo medicalDrug, DrugDictView drugDictView)
        {
            // 直接赋值，EF会自动比较并更新变化的属性
            medicalDrug.DrugName = drugDictView.DrugName;
            medicalDrug.DrugSpec = drugDictView.DrugSpec;
            medicalDrug.Units = drugDictView.Units;
            medicalDrug.DrugType = drugDictView.DrugType;
            medicalDrug.DrugForm = drugDictView.DrugForm;
            medicalDrug.DoseUnits = drugDictView.DoseUnits;
            medicalDrug.DosePreUnit = drugDictView.DosePreUnit;
            medicalDrug.PinYinCode = drugDictView.PinYinCode;
            medicalDrug.HighRiskFlag = drugDictView.HighRiskFlag;

            // 确认是否进行了更新
            return _medicalDbContext.Entry(medicalDrug).State != Microsoft.EntityFrameworkCore.EntityState.Unchanged;
        }
        #endregion

        #region CA同步
        /// <summary>
        /// 同步用户CA数据
        /// </summary>
        /// <param name="employeeCAViews"></param>
        /// <returns></returns>
        public async Task<bool> SyncEmployeeCAData(List<EmployeeCAView> employeeCAViews)
        {
            if (employeeCAViews == null || employeeCAViews.Count == 0)
            {
                return false;
            }
            var newEmployeeCADataList = new List<EmployeeCADataInfo>();
            var oldEmployeeCADataList = await _employeeCADataRepository.GetEmployeeCANoCacheAsync();
            foreach (var employeeCAView in employeeCAViews)
            {
                var oldEmployeeCA = oldEmployeeCADataList.FirstOrDefault(m => m.UserID == employeeCAView.UserID);
                if (oldEmployeeCA == null)
                {
                    //获取的集合没有的新增
                    var cADataInfo = new EmployeeCADataInfo
                    {
                        HospitalID = employeeCAView.HospitalID,
                        UserID = employeeCAView.UserID,
                        CAUserID = employeeCAView.CAUserID,
                        StampImage = employeeCAView.StampImage,
                        AddPersonID = MODIFYPERSONID,
                        AddDateTime = DateTime.Now
                    };
                    newEmployeeCADataList.Add(cADataInfo);
                    continue;
                }
                //已有的更新
                if (UpdateEmployeeCADataInfo(employeeCAView, oldEmployeeCA))
                {
                    oldEmployeeCA.Modify(MODIFYPERSONID);
                }
            }
            if (newEmployeeCADataList.Count > 0)
            {
                _unitOfWork.GetRepository<EmployeeCADataInfo>().Insert(newEmployeeCADataList);
            }
            try
            {
                if (_unitOfWork.SaveChanges() > 0)
                {
                    await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.EmployeeCAData}", null);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("同步用户CA数据失败" + ex.ToString());
                return false;
            }
        }
        /// <summary>
        /// 更新用户CA数据
        /// </summary>
        /// <param name="employeeCAView"></param>
        /// <param name="oldEmployeeCA"></param>
        private bool UpdateEmployeeCADataInfo(EmployeeCAView employeeCAView, EmployeeCADataInfo oldEmployeeCA)
        {
            // 直接赋值，EF会自动比较并更新变化的属性
            oldEmployeeCA.CAUserID = employeeCAView.CAUserID;
            oldEmployeeCA.StampImage = employeeCAView.StampImage;
            // 确认是否进行了更新
            return _medicalDbContext.Entry(oldEmployeeCA).State != Microsoft.EntityFrameworkCore.EntityState.Unchanged;
        }
        #endregion
    }
}
