﻿using Medical.Data.Context;
using Medical.Data.Interface;

namespace Medical.Data.Repository
{
    //排除原因： PatientTubeMain表不再使用
    //操作人员：梁宝华 2020-03-17
    public class PatientTubeRepository : IPatientTubeRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientTubeRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        //public async Task<List<PatientTubeInfo>> GetAsync(string inPatientID, bool? isCurrent = null)
        //{
        //    if (inPatientID == "")
        //    {
        //        if (isCurrent.Value == true)
        //        {
        //            var oldData = from oldDatas in _medicalDbContext.PatientTubes
        //                          join inpatient in _medicalDbContext.InpatientDatas on oldDatas.InpatientID equals inpatient.ID
        //                          where oldDatas.DataPumpFlag != "A" && oldDatas.DeleteFlag != "*" && inpatient.DeleteFlag != "*" && inpatient.DischargeDate != null
        //                          select oldDatas;

        //            return oldData.ToList();
        //        }
        //        else
        //        {
        //            var oldData = from oldDatas in _medicalDbContext.PatientTubes
        //                          join inpatient in _medicalDbContext.InpatientDatas on oldDatas.InpatientID equals inpatient.ID
        //                          where oldDatas.DataPumpFlag == null && oldDatas.DeleteFlag != "*" 
        //                          select oldDatas;

        //            return oldData.ToList();
        //        }
        //        //return await _medicalDbContext.PatientTubes.Where(m => m.DataPumpFlag!="A"
        //        //                        && m.DeleteFlag != "*").ToListAsync();
        //    }

        //    var query = await _medicalDbContext.PatientTubes.Where(m =>
        //     m.InpatientID == inPatientID &&
        //     m.DeleteFlag != "*").ToListAsync();

        //    if (isCurrent != null)
        //    {
        //        if (isCurrent.Value)
        //        {
        //            query = query.Where(m => m.RemoveTime == null).ToList();
        //        }
        //        else
        //        {
        //            query = query.Where(m => m.RemoveTime != null).ToList();
        //        }
        //    }
        //    return query;
        //}

        ///// <summary>
        ///// 获取所有导管
        ///// </summary>
        ///// <param name="inPatientID"></param>
        ///// <param name="stationID"></param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetAllAsync(string inPatientID, int stationID)
        //{
        //    return await _medicalDbContext.PatientTubes.Where(m =>
        //     m.InpatientID == inPatientID && m.StationID == stationID &&
        //     m.DeleteFlag != "*").ToListAsync();
        //}

        //public async Task<PatientTubeInfo> GetAsync(string id)
        //{
        //    return await _medicalDbContext.PatientTubes.Where(m => m.PatientTubeID == id).SingleAsync();
        //}

        ///// <summary>
        /////  获取病人未结束导管记录
        ///// </summary>
        ///// <param name="stationID">单位代码</param>
        ///// <param name="inPatientID">病人在院号</param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetUnFinishedAsync(string inPatientID)
        //{
        //    return await GetAsync(inPatientID, true);
        //}

        ///// <summary>
        ///// 根据排程ID获取未结束的导管
        ///// </summary>
        ///// <param name="ids">排程主ID</param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetByScheduleMainIDs(List<string> ids)
        //{
        //    var patientTubeList = new List<PatientTubeInfo>();
        //    foreach (var item in ids)
        //    {
        //        var tempList = await _medicalDbContext.PatientTubes.Where(m =>
        //        m.PatientScheduleMainID == item &&
        //        m.DeleteFlag != "*").ToListAsync();
        //        patientTubeList = patientTubeList.Union(tempList).ToList();
        //    }
        //    return patientTubeList.Where(m => m.RemoveTime == null).ToList();
        //}

        ///// <summary>
        ///// 获取时间点后未结束的导管
        ///// </summary>
        ///// <param name="inpatientID"></param>
        ///// <param name="stationID"></param>
        ///// <param name="dateTime"></param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetAsync(string inpatientID, int stationID, DateTime dateTime)
        //{
        //    //未移除导管
        //    var list = await _medicalDbContext.PatientTubes.Where(m =>
        //    m.InpatientID == inpatientID && m.StationID == m.StationID
        //    && (m.RemoveDate.ToString().Length == 0 || m.RemoveDate == null)
        //    && m.DeleteFlag != "*").ToListAsync();
        //    //移除时间在传入时间点后的
        //    list.AddRange(await _medicalDbContext.PatientTubes.Where(m =>
        //   m.InpatientID == inpatientID && m.StationID == m.StationID
        //   && m.RemoveDate != null && ((DateTime)m.RemoveDate).Add((TimeSpan)m.RemoveTime) > dateTime
        //   && m.DeleteFlag != "*").ToListAsync());

        //    return list;
        //}

        ///// <summary>
        ///// 获取数据
        ///// </summary>
        ///// <param name="InpatientID"></param>
        ///// <param name="num"></param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetPatientTubeMainByNumAsync(string InpatientID, string num)
        //{
        //    return await _medicalDbContext.PatientTubes.Where(m =>
        //    m.InpatientID == InpatientID &&
        //    m.AssessMainID == num && m.DeleteFlag != "*").ToListAsync();
        //}

        ///// <summary>
        ///// 取得病人身上导管
        ///// </summary>
        ///// <param name="inpatientID">病人住院序号</param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetPatientTube(string inpatientID)
        //{
        //    return await _medicalDbContext.PatientTubes.Where(m =>
        //           m.InpatientID == inpatientID).ToListAsync();
        //}

        ///// <summary>
        ///// 取得病人身上导管
        ///// </summary>
        ///// <param name="patientTubeIDs">病人导管序号</param>
        ///// <returns></returns>
        //public async Task<List<PatientTubeInfo>> GetPatientTube(string[] patientTubeIDs)
        //{
        //    var patientTubeList = new List<PatientTubeInfo>();
        //    for (int i = 0; i < patientTubeIDs.Length; i++)
        //    {
        //        var tempList = await _medicalDbContext.PatientTubes.Where(m =>
        //            m.PatientTubeID == patientTubeIDs[i]).ToListAsync();
        //        patientTubeList = patientTubeList.Union(tempList).ToList();
        //    }
        //    return patientTubeList;
        //}
    }
}