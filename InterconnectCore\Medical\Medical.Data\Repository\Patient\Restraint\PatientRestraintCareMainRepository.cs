﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRestraintCareMainRepository : IPatientRestraintCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientRestraintCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据ID获取对应数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientRestraintCareMainInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintCareMainID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取recordID对应的所有评估主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientRestraintCareMainInfo>> GetByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintRecordID == recordID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取recordID对应的所有评估主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientRestraintCareMain>> GetCareMainViewByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintRecordID == recordID && t.DeleteFlag != "*")
                .Select(m => new PatientRestraintCareMain
                {
                    PatientRestraintRecordID = m.PatientRestraintRecordID,
                    PatientRestraintCareMainID = m.PatientRestraintCareMainID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    NursingLevel = m.NursingLevel,
                    RecordsCode = m.RecordsCode,
                    NumberOfAssessment = m.NumberOfAssessment,
                    AssessDateTime = m.AssessDate.Add(m.AssessTime),
                    RestraintTool = m.RestraintTool,
                    RestraintLocation = m.RestraintLocation,
                    RestraintReason = m.RestraintReason,
                    CareIntervention = m.CareIntervention,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InformPhysician = m.InformPhysician ?? false,
                    AddEmployeeID = m.AddEmployeeID
                }).ToListAsync();
        }
        /// <summary>
        /// 获取recordID对应的按次数的最后一次评估主记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientRestraintCareMainInfo> GetLastByNumAsync(string id)
        {
            var list = await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintRecordID == id && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        /// <summary>
        /// 获取recordID对应的按次数的最后一次评估主记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientRestraintCareMainInfo> GetLastByTimeAsync(string id)
        {
            var list = await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintRecordID == id && t.DeleteFlag != "*")
                                .OrderByDescending(t => t.AssessDate).ThenByDescending(t => t.AssessTime).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }
        /// <summary>
        ///  获取措施码对应的所有维护记录
        /// </summary>
        /// <param name="scheduleID"></param>
        /// <returns></returns>
        public async Task<List<PatientRestraintCareMainInfo>> GetCareByScheduleID(string scheduleID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientScheduleMainID == scheduleID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据RecordsCode获取约束评估数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientRestraintCareMain> GetCareByRecordsCode(string recordID, string recordsCode)
        {
            var careMainView = await _medicalDbContext.PatientRestraintCareMainInfos.Where(t => t.PatientRestraintRecordID == recordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*")
                .Select(m => new PatientRestraintCareMain
                {
                    InpatientID = m.InpatientID,
                    PatientRestraintCareMainID = m.PatientRestraintCareMainID,
                    StationID = m.StationID,
                }).FirstOrDefaultAsync();
            return careMainView;
        }

        //透过评估序号取得内容
        public async Task<List<PatientRestraintCareMainInfo>> GetByAssessMainID(string assessMainID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.PatientAssessMainID == assessMainID
           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRestraintCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRestraintCareMainInfo>> GetMainByTimeData(DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.AssessDate >= startTime && m.AssessDate <= endTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRestraintCareMainInfo>> GetByRecordIDArrayAsync(string[] recordIDs)
        {
            var patientRestraintCareMainList = new List<PatientRestraintCareMainInfo>();
            for (int t = 0; t < recordIDs.Length - 1; t++)
            {
                var patientRestraintCareMain = await GetByRecordID(recordIDs[t]);
                if (patientRestraintCareMain != null)
                {
                    patientRestraintCareMainList.AddRange(patientRestraintCareMain);
                }
            }
            return patientRestraintCareMainList;
        }

        public async Task<List<RestraintCareContent>> GetCareContent(string patientRestraintRecordID, string hospitalID)
        {
            var query = await (from m in _medicalDbContext.PatientRestraintCareMainInfos
                               join n in _medicalDbContext.userInfos on new { UserID = m.AddEmployeeID } equals new { n.UserID }
                               where n.HospitalID == hospitalID && m.PatientRestraintRecordID == patientRestraintRecordID && m.DeleteFlag != "*"
                               select new RestraintCareContent
                               {
                                   RecordsCode = m.RecordsCode,
                                   NumberOfAssement = m.NumberOfAssessment,
                                   StationID = m.StationID,
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   RestraintTool = m.RestraintTool,
                                   RestraintLocation = m.RestraintLocation,
                                   RestraintReason = m.RestraintReason,
                                   CareIntervention = m.CareIntervention,
                                   UserName = n.Name,
                                   UserID = m.AddEmployeeID,
                                   BedNumber = m.BedNumber
                               }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
            return query;
        }
        public async Task<List<RestraintCareContent>> GetAllCareContent(string inpatientID)
        {
            var query = await (from m in _medicalDbContext.PatientRestraintCareMainInfos
                               join n in _medicalDbContext.userInfos on new { UserID = m.AddEmployeeID } equals new { n.UserID }
                               where m.InpatientID == inpatientID && m.DeleteFlag != "*"
                               select new RestraintCareContent
                               {
                                   RecordsCode = m.RecordsCode,
                                   NumberOfAssement = m.NumberOfAssessment,
                                   StationID = m.StationID,
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   RestraintTool = m.RestraintTool,
                                   RestraintLocation = m.RestraintLocation,
                                   RestraintReason = m.RestraintReason,
                                   CareIntervention = m.CareIntervention,
                                   UserName = n.Name,
                                   PatientRestraintRecordID = m.PatientRestraintRecordID,
                                   BedNumber = m.BedNumber,
                                   UserID = m.AddEmployeeID
                               }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
            return query;
        }
        public async Task<List<RestraintDetailView>> GetCareDetail(string patientRestraintRecordID)
        {
            var query = await (from m in _medicalDbContext.PatientRestraintCareMainInfos
                               join n in _medicalDbContext.PatientRestraintCareDetailInfos on new { m.PatientRestraintCareMainID } equals new { n.PatientRestraintCareMainID }
                               where m.PatientRestraintRecordID == patientRestraintRecordID && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                               select new RestraintDetailView
                               {
                                   NumberOfAssement = m.NumberOfAssessment,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue
                               }).OrderBy(m => m.NumberOfAssement).ToListAsync();
            return query;
        }

        public async Task<List<SchedulePerformDetail>> GetDayPerformDetail(DateTime shiftDate, string inpatientID)
        {
            var datas = await (from a in _medicalDbContext.PatientRestraintCareMainInfos
                               join b in _medicalDbContext.PatientRestraintCareDetailInfos on a.PatientRestraintCareMainID equals b.PatientRestraintCareMainID
                               where a.InpatientID == inpatientID && a.ShiftDate == shiftDate && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new SchedulePerformDetail
                               {
                                   PerformDate = a.AssessDate,
                                   PerformTime = a.AssessTime,
                                   AssessListID = b.AssessListID,
                                   ScheduleData = b.AssessValue,
                                   InterventionDetailID = b.AssessListID
                               }).ToListAsync();

            return datas;
        }
        public async Task<List<PatientRestraintCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        /// <summary>
        /// 获取交班时约束内容
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="startDateTime">开始日期</param>
        /// <param name="endDateTime">结束日期</param>
        /// <param name="bringToShiftDatas">过滤出勾选了带入交班的</param>
        /// <returns></returns>
        public async Task<List<RestraintHandoverView>> GetPatientRestraintHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime, bool bringToShiftDatas = false)
        {
            var datas = await (from m in _medicalDbContext.PatientRestraintRecordInfos.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID)
                               join n in _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID)
                               on m.PatientRestraintRecordID equals n.PatientRestraintRecordID
                               where n.AssessDate >= startDateTime.Date && n.AssessDate <= endDateTime.Date
                               select new RestraintHandoverView
                               {
                                   PatientRestraintRecordID = m.PatientRestraintRecordID,
                                   PatientRestraintCareMainID = n.PatientRestraintCareMainID,
                                   StartDateTime = m.StartDate.Add(m.StartTime),
                                   EndDate = m.EndDate,
                                   EndTime = m.EndTime,
                                   RecordsCode = n.RecordsCode,
                                   AssessDate = n.AssessDate,
                                   AssessTime = n.AssessTime,
                                   StationID = m.StationID,
                                   BringToShift = n.BringToShift ?? false,
                               }).ToListAsync();
            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).ToList();
            if (bringToShiftDatas)
            {
                return datas.Where(m => m.BringToShift == bringToShiftDatas).ToList();
            }
            return datas.OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .GroupBy(m => m.PatientRestraintRecordID).Select(m => m.First()).ToList();
        }
        public async Task<List<RestraintHandoverView>> GetHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            return await (from a in _medicalDbContext.PatientRestraintRecordInfos.Where(m => m.DeleteFlag != "*")
                          join b in _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.DeleteFlag != "*")
                          on a.PatientRestraintRecordID equals b.PatientRestraintRecordID
                          where a.InpatientID == inpatientID
                             && (a.StartDate < endDateTime.Date || (a.StartDate == endDateTime.Date && a.StartTime <= endDateTime.TimeOfDay))
                             && (!a.EndDate.HasValue || (a.EndDate.HasValue && (a.EndDate.Value > startDateTime.Date || (a.EndDate == startDateTime.Date && a.EndTime >= startDateTime.TimeOfDay))))
                          select new RestraintHandoverView
                          {
                              PatientRestraintRecordID = a.PatientRestraintRecordID,
                              PatientRestraintCareMainID = b.PatientRestraintCareMainID,
                              StartDateTime = a.StartDate.Add(a.StartTime),
                              EndDate = a.EndDate,
                              EndTime = a.EndTime,
                              RecordsCode = b.RecordsCode,
                              AssessDate = b.AssessDate,
                              AssessTime = b.AssessTime,
                              StationID = a.StationID,
                              BringToShift = b.BringToShift ?? false,
                          }).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
        /// <summary>
        /// 获取当前病人所有主记录的选框勾选状态
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientBringView>> GetRecordsBringViewsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientRestraintCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.RecordsCode.Contains("Start")
            ).Select(m => new PatientBringView
            {
                RecordID = m.PatientRestraintRecordID,
                CareMainID = m.PatientRestraintCareMainID,
                BringToShift = m.BringToShift,
                BringToNursingRecord = m.BringToNursingRecord,
                InformPhysician = m.InformPhysician
            }).ToListAsync();
        }
    }
}