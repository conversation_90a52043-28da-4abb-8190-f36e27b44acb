﻿using Medical.Models;
using Medical.ViewModels.Interface;

namespace InterconnectCore.ViewModels
{
    /// <summary>
    /// 同步的时候，需要处理的病人信息
    /// </summary>
    public class SyncInpatientDataView
    {
        /// <summary>
        /// 数据状态
        /// </summary>
        public bool RetureFlag { get; set; }

        /// <summary>
        /// 新增的病人数据
        /// </summary>
        public InpatientDataInfo InsertInpatientDataInfo { get; set; }
        /// <summary>
        /// 需要呼叫Porfile的数据
        /// </summary>
        public List<PatientProfile> PatientProfileList { get; set; }

        /// <summary>
        /// 需要处理的病人事件数据
        /// </summary>
        public List<InPatientChangeViewInfo> InPatientChangeViewList { get; set; }
        /// <summary>
        /// 需要处理的Mark数据
        /// </summary>
        public List<MarkView> MarkViewList { get; set; }
    }
}