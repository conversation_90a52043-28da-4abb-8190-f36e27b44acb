﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientThrombolysisCareMainRepository : IPatientThrombolysisCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientThrombolysisCareMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<PatientThrombolysisCareMainInfo> GetDataByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(m => m.PatientThrombolysisCareMainID == careMainID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主表ID和recordsCode获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="recordsCode"></param>
        /// <returns></returns>
        public async Task<PatientThrombolysisCareMainInfo> GetDataByRecordIDAndRecordCode(string recordID, string recordsCode)
        {
            return await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(m => m.PatientThrombolysisRecordID == recordID && m.RecordsCode == recordsCode).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据溶栓维护记录ID获取溶栓记录维护详情
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<PatientThrombolysisCareMainInfo> GetThrombolysisMainByMainID(string mainID)
        {
            return await _medicalDbContext.PatientThrombolysisCareMainInfos.FirstAsync(t => t.PatientThrombolysisCareMainID == mainID && t.DeleteFlag != "*");
        }
        /// <summary>
        /// 主记录获取 维护记录列表
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientThrombolysisCareMainInfo>> GetThrombolysisMainsByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(t => t.PatientThrombolysisRecordID == recordID && t.DeleteFlag != "*").OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
        }
        /// <summary>
        /// 获取评估次数
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<int> GetThrombolysisMainsAssessContByRecordID(string recordID)
        {
            var list = await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(t => t.PatientThrombolysisRecordID == recordID && t.DeleteFlag != "*").OrderByDescending(m => m.NumberOfAssessment).ToListAsync();
            if (list.Count == 0)
            {
                return 1;
            }
            return list[0].NumberOfAssessment.HasValue ? list[0].NumberOfAssessment.Value : list.Count;
        }

        /// <summary>
        /// 根据主表ID获取RecordsCode
        /// </summary>
        /// <param name="careMainID">主表ID</param>
        /// <returns></returns>
        public async Task<string> GetRecordsCodeByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(t => t.PatientThrombolysisCareMainID == careMainID)
                .Select(t => t.RecordsCode).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取所有主记录的勾选数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientBringView>> GetRecordsBringViewsByInpatientID(string inpatientID)
        {

            return await _medicalDbContext.PatientThrombolysisCareMainInfos.Where(m => m.InpatientID == inpatientID && m.RecordsCode.Contains("Start") && m.DeleteFlag != "*")
                .Select(m => new PatientBringView
                {
                    RecordID = m.PatientThrombolysisRecordID,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InformPhysician = m.InformPhysician
                }).ToListAsync();
        }
    }
}
