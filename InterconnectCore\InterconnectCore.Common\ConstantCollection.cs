﻿namespace InterconnectCore.Common
{
    /// <summary>
    /// 常量集合类 | 主要用于配置需要在程序中写死的变量集合 -提供一个公共的访问的方法
    /// </summary>
    public static class ConstantCollection
    {
        /// <summary>
        /// 给药药品类别： 主要用于判断哪些药品需要进行双人核对并回写核对记录到HIS
        /// </summary>
        public static List<string> ReCheckDrugTypes = new List<string> { "精神二类" };

        #region --交班工厂带入内容对应常量集合配置

        /// <summary>
        /// 肠造口 带入交班的内容K,V对应专项模板中的AssessListGroupIDs
        /// </summary>
        public static readonly List<int> HANDOVER_ENTEROSTOMA_ASSESSLISTGIDS = new List<int>
            {
                30007150, //造口开放
                30007180, //排气
                30007210, //排便
                30007250,//造口用物及附件
                30007360, //造口黏膜 --用作颜色
                30007430, //D-皮肤变色面积
                30007435, //D-变色严重程度
                30007440, //E-侵蚀溃疡面积
                30007445, //E-侵蚀严重程度
                30007450, //T-组织增生面积
                30007455 //T-增生严重程度
            };

        /// <summary>
        /// 泌尿造口 带入交班的内容K,V对应专项模板中的AssessListGroupIDs
        /// </summary>
        public static readonly List<int> HANDOVER_UROSTOMA_ASSESSLISTGIDS = new List<int> {
            30007590, //造口内导管
            30007250, //造口用物及附件
            30007360, //造口黏膜
            30007430, //D-皮肤变色面积
            30007435, //D-变色严重程度
            30007440, //E-侵蚀溃疡面积
            30007445, //E-侵蚀严重程度
            30007450, //T-组织增生面积
            30007455 //T-增生严重程度
        };

        #endregion

        /// <summary>
        /// 护理级别医嘱对应的医嘱码集
        /// </summary>
        public static List<string> NURSING_LEVEL_ORDER_CODES =
            new List<string> { "55091890", "55091891", "55091892", "55091893", "55204403", "55204415", "55204424", "55211366" };

        /// <summary>
        /// 中山病人病历使用CA签章的开始时间点
        /// </summary>
        public static readonly DateTime USE_CA_START__TIME_OF_ZHONGSHAN = new DateTime(2023, 04, 24, 14, 0, 0);

        /// <summary>
        /// 中厦门病人病历使用CA签章的开始时间点
        /// </summary>
        public static readonly DateTime USE_CA_START__TIME_OF_ZHONGSHANXIAMEN = new DateTime(2023, 09, 19, 08, 0, 0);

        /// <summary>
        /// 银川病人病历使用CA签章的开始时间点
        /// </summary>
        public static readonly DateTime USE_CA_START__TIME_OF_YinChuan = new DateTime(2023, 07, 03, 14, 0, 0);

        /// <summary>
        /// 心电监测医嘱对应的医嘱码集
        /// </summary>
        public static List<string> ECG_ORDER_CODES = new List<string> { "51510555", "55209619", "55208960" };
    }
}