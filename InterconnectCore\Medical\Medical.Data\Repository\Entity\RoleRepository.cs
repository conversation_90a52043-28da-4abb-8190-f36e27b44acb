﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RoleRepository : IRoleRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public RoleRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据id获取角色信息
        /// 2019.08.28 孟昭永
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<RoleInfo> GetRoleById(int id)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.ID == id).FirstOrDefault();
        }

        /// <summary>
        /// 根据id获取角色信息（修复bug，需要更新用户权限，不能走获取缓存）
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<RoleInfo> GetRoleByIdNoCacheAsync(int id)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.RoleInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.ID == id).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取医院角色
        /// </summary>
        /// <returns></returns>
        public async Task<List<RoleInfo>> GetAsync()
        {
            var datas = await GetCacheAsync() as List<RoleInfo>;
            if (datas != null)
            {
                return datas;
            }
            return null;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<RoleInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.RoleInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.RoleList.GetKey(_sessionCommonServer);
        }
    }
}
