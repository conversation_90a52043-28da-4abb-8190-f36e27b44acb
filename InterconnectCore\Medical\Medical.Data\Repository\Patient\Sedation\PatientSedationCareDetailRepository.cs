﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientSedationCareDetailRepository : IPatientSedationCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext;

        public PatientSedationCareDetailRepository(
              MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientSedationCareDetailInfo>> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientSedationCareDetailInfos.Where(m => m.PatientSedationCareMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AssessContentValue>> GetAssessValueByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientSedationCareDetailInfos
                .Where(m => m.PatientSedationCareMainID == mainID && m.DeleteFlag != "*")
                .Select(m => new AssessContentValue
                {
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }
    }
}