﻿using Medical.Data.Interface;
using Medical.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SendMessagesRepository : ISendMessagesRepository
    {
        //private readonly FilesDBContext _filesDBContext;
        //public SendMessagesRepository(FilesDBContext filesDBContext) {
        //    _filesDBContext = filesDBContext;
        //}

        //public async Task<List<SendMessageInfo>> GetUnreadMessagesAsync(DateTime startTime,DateTime endTime)
        //{
        //    return await _filesDBContext.SendMessageInfos.Where(m=>m.ReadFlag == false && m.AddTime >= startTime && m.AddTime <= endTime && m.DeleteFlag != "*").ToListAsync();
        //}

        //public async Task<List<SendMessageInfo>> GetUnreadMessagesAsync() {
        //    return await _filesDBContext.SendMessageInfos.Where(m => m.ReadFlag == false && m.DeleteFlag != "*").ToListAsync();
        //}

        //public async Task<bool> SaveMessages(List<SendMessageInfo> messages) {

        //    _filesDBContext.UpdateRange(messages);
        //    var count = await _filesDBContext.SaveChangesAsync();
        //    if (count < 0) {
        //        return false;
        //    }
        //    return true;
        //}
        public Task<List<SendMessageInfo>> GetUnreadMessagesAsync(DateTime startTime, DateTime endTime)
        {
            throw new NotImplementedException();
        }

        public Task<List<SendMessageInfo>> GetUnreadMessagesAsync()
        {
            throw new NotImplementedException();
        }

        public Task<bool> SaveMessages(List<SendMessageInfo> messages)
        {
            throw new NotImplementedException();
        }
    }
}
