﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DataTableEditListRepository : IDataTableEditListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public DataTableEditListRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<DataTableEditListInfo>> GetDataTableEditList(string tableName)
        {
            return await _medicalDbContext.DataTableEditListInfos.Where(m => m.DataPumpFlag != "*" && m.TableName == tableName).ToListAsync();
        }

        public async Task<bool> DelDataTableEditList()
        {
            var DataTableEditList = await _medicalDbContext.DataTableEditListInfos.Where(m => m.EditDateTime <= DateTime.Now.AddDays(-1)).ToListAsync();
            try
            {
                if (DataTableEditList.Count > 0)
                {
                    _medicalDbContext.DataTableEditListInfos.RemoveRange(DataTableEditList);
                    await _medicalDbContext.SaveChangesAsync();
                }
                return true;
            }
            catch (Exception)
            {
                return false;

            }
        }

        public async Task<List<DataTableEditListInfo>> GetDataTableEditAllList()
        {
            return await _medicalDbContext.DataTableEditListInfos.Where(m => m.DataPumpFlag != "*").ToListAsync();
        }

        public async Task<DataTableEditListInfo> GetDataTableEditByID(int id)
        {
            return await _medicalDbContext.DataTableEditListInfos.Where(m => m.ID == id).FirstOrDefaultAsync();
        }

        public async Task<DataTableEditListInfo> GetDataTableEditByClassAndInpatientID(string inpatientid, int fileClass)
        {
            return await _medicalDbContext.DataTableEditListInfos.Where(m => m.InpatientID == inpatientid && m.FileClass == fileClass).FirstOrDefaultAsync();
        }
        public async Task<List<DataTableEditListInfo>> GetDataTableEditClassList(string inpatientid, int fileClass)
        {
            if (inpatientid == "0" && fileClass == 0)
            {
                return await _medicalDbContext.DataTableEditListInfos.Where(m => m.DataPumpFlag != "*").ToListAsync();
            }

            if (inpatientid != "0" && fileClass == 0)
            {
                return await _medicalDbContext.DataTableEditListInfos.Where(m => m.InpatientID == inpatientid && m.DataPumpFlag != "*").ToListAsync();
            }

            if (inpatientid == "0" && fileClass != 0)
            {
                return await _medicalDbContext.DataTableEditListInfos.Where(m => m.FileClass == fileClass && m.DataPumpFlag != "*").ToListAsync();
            }

            if (inpatientid != "0" && fileClass != 0)
            {
                return await _medicalDbContext.DataTableEditListInfos.Where(m => m.DataPumpFlag != "*" && m.InpatientID == inpatientid && m.FileClass == fileClass).ToListAsync();
            }
            return new List<DataTableEditListInfo>();
        }

        public async Task<bool> CheckHasLog(DataTableEditListInfo data)
        {
            var query = await _medicalDbContext.DataTableEditListInfos.AnyAsync(m => m.StationID == data.StationID && m.InpatientID == data.InpatientID
                                                                 && m.FileClass == data.FileClass && m.RecordListID == data.RecordListID
                                                                 && m.SerialNumber == data.SerialNumber && m.DataPumpFlag != "*");

            return query;
        }
        /// <summary>
        /// 获取是否有异动记录
        /// </summary>
        /// <param name="inpatientids"></param>
        /// <returns></returns>
        public async Task<bool> GetHaveDataTableEditByInpatientids(List<string> inpatientids)
        {
            var data = await _medicalDbContext.DataTableEditListInfos.Where(m => inpatientids.Contains(m.InpatientID) && m.DataPumpFlag != "*").ToListAsync();
            return data.Count > 0 ? true : false;
        }

        public async Task<List<DataTableEditListView>> GetDataTableEditListInfosByInpatientID(string inpatientID)
        {
            var dataTableEditListInfos = await (from m in _medicalDbContext.DataTableEditListInfos
                                                where m.InpatientID == inpatientID
                                                select new DataTableEditListView
                                                {
                                                    ID = m.ID,
                                                    InpatientID = m.InpatientID,
                                                    FileClass = m.FileClass,
                                                    StationID = m.StationID,
                                                    RecordListID = m.RecordListID,
                                                    SerialNumber = m.SerialNumber,
                                                    EditDateTime = m.EditDateTime,
                                                    DataPumpFlag = m.DataPumpFlag == "*",
                                                    DataPumpData = m.DataPumpData,
                                                    TableName = m.TableName,
                                                    IsBackupFlag = false
                                                }).OrderByDescending(m => m.EditDateTime).ToListAsync();
            return dataTableEditListInfos;
        }

        public async Task<List<DataTableEditListView>> GetDataTableEditListBakInfosByInpatientID(string inpatientID)
        {
            var dataTableEditListBakInfos = await (from m in _medicalDbContext.DataTableEditListBakInfos
                                                   where m.InpatientID == inpatientID
                                                   select new DataTableEditListView
                                                   {
                                                       ID = m.ID,
                                                       InpatientID = m.InpatientID,
                                                       FileClass = m.FileClass,
                                                       StationID = m.StationID,
                                                       RecordListID = m.RecordListID,
                                                       EditDateTime = m.EditDateTime,
                                                       DataPumpFlag = m.DataPumpFlag == "*",
                                                       DataPumpData = m.DataPumpData,
                                                       TableName = m.TableName,
                                                       IsBackupFlag = true
                                                   }).OrderByDescending(m => m.EditDateTime).ToListAsync();

            return dataTableEditListBakInfos;
        }
    }
}
