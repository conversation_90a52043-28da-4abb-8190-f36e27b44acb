﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DynamicColumnAttributeRepository : IDynamicColumnAttributeRepository
    {
        private MedicalDbContext _medicalDbContext;
        public DynamicColumnAttributeRepository(
             MedicalDbContext medicalDbContext
            )
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据表格ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        public async Task<List<DynamicColumnAttributeInfo>> GetListByID(int dynamicTableListID, string hospitalID)
        {
            return await _medicalDbContext.DynamicColumnAttributeInfos.Where(m => m.DynamicTableListID == dynamicTableListID && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据用户ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<List<DynamicColumnAttributeInfo>> GetListByUserID(int dynamicTableListID, string userID, string hospitalID)
        {
            return await _medicalDbContext.DynamicColumnAttributeInfos.Where(m => m.DynamicTableListID == dynamicTableListID && m.HospitalID == hospitalID && m.UserID == userID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
