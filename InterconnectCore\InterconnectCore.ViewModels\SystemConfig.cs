﻿using System.ComponentModel;

namespace InterconnectCore.ViewModels
{
    /// <summary>
    /// 配置参数()
    /// </summary>
    public class SystemConfig
    {

        /// <summary>
        /// 医院代码
        /// </summary>
        [Description("医院代码")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        [Description("语言")]
        public int Language { get; set; }
        /// <summary>
        /// 服务器类型
        /// </summary>
        public int ServerType { get; set; }

        /// <summary>
        /// 默认系统操作人
        /// </summary>
        public string SystemOperator { get; set; }

        /// <summary>
        /// 白名单
        /// </summary>
        public string WhiteListIPs { get; set; }

        ///// <summary>
        ///// 默认客户端类型
        ///// </summary>
        public int PCClientType { get; set; }


        ///// <summary>
        /////  写负载均衡服务器节点Session
        ///// </summary>
        public string SetNodeServerLoginSessionAPI { get; set; }
        /// <summary>
        /// 根据Token获取负载均衡服务器节点Session
        /// </summary>
        public string GetNodeServerLoginSessionAPI { get; set; }
        /// <summary>
        /// 获取负载均衡服务器节点所有Session
        /// </summary>
        public string GetNodeServerLoginAllSessionAPI { get; set; }
        /// <summary>
        /// 服务器IP，及端口
        /// </summary>
        public string ServerIPAddress { get; set; }
        /// <summary>
        /// 是否记录日志
        /// </summary>
        public bool RecordLog { get; set; }

        /// <summary>
        /// 系统使用缓存类型
        /// </summary>
        public string UseCacheType { get; set; }
        /// <summary>
        /// 环境状态 1:正式环境 2:测试环境
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 调用medicalAPI
        /// </summary>
        public string SyncMedicalApi { get; set; }
        /// <summary>
        /// 调用DataInterfaceAPI
        /// </summary>
        public string DataInterfaceAPI { get; set; }
    }
}