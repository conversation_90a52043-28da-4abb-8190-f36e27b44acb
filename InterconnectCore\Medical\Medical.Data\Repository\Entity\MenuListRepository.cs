﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class MenuListRepository : IMenuListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public MenuListRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<MenuListInfo>> GetMenuListAsync(string system, string clientType)
        {
            var list = await GetCacheAsync() as List<MenuListInfo>;
            list = list.Where(t => t.System.Trim() == system && t.ClientType.Trim() == clientType).ToList();
            return list;
        }
        public async Task<string> GetMenuName(string system, string router, string clientType)
        {
            var list = await GetCacheAsync() as List<MenuListInfo>;
            var result = list.Find(t => t.System.Trim() == system && t.Router.Trim() == router && t.ClientType.Trim() == clientType);
            if (result != null)
            {
                return result.MenuName;
            }
            return "";
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<MenuListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.MenuListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.MenuList.GetKey(_sessionCommonServer);
        }
        public async Task<MenuListInfo> GetMenuByRouter(string system, string router, string clientType)
        {
            var list = await GetCacheAsync() as List<MenuListInfo>;
            return list.Where(m => m.Router == router && m.System.Trim() == system && m.ClientType.Trim() == clientType).FirstOrDefault();
        }
    }
}
