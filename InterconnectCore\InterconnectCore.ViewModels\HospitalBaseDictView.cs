﻿using Medical.Models;

namespace InterconnectCore.ViewModels
{
    public class HospitalBaseDictView
    {
        /// <summary>
        /// 病区字典
        /// </summary>
        public List<StationListInfo> StationList { get; set; }
        /// <summary>
        /// 科室字典
        /// </summary>
        public List<DepartmentListInfo> DepartmentList { get; set; }
        /// <summary>
        /// 床位字典
        /// </summary>
        public List<BedListInfo> BedList { get; set; }

    }
}