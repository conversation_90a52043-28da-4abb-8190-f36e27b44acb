﻿using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IPatientTestService
    {
        /// <summary>
        /// 同步患者检验信息
        /// </summary>
        /// <param name="patientTestResultViewList"></param>
        /// <returns></returns>
        Task<bool> SyncPatientTestResults(List<PatientTestResultView> patientTestResultViewList);
    }
}
