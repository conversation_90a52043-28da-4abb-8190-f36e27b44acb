﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Medical.ViewModels.View.Patient.Delivery;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDeliveryRecordRepository : IPatientDeliveryRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientDeliveryRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据D获取对应数据
        /// </summary>
        /// <param name="id">分娩记录ID</param>
        /// <returns></returns>
        public async Task<PatientDeliveryRecordInfo> GetByID(string id)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.PatientDeliveryRecordID == id && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }


        /// <summary>
        /// 根据RecordID获取对应数据
        /// </summary>
        /// <param name="id">分娩记录ID</param>
        /// <returns></returns>
        public async Task<PatientDeliveryRecordInfo> GetByRecordID(string id)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.PatientDeliveryRecordID == id).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取患者所有分娩记录
        /// </summary>
        /// <param name="inpatientID">患者在院ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryRecordInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取主记录View
        /// </summary>
        /// <param name="inpatientID">患者在院ID</param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryRecordRecordView>> GetViewByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*")
                .Select(m => new PatientDeliveryRecordRecordView
                {
                    PatientDeliveryRecordID = m.PatientDeliveryRecordID,
                    InpatientID = m.InpatientID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    PregnantTimes = m.Parity.ToString(),
                    DeliveryTimes = m.DeliveryTimes,
                    PregnantWeeks = m.PregnantWeeks,
                    PregnantDays = m.PregnantDays,
                    EDC = m.EDC,
                    ContractionStartedTime = m.ContractionStartedTime,
                    FullyDilatedTime = m.FullyDilatedTime,
                    DeliveryStartTime = m.DeliveryStartTime,
                    ThirdStageCompletedTime = m.ThirdStageCompletedTime,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    UserID = m.AddEmployeeID,
                }).ToListAsync();
        }

        public async Task<PatientDeliveryRecordInfo> GetLastByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*")
                .OrderByDescending(m => m.Parity).ThenByDescending(m => m.DeliveryTimes).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取交班使用数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<HandoverDeliveryCareIntervention>> GetPatientDeliveryIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientDeliveryCareMainInfos
                               join b in _medicalDbContext.PatientDeliveryRecordInfos on a.PatientDeliveryRecordID equals b.PatientDeliveryRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverDeliveryCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   PatientDeliveryRecordID = a.PatientDeliveryRecordID
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientDeliveryRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }

        public async Task<List<string>> GetUnEndInpatientID(List<int> departmentListIDs)
        {
            return await (from m in _medicalDbContext.InpatientDatas.Where(m => departmentListIDs.Contains(m.DepartmentListID)
                          && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*")
                          join n in _medicalDbContext.PatientDeliveryRecordInfos.Where(m => m.DeleteFlag != "*") on m.ID equals n.InpatientID
                          into temp
                          from o in temp.DefaultIfEmpty()
                          where o.ThirdStageCompletedTime == null
                          select m.ID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 获取阴道分娩记录CDA数据
        /// </summary>
        /// <param name="hospitalID">医院序号</param>
        /// <param name="lastTranslateDate">最后同步时间</param>
        /// <returns></returns>
        public async Task<List<CDA_VaginalDeliveryRecView>> GetCDAVaginalDeliveryRecViewAsync(string hospitalID, DateTime lastTranslateDate)
        {
            var query = from record in _medicalDbContext.PatientDeliveryRecordInfos.Where(m => m.ThirdStageCompletedTime.HasValue)
                        join inpatient in _medicalDbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID) on record.InpatientID equals inpatient.ID
                        join patientBasic in _medicalDbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on record.PatientID equals patientBasic.PatientID
                        where record.ModifyDate.HasValue && record.ModifyDate > lastTranslateDate
                        select new CDA_VaginalDeliveryRecView
                        {
                            DCID = record.PatientDeliveryRecordID,
                            PatientID = record.PatientID,
                            PatientType = "04",
                            InpatientID = record.InpatientID,
                            CaseNumber = record.CaseNumber,
                            ChartNo = record.ChartNo,
                            VisitID = record.CaseNumber,
                            EffectiveFlag = record.DeleteFlag == "*" ? "0" : "1",
                            IdCard = patientBasic.IdentityID,
                            VisitDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                            //ContactPerson =  ,
                            //ContactPersonTel = ,
                            HospizationId = record.CaseNumber,
                            Name = patientBasic.PatientName,
                            BirthDate = patientBasic.DateOfBirth,
                            Age = inpatient.Age.HasValue ? inpatient.Age.ToString() : "", //此处会出现问题，之后处理
                            //SickRoomId = "",
                            SickbedId = record.BedNumber,
                            PregnancyNumber = record.DeliveryTimes,
                            DeliveryNumber = record.DeliveryTimes,
                            CDC = record.EDC,
                            AwaitingDeliveryDateTime = record.ContractionStartedTime,
                            AdmissionDiagnose = inpatient.Diagnosis,
                            FetalChildbirthTime = record.DeliveryStartTime,
                            PlacentalDeliveryTime = record.ThirdStageCompletedTime,
                            TimeStamp = record.ModifyDate ?? record.AddDate,
                            //附加对象
                            FullyDilatedTime = record.FullyDilatedTime,
                            ContractionStartedTime = record.ContractionStartedTime,
                            DeliveryStartTime = record.DeliveryStartTime,
                            DepartmentListID = record.DepartmentListID,
                            StationID = record.StationID,

                            ModifyPersonID = record.ModifyPersonID,

                        };
            return await query.OrderBy(m => m.TimeStamp).ToListAsync();
        }

        /// <summary>
        /// 判断病人产时记录的部分字段 id stationid inpatientid
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryRecordInfo>> GetPartFieldByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new PatientDeliveryRecordInfo
                {
                    InpatientID = m.InpatientID,
                    PatientDeliveryRecordID = m.PatientDeliveryRecordID,
                    StationID = m.StationID
                }).ToListAsync();
        }
        /// <summary>
        /// 获取病人最新的Record孕产信息
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<PatientPregnancyView> GetLastRecordViewByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliveryRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AddDate).Select(m => new PatientPregnancyView
                {
                    Parity = m.Parity,
                    DeliveryTimes = m.DeliveryTimes,
                    IsDeliveryEnd = m.ThirdStageCompletedTime.HasValue
                }).FirstOrDefaultAsync();

        }
        /// <summary>
        /// 获取患者分娩记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientDeliveryRecordInfo>> GetByInpatientIDOrRecordID(string inpatientID, string recordID = null)
        {
            var list = await _medicalDbContext.PatientDeliveryRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
            if (string.IsNullOrEmpty(recordID))
            {
                return list;
            }
            return list.Where(m => m.PatientDeliveryRecordID == recordID).ToList();
        }
        ///<summary>
        /// 根据一段时间内的产时主记录生成CDA数据
        /// </summary>
        /// <param name="startDateTime">异动时间（开始）</param>
        /// <param name="endDateTime">异动时间（结束）</param>
        /// <returns></returns>
        public async Task<CDA_PreDeliveryRecordInfo[]> GetDeliveryRecordsToCDA(DateTime startDateTime, DateTime? endDateTime)
        {
            var query = from record in _medicalDbContext.PatientDeliveryRecordInfos
                        join inpatient in _medicalDbContext.InpatientDatas.Where(m => m.DeleteFlag != "*") on record.InpatientID equals inpatient.ID
                        join patientBasic in _medicalDbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on record.PatientID equals patientBasic.PatientID
                        where record.ModifyDate >= startDateTime && (!endDateTime.HasValue || record.ModifyDate <= endDateTime)
                        select new CDA_PreDeliveryRecordInfo
                        {
                            DCID = record.PatientDeliveryRecordID,
                            PatientType = "04",
                            InpatientID = record.InpatientID,
                            PatientID = record.PatientID,
                            CaseNumber = record.CaseNumber,
                            ChartNo = record.ChartNo,
                            Name = patientBasic.PatientName,
                            BirthDate = patientBasic.DateOfBirth,
                            Age = inpatient.Age.HasValue ? inpatient.Age.ToString() : "",
                            IdCard = patientBasic.IdentityID,
                            DepartmentCode = record.DepartmentListID.ToString(),
                            SickBedId = record.BedNumber,
                            PregnancyNumber = record.DeliveryTimes,
                            DeliveryNumber = record.DeliveryTimes,
                            ModifyDateTime = record.ModifyDate ?? DateTime.Now,
                            ModifyPersonID = record.ModifyPersonID,
                            DataPumpFlag = "",
                            DataStatus = CdaDataStatus.Add,
                            DeleteFlag = ""
                        };
            return await query.ToArrayAsync();
        }
    }
}
