﻿/*
 2022-01-10 2225 配合用户故世观察模版要可以对应评估模版同时多语言版本新增字段语言及表单编号 -正元
 2022-01-10      观察措施模版改为走缓存  -正元
 */

using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ObserveTemplateRepository : IObserveTemplateRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public ObserveTemplateRepository(MedicalDbContext db
               , IMemoryCache memoryCache
               , SessionCommonServer sessionCommonServer
               , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ObserveTemplateInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.ObserveTemplateInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        #region 缓存接口实现
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.ObserveTemplate.GetKey(_sessionCommonServer);
        }

        public void UpdateCache()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
        #endregion

        /// <summary>
        ///根据主键ID获取数据
        /// </summary>
        /// <param name="observeTemplateID"></param>
        /// <returns></returns>
        public async Task<ObserveTemplateInfo> GetByID(string observeTemplateID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.ObserveTemplateID == observeTemplateID).FirstOrDefault();
        }

        /// <summary>
        /// 根据主键集合获取数据
        /// </summary>
        /// <param name="observeTemplateIDs"></param>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetByIDs(List<string> observeTemplateIDs)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.ObserveTemplateInfos.Where(m => m.Language == language && m.HospitalID == hospitalID
            && m.DeleteFlag != "*" && observeTemplateIDs.Contains(m.ObserveTemplateID)).ToListAsync();
        }

        /// <summary>
        /// 取得模版缓存
        /// </summary>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<ObserveTemplateInfo>;
        }

        /// <summary>
        /// 根据templateName获取数据
        /// </summary>
        /// <param name="templateName"></param>
        /// <returns></returns>
        public async Task<ObserveTemplateInfo> GetByTemplateName(string templateName)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.TemplateName == templateName).FirstOrDefault();
        }

        /// <summary>
        /// 根据科室ID获取模板
        /// </summary>
        /// <param name="departmentListID">科室ID</param>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetByDepartmentListID(int departmentListID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.DepartmentListID == departmentListID || m.DepartmentListID == 999).OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 根据科室ID及模板类别获取观察模版
        /// </summary>
        /// <param name="departmentListID">科室ID</param>
        /// <param name="templateCategory">模板类别</param>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetAsync(int departmentListID, string templateCategory)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.DepartmentListID == departmentListID && m.TemplateCategory == templateCategory).OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 根据科室集合获取数据
        /// </summary>
        /// <param name="departmentListIDArr">科室集合</param>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetTemplateByDepartmentListArr(List<int> departmentListIDArr)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.StationListID == null && m.DepartmentListID != null && departmentListIDArr.Contains(m.DepartmentListID.Value)).OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 根据病区获取数据
        /// </summary>
        /// <param name="stationListID">病区ID</param>
        /// <returns></returns>
        public async Task<List<ObserveTemplateInfo>> GetTemplateByStationListID(int stationListID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.StationListID == stationListID).OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 取得指定科室的指定观察措施模版ID
        /// </summary>
        /// <param name="departmentListID">科室ID</param>
        /// <param name="recordsCode">记录码</param>
        /// <returns></returns>
        public async Task<string> GetObserveTemplateIDByDepartmentListIDAndRecordsCode(int departmentListID, string recordsCode)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.DepartmentListID == departmentListID && m.RecordsCode == recordsCode).Select(m => m.ObserveTemplateID).FirstOrDefault();
        }

        /// <summary>
        /// 根据模板名称及科室ID获取观察模板
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="deptID">科室ID</param>
        /// <returns></returns>
        public async Task<ObserveTemplateInfo> GetIDByTemplateName(string templateName, int deptID)
        {
            var datas = await GetAsync();

            return datas.Where(m => m.TemplateName == templateName && m.DepartmentListID == deptID).FirstOrDefault();
        }
        /// <summary>
        /// 根据observeTemplateID获取数据，可更改
        /// </summary>
        /// <param name="observeTemplateID"></param>
        /// <returns></returns>
        public async Task<ObserveTemplateInfo> GetByIDModdify(string observeTemplateID)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.ObserveTemplateInfos.Where(m => m.Language == language && m.HospitalID == hospitalID
            && m.DeleteFlag != "*" && m.ObserveTemplateID == observeTemplateID).FirstOrDefaultAsync();
        }
    }
}