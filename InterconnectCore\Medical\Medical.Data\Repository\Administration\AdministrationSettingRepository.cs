﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AdministrationSettingRepository : IAdministrationSettingRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public AdministrationSettingRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AdministrationSettingInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var data = await _medicalDbContext.AdministrationSettingInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AdministrationSetting.GetKey(_sessionCommonServer);
        }

        public async Task<AdministrationSettingInfo> GetAsync(string settingTypeCode, string settingType, string typeValue)
        {
            var data = (List<AdministrationSettingInfo>)(await GetCacheAsync());
            return data.Find(t => t.SettingTypeCode == settingTypeCode && t.SettingType == settingType
                && t.TypeValue == typeValue);
        }

        public async Task<List<AdministrationSettingInfo>> GetBySelect(string SettingTypeCode, string SettingType)
        {
            var data = (List<AdministrationSettingInfo>)(await GetCacheAsync());

            return data.Where(m => m.SettingType == SettingType && m.SettingTypeCode == SettingTypeCode).ToList();
        }

        public async Task<List<AdministrationSettingInfo>> GetBySelect()
        {
            return (List<AdministrationSettingInfo>)(await GetCacheAsync());
        }

        public async Task<List<AdministrationSettingInfo>> GetSelectBySettingTypeCode(string settingTypeCode)
        {
            var data = (List<AdministrationSettingInfo>)(await GetCacheAsync());

            return data.Where(m => m.SettingTypeCode == settingTypeCode).ToList();
        }
    }
}