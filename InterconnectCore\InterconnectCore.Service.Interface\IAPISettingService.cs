﻿using Medical.ViewModels;

namespace InterconnectCore.Service.Interface
{
    public interface IAPISettingService
    {
        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="serverCode"></param>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<string> GetSettingBySettingCode(string serverCode, string settingCode);
        /// <summary>
        /// 获取API地址
        /// </summary>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<ApiUrlView> GetAPIAddressByCode(string settingCode);

        /// <summary>
        /// 获取服务器地址
        /// </summary>
        /// <param name="serverCode">服务器码</param>
        /// <returns></returns>
        Task<string> GetServerURL(string serverCode);
    }
}
