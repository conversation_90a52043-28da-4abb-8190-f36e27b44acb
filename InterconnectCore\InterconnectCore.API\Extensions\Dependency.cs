using InterconnectCore.Data.Interface;
using InterconnectCore.Data.Repository;
using InterconnectCore.Service;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.Services.Interface;
using Medical.Common;
using Medical.Data;
using Medical.Data.Interface;
using Medical.Data.Repository;
using MedicalExternalCommon.Service;

namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// 依赖
    /// </summary>
    public static class Dependency
    {

        /// <summary>
        /// 配置DI
        /// </summary>
        /// <param name="services"></param>
        /// <param name="name"></param>
        /// <param name="jsonFile"></param>
        public static void SetDI(this IServiceCollection services, string name, string jsonFile = null)
        {
            SetDI(services);//以编码方式进行注入
        }
        private static void SetDI(IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            services.AddScoped<IAPISettingRepository, APISettingRepository>();
            services.AddScoped<IAPISettingService, APISettingService>();
            services.AddScoped<IDictionaryService, DictionaryService>();
            services.AddScoped<IBedListRepository, BedListRepository>();
            services.AddScoped<SessionCommonServer>();
            services.AddScoped<IStationListRepository, StationListRepository>();
            services.AddScoped<CommonHelper>();
            services.AddScoped<SessionCommonServer>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IUserRoleRepository, UserRoleRepository>();
            services.AddScoped<IEmployeeDepartmentSwitchRepository, EmployeeDepartmentSwitchRepository>();
            services.AddScoped<IAppConfigSettingRepository, AppConfigSettingRepository>();
            services.AddScoped<IAppConfigSettingService, AppConfigSettingService>();
            services.AddScoped<IBedListRepository, BedListRepository>();
            services.AddHttpContextAccessor();
            services.AddScoped<IInpatientService, InpatientService>();
            services.AddScoped<ExternalCommonService>();
            services.AddScoped<ExternalProfileCommonService>();
            services.AddScoped<MQCommonService>();
            services.AddScoped<PatientEventCommonService>();
            services.AddScoped<PatientProfileMarkService>();
            services.AddScoped<StationaShiftCommonService>();
            services.AddScoped<ISyncLogService, SyncLogService>();
            services.AddScoped<ILogInfoServices, LogInfoServices>();
            services.AddScoped<IPatientBasicService, PatientBasicService>();
            services.AddScoped<ILogInfoRepository, LogInfoRepository>();
            services.AddScoped<IPatientBasicRepository, PatientBasicRepository>();
            services.AddScoped<ISettingDescRepository, SettingDescRepository>();
            services.AddScoped<IInpatientDataRepository, InpatientDataRepository>();
            services.AddScoped<IStationListRepository, StationListRepository>();
            services.AddScoped<IStationShiftRepository, StationShiftRepository>();
            services.AddScoped<ISettingDescriptionRepository, SettingDescriptionRepository>();
            services.AddScoped<IPatientBasicDataRepository, PatientBasicDataRepository>();
            services.AddScoped<IInpatientDataLogRepository, InpatientDataLogRepository>();
            services.AddScoped<IInpatientLogRepository, InpatientLogRepository>();
            services.AddScoped<IDepartmentListRepository, DepartmentListRepository>();
            services.AddScoped<IPatientListIconRepository, PatientListIconRepository>();
            services.AddScoped<IEventSettingRepository, EventSettingRepository>();
            services.AddScoped<IPatientEventRepository, PatientEventRepository>();
            services.AddScoped<IPatientProfileMarkRepository, PatientProfileMarkRepository>();
            services.AddScoped<IPatientProfileRepository, PatientProfileRepository>();
            services.AddScoped<INursingClusterOrderRepository, NursingClusterOrderRepository>();
            services.AddScoped<IICDToAssessRepository, ICDToAssessRepository>();
            services.AddScoped<IOrderToAssessListRepository, OrderToAssessListRepository>();
            services.AddScoped<IHandoverKeySignSettingRepository, HandoverKeySignSettingRepository>();
            services.AddScoped<IDepartmentListRepository, DepartmentListRepository>();
            services.AddScoped<IStationToDeptInfoRepository, StationToDeptInfoRepository>();
            services.AddScoped<IAPISettingRepository, APISettingRepository>();
            services.AddScoped<GetCacheService>();
            services.AddScoped<ITestItemToAssessListRepository, TestItemToAssessListRepository>();
            services.AddScoped<IPatientTestResultInfoRepository, PatientTestResultInfoRepository>();
            services.AddScoped<IPatientAllergyRepository, PatientAllergyRepository>();
            services.AddScoped<IAllergyBasicRepository, AllergyBasicRepository>();
            services.AddScoped<IClinicDataRepository, ClinicDataRepository>();
            services.AddScoped<IPatientDiagnosisRepository, PatientDiagnosisRepository>();
            services.AddScoped<IRequestApiService, RequestApiService>();
            services.AddScoped<IPatientTestService, PatientTestService>();
            services.AddScoped<IPatientAllergyService, PatientAllergyService>();
            services.AddScoped<IPatientDiagnosisService, PatientDiagnosisService>();
            services.AddScoped<IDrugListRepository, DrugListRepository>();
        }
    }
}