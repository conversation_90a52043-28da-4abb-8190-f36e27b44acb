﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientCRRTRecordRepository : IPatientCRRTRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientCRRTRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据主记录ID获取主记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientCRRTRecordInfo> GetRecordInfoByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTRecordInfos.Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*")
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取主记录ByInpatientID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<CRRTRecordView>> GetRecordInfosByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientCRRTRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new CRRTRecordView
                {
                    PatientCRRTRecordID = m.PatientCRRTRecordID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    UserID = m.AddEmployeeID,
                    AdmissionBodyWeight = m.AdmissionBodyWeight,
                    PreTreatmentWeight = m.PreTreatmentWeight,
                    TreatmentMode = m.TreatmentMode,
                    DateOfTreatment = m.DateOfTreatment,
                    TimesOfTreatment = m.TimesOfTreatment,
                    AnticoagulationType = m.AnticoagulationType,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    TotalUltrafiltrationVolume = m.TotalUltrafiltrationVolume,
                    AnticoagulationDosage = m.AnticoagulationDosage,
                    CRRTDurationOfTherapy = m.CRRTDurationOfTherapy,
                    CRRTDurationOfTherapyTime = m.CRRTDurationOfTherapyTime,
                    DialysateVolume = m.DialysateVolume,
                    Others = m.Others,
                    ReplacementVolume = m.ReplacementVolume
                }).ToListAsync();
        }
        public async Task<List<CRRTRecordView>> GetRecordByCaseNumber(string caseNumber)
        {
            return await _medicalDbContext.PatientCRRTRecordInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*")
                .Select(m => new CRRTRecordView
                {
                    PatientCRRTRecordID = m.PatientCRRTRecordID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    UserID = m.AddEmployeeID,
                    AdmissionBodyWeight = m.AdmissionBodyWeight,
                    PreTreatmentWeight = m.PreTreatmentWeight,
                    TreatmentMode = m.TreatmentMode,
                    DateOfTreatment = m.DateOfTreatment,
                    TimesOfTreatment = m.TimesOfTreatment,
                    AnticoagulationType = m.AnticoagulationType,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    TotalUltrafiltrationVolume = m.TotalUltrafiltrationVolume,
                    AnticoagulationDosage = m.AnticoagulationDosage,
                    CRRTDurationOfTherapy = m.CRRTDurationOfTherapy,
                    CRRTDurationOfTherapyTime = m.CRRTDurationOfTherapyTime,
                    DialysateVolume = m.DialysateVolume,
                    Others = m.Others,
                    ReplacementVolume = m.ReplacementVolume
                }).ToListAsync();
        }
        /// <summary>
        /// 获取病人所有的主记录ID
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordIDsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientCRRTRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .OrderBy(m => m.StartDate).ThenBy(m => m.StartTime).Select(m => m.PatientCRRTRecordID).ToListAsync();
        }
        /// <summary>
        /// 根据recordID获取View
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<CRRTRecordView> GetRecordViewByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTRecordInfos.Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*")
                .Select(m => new CRRTRecordView
                {
                    PatientCRRTRecordID = m.PatientCRRTRecordID,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    UserID = m.AddEmployeeID,
                    AdmissionBodyWeight = m.AdmissionBodyWeight,
                    PreTreatmentWeight = m.PreTreatmentWeight,
                    TreatmentMode = m.TreatmentMode,
                    DateOfTreatment = m.DateOfTreatment,
                    TimesOfTreatment = m.TimesOfTreatment,
                    AnticoagulationType = m.AnticoagulationType,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    TotalUltrafiltrationVolume = m.TotalUltrafiltrationVolume,
                    AnticoagulationDosage = m.AnticoagulationDosage,
                    CRRTDurationOfTherapy = m.CRRTDurationOfTherapy,
                    CRRTDurationOfTherapyTime = m.CRRTDurationOfTherapyTime,
                    DialysateVolume = m.DialysateVolume,
                    Others = m.Others,
                    ReplacementVolume = m.ReplacementVolume
                }).FirstOrDefaultAsync();
        }
    }
}