﻿/*
 * 20220-04-26 2266 CDA护理计划数据推送新增GetSyncData -En
 */

using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_NursingPlanRepository : ICDA_NursingPlanRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_NursingPlanRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<CDA_NursingPlansInfo> GetByID(string handOverID)
        {
            return await _cDADBConnect.CDA_NursingPlansInfos.Where(m => m.DCID == handOverID).FirstOrDefaultAsync();
        }

        public async Task<DateTime?> GetLast()
        {
            var data = await _cDADBConnect.CDA_NursingPlansInfos.Select(m => m.TimeStamp)
                .OrderByDescending(m => m).FirstOrDefaultAsync();

            if (data == null)
            {
                return null;
            }

            return data.Value;
        }

        public async Task<List<CDA_NursingPlansInfo>> GetSyncData(DateTime? startDateTime, DateTime? endDateTime)
        {
            if (!startDateTime.HasValue)
            {
                startDateTime = await _cDADBConnect.CDA_NursingPlansInfos.Where(m => m.DataPumpFlag != "*").MinAsync(m => m.TimeStamp);
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = startDateTime.Value.AddDays(1);
            }
            // 开始结束传的是同一天，则取这一天（24小时）的数据
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }
            return await _cDADBConnect.CDA_NursingPlansInfos.Where(m => m.DataPumpFlag != "*"
            && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime).ToListAsync();
        }


        public async Task<bool> Save(CDA_NursingPlansInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA护理计划写入数据失败" + ex.Message + ",异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }

        public async Task<bool> Update(CDA_NursingPlansInfo data)
        {
            var old = await _cDADBConnect.CDA_NursingPlansInfos.Where(m => m.DCID == data.DCID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA护理计划更新数据失败" + ex.Message + ",异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }
    }
}
