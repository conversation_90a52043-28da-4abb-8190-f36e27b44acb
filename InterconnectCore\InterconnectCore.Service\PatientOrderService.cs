﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;
using System.Linq;

namespace InterconnectCore.Service
{
    public class PatientOrderService : IPatientOrderService
    {
        #region 引用
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IStationListRepository _stationListRepository;
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IOrderToAssessListRepository _orderToAssessListRepository;
        private readonly IFrequencyRepository _frequencyRepository;
        private readonly IPhysicianOrderRepository _physicianOrderRepository;
        private readonly IEventSettingRepository _eventSettingRepository;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly IPatientOrderRepository _patientOrderRepository;
        private readonly IPatientOrderDetailRepository _patientOrderDetailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly PatientEventCommonService _patientEventCommonService;
        private readonly CommonHelper _commonHelper;
        private readonly IPatientScheduleMainRepository _patientScheduleMainRepository;
        private readonly IUserRepository _userRepository;
        private readonly IPatientMedicineScheduleRepository _patientMedicineScheduleRepository;
        private readonly MedicalDbContext _medicalDbContext;
        #endregion

        #region 常量

        /// <summary>
        /// 停止医嘱状态
        /// </summary>
        private const int ORDER_STATUS_STOP = 3;
        /// <summary>
        /// 作废医嘱状态
        /// </summary>
        private const int ORDER_STATUS_CANCEL = 4;
        /// <summary>
        /// 修改人ID
        /// </summary>
        private const string PERSONID_TONGBU = "Tongbu";
        /// <summary>
        /// 死亡事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DEATH = 2876;
        /// <summary>
        /// 出院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DISCHARGE = 2873;
        /// <summary>
        /// 死亡事件的LogCode
        /// </summary>
        private const string LOGCODE_DEATH = "Death";
        /// <summary>
        /// 
        /// </summary>
        private const string STRING_EMPTY = "";
        #endregion

        #region 构造器
        public PatientOrderService(IStationListRepository stationListRepository
            , IOptions<ViewModels.SystemConfig> config
            , IInpatientDataRepository inpatientDataRepository
            , IOrderToAssessListRepository orderToAssessListRepository
            , IFrequencyRepository frequencyRepository
            , IPhysicianOrderRepository physicianOrderRepository
            , IEventSettingRepository eventSettingRepository
            , IClinicSettingRepository clinicSettingRepository
            , IPatientOrderRepository patientOrderRepository
            , IPatientOrderDetailRepository patientOrderDetailRepository
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IPatientEventRepository patientEventRepository
            , PatientEventCommonService patientEventCommonService
            , CommonHelper commonHelper
            , IPatientScheduleMainRepository patientScheduleMainRepository
            , IUserRepository userRepository
            , IPatientMedicineScheduleRepository patientMedicineScheduleRepository
            , MedicalDbContext medicalDbContext
            )
        {
            _stationListRepository = stationListRepository;
            _config = config;
            _inpatientDataRepository = inpatientDataRepository;
            _orderToAssessListRepository = orderToAssessListRepository;
            _frequencyRepository = frequencyRepository;
            _physicianOrderRepository = physicianOrderRepository;
            _eventSettingRepository = eventSettingRepository;
            _clinicSettingRepository = clinicSettingRepository;
            _patientOrderRepository = patientOrderRepository;
            _patientOrderDetailRepository = patientOrderDetailRepository;
            _unitOfWork = unitOfWork;
            _patientEventRepository = patientEventRepository;
            _patientEventCommonService = patientEventCommonService;
            _commonHelper = commonHelper;
            _patientScheduleMainRepository = patientScheduleMainRepository;
            _userRepository = userRepository;
            _patientMedicineScheduleRepository = patientMedicineScheduleRepository;
            _medicalDbContext = medicalDbContext;
        }
        #endregion

        /// <summary>
        /// 同步患者医嘱数据
        /// </summary>
        /// <param name="commonOrders">患者医嘱数据</param>
        /// <returns></returns>
        public async Task<bool> SyncPatientOrderAsync(List<CommonOrderView> commonOrders)
        {
            _logger.Info($"SyncPatientOrderAsync方法入参commonOrders=>{Common.ListToJson.ToJson(commonOrders)}");
            // 字典加载
            var (isSuccess, inpatient, orderToAssessList, frequencyList, physicianOrder, eventSettings, stationList, orderKeyWords
                ) = await GetSyncPatientOrderAsyncParams(commonOrders[0].CaseNumber);
            if (!isSuccess) return false;

            try
            {
                commonOrders = FormatOrderKeyWord(commonOrders, orderKeyWords);
                if (commonOrders.Count == 0)
                {
                    _logger.Warn("SyncPatientOrderAsync方法筛选后没有需要同步的医嘱数据orderID=>{commonOrders.FirstOrDefault().OrderID}");
                    return false;
                }
                //获取患者医嘱主从表内容           
                var orderIDs = commonOrders.Select(m => m.OrderGroupID).Distinct().ToList();
                var existOrderMains = await _patientOrderRepository.GetByInpatientIDAndOrderIDAsync(inpatient.ID, orderIDs);
                // 医嘱主表明细表处理
                var (patientOrderMains, patientOrderDetails) = await SetPatientOrderMainAndDetalis(commonOrders, existOrderMains, inpatient, frequencyList, physicianOrder, stationList);
                var (patientOrderDetailToEvent, patientOrderDetailToProfile) = SplitOrderDetails(patientOrderDetails, orderToAssessList, eventSettings);
                _logger.Info($"SyncPatientOrderAsync方法开始处理患者Profile数据,patientOrderDetailToProfile=>{Common.ListToJson.ToJson(patientOrderDetailToEvent)}");
                _logger.Info($"SyncPatientOrderAsync方法开始处理患者Profile数据,patientOrderDetailToProfile=>{Common.ListToJson.ToJson(patientOrderDetailToProfile)}");
                // Profile处理
                if (patientOrderDetailToProfile.Count > 0)
                {
                    await SetPatientProfiles(patientOrderMains, orderToAssessList, patientOrderDetailToProfile);
                }
                // 患者事件处理
                if (patientOrderDetailToEvent.Count > 0)
                {
                    await SetPatientEvents(inpatient, patientOrderMains, orderToAssessList, eventSettings, patientOrderDetailToEvent);
                }
                // 排程处理
                await SetPatientSchedule(commonOrders, patientOrderMains, inpatient);
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"SyncPatientOrderAsync方法同步患者医嘱数据失败,,orderID=>{commonOrders.FirstOrDefault().OrderID},错误信息=>{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 拆分医嘱明细
        /// </summary>
        /// <param name="orderToAssessList">医嘱对评估字典</param>
        /// <param name="newOrderDetails">医嘱明细</param>
        /// <returns></returns>
        private static (List<PatientOrderDetailInfo>, List<PatientOrderDetailInfo>) SplitOrderDetails(
             List<PatientOrderDetailInfo> patientOrderDetails, List<OrderToAssessListInfo> orderToAssessList, List<EventSettingInfo> eventSettings)
        {
            var patientOrderDetailToEvent = new List<PatientOrderDetailInfo>();
            var patientOrderDetailToProfile = new List<PatientOrderDetailInfo>();
            foreach (var detail in patientOrderDetails)
            {
                var orderToAssess = orderToAssessList.FirstOrDefault(m => m.OrderCode == detail.OrderCode);
                if (orderToAssess == null)
                {
                    continue;
                }
                var eventSetting = eventSettings.FirstOrDefault(m => m.AssessListID == orderToAssess.AssessListID);
                if (eventSetting != null)
                {
                    patientOrderDetailToEvent.Add(detail);
                    continue;
                }
                eventSetting = eventSettings.FirstOrDefault(m => orderToAssess.InteractAssessList.HasValue && m.AssessListID == orderToAssess.InteractAssessList);
                if (eventSetting != null)
                {
                    patientOrderDetailToEvent.Add(detail);
                    continue;
                }
                patientOrderDetailToProfile.Add(detail);
            }
            return (patientOrderDetailToEvent, patientOrderDetailToProfile);
        }

        /// <summary>
        /// 获取同步患者医嘱数据
        /// </summary>
        /// <param name="caseNumber">患者流水号</param>
        /// <returns></returns>
        private async Task<(bool, InpatientDataInfo, List<OrderToAssessListInfo>, List<FrequencyInfo>, List<PhysicianOrderInfo>, List<EventSettingInfo>
            , List<SimpleInfo>, List<ClinicalSettingInfo>)> GetSyncPatientOrderAsyncParams(string caseNumber)
        {
            var result = (false, new InpatientDataInfo(), new List<OrderToAssessListInfo>(), new List<FrequencyInfo>(), new List<PhysicianOrderInfo>()
                , new List<EventSettingInfo>(), new List<SimpleInfo>(), new List<ClinicalSettingInfo>());
            var inpatient = await _inpatientDataRepository.GetAsyncByCaseNumber(caseNumber, _config.Value.HospitalID);
            if (inpatient == null)
            {
                _logger.Error("SyncPatientOrderAsync<患者医嘱同步失败>[未查询到住院患者](CaseNumber={0})", caseNumber);
                return result;
            }
            var orderToAssessList = await _orderToAssessListRepository.GetAllAsync<OrderToAssessListInfo>();
            if (orderToAssessList.Count <= 0)
            {
                _logger.Error("SyncPatientOrderAsync 获取 orderToAssessList 失败");
                return result;
            }
            var frequencyList = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            if (frequencyList.Count <= 0)
            {
                _logger.Error("SyncOnePatientOrderAsync 获取 frequencyList 失败");
                return result;
            }
            var physicianOrder = await _physicianOrderRepository.GetAllAsync<PhysicianOrderInfo>();
            if (physicianOrder.Count <= 0)
            {
                _logger.Error("SyncPatientOrderAsync 获取 physicianOrder 失败");
                return result;
            }
            var eventSettings = await _eventSettingRepository.GetAsync();
            if (eventSettings.Count <= 0)
            {
                _logger.Error("SyncPatientOrderAsync 获取 EventSetting 失败");
                return result;
            }
            var stationList = await _stationListRepository.GetSimpleList();
            if (stationList.Count <= 0)
            {
                _logger.Error("SyncPatientOrderAsync 获取 simpleStationList 失败");
                return result;
            }
            var orderKeyWords = await _clinicSettingRepository.GetClinicSetting("OrderKeyWord");
            return (true, inpatient, orderToAssessList, frequencyList, physicianOrder, eventSettings, stationList, orderKeyWords);
        }

        /// <summary>
        /// 写入医嘱主表以及明细表数据
        /// </summary>
        /// <param name="commonOrders"></param>
        /// <param name="patientOrderMainList"></param>
        /// <param name="inpatient"></param>
        /// <param name="frequencyList"></param>
        /// <param name="physicianOrder"></param>
        /// <param name="stationList"></param>
        /// <returns></returns>
        private async Task<(List<PatientOrderMainInfo>, List<PatientOrderDetailInfo>)> SetPatientOrderMainAndDetalis(List<CommonOrderView> commonOrders, List<PatientOrderMainInfo> patientOrderMainList, InpatientDataInfo inpatient
            , List<FrequencyInfo> frequencyList, List<PhysicianOrderInfo> physicianOrder, List<SimpleInfo> stationList)
        {
            var newOrderMainList = new List<PatientOrderMainInfo>();
            var newOrderDetailList = new List<PatientOrderDetailInfo>();
            var patientOrderMainIDs = patientOrderMainList.Select(m => m.PatientOrderMainID).Distinct().ToArray();
            var patientOrderDetail = await _patientOrderDetailRepository.GetByMainIDs(patientOrderMainIDs);
            var employeeInfos = await _userRepository.GetAsync();
            var groupCommonOrders = commonOrders.GroupBy(m => m.OrderGroupID).ToList();
            foreach (var commonOrder in groupCommonOrders)
            {
                var orderTemp = commonOrder.ToList();
                if (!CheckExistEmployee(orderTemp[0], employeeInfos))
                {
                    continue;
                }
                //医嘱主记录
                var orderMain = patientOrderMainList.FirstOrDefault(m => m.OrderID == orderTemp[0].OrderID);
                var newOrderMain = await SyncOrderMainAsync(inpatient, frequencyList, stationList, employeeInfos, orderMain, orderTemp, physicianOrder);
                if (newOrderMain == null)
                {
                    continue;
                }
                newOrderMainList.Add(newOrderMain);
                //医嘱明细
                var patientOrderDetails = patientOrderDetail.Where(m => m.OrderID == commonOrder.Key).ToList();
                var newOrderDetails = await SetOrderDetailsAsync(orderTemp, newOrderMain, patientOrderDetails);
                newOrderDetailList.AddRange(newOrderDetails);
            }
            //更新给药表医嘱状态
            await UpdateMedicineScheduleOrderStatus(newOrderMainList);
            return (newOrderMainList, newOrderDetailList);
        }

        /// <summary>
        /// 更新给药表医嘱状态
        /// 这里的医嘱主表的CancalDate取得是his过来数据的CancalDate,只有在停止或者作废的医嘱才可能有这个时间,停止的医嘱代表的是停止时间,作废代表作废时间
        /// </summary>
        /// <param name="orderMainList">医嘱主表数据</param>
        /// <returns></returns>
        private async Task UpdateMedicineScheduleOrderStatus(List<PatientOrderMainInfo> orderMainList)
        {
            var orderStatusList = new List<string> { "3", "4" }; // 3 停止 4 作废
            var orderIDs = orderMainList.Where(m => m.CancalDate.HasValue && m.CancalDate != DateTime.MinValue).Select(m => m.OrderID).Distinct().ToList();
            var medicineInfos = await _patientMedicineScheduleRepository.GetMedicineScheduleByOrderIDsAsync(orderMainList[0].InpatientID, orderIDs);
            if (medicineInfos.Count > 0)
            {
                foreach (var orderMain in orderMainList)
                {
                    //调整为医嘱对应的所有未执行的给药医嘱在作废时都需需要停止，而不是医嘱的计划时间大于取消时间的医嘱才需要停止  GPC
                    var singleMedicineInfos = medicineInfos.Where(m => m.PatientOrderMainID == orderMain.OrderID &&  !m.PerformDate.HasValue ).ToList();

                    if (int.TryParse(orderMain.OrderStatus, out int orderStatus))
                    {
                        foreach (var singleMedicineInfo in singleMedicineInfos)
                        {
                            singleMedicineInfo.OrderStatus = orderStatus;
                            singleMedicineInfo.StopDate = orderMain.CancalDate.Value.Date;
                            singleMedicineInfo.StopTime = orderMain.CancalDate.Value.TimeOfDay;
                            singleMedicineInfo.Modify(PERSONID_TONGBU);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理患者profile
        /// </summary>
        /// <param name="orderMains"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="orderDetails"></param>
        private async Task SetPatientProfiles(List<PatientOrderMainInfo> orderMains, List<OrderToAssessListInfo> orderToAssessList
            , List<PatientOrderDetailInfo> orderDetails)
        {
            var profiles = new List<PatientProfile>();
            foreach (var item in orderDetails)
            {
                var assesslist = orderToAssessList.Where(m => m.OrderCode == item.OrderCode).Select(m => m.AssessListID).ToList();
                var orderMainTemp = orderMains.FirstOrDefault(m => m.PatientOrderMainID == item.PatientOrderMainID);
                if (orderMainTemp == null)
                {
                    continue;
                }
                profiles.AddRange(CreateProfiles(orderMainTemp, item.PatientOrderDetailID, assesslist));
            }
            if (profiles.Count > 0)
            {
                await _commonHelper.AddProfile(profiles);
            }
        }

        /// <summary>
        /// 处理患者事件
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="orderMains"></param>
        /// <param name="orderToAssessList"></param>
        /// <param name="eventSettings"></param>
        /// <param name="orderDetails"></param>
        /// <returns></returns>
        private async Task SetPatientEvents(InpatientDataInfo inpatient, List<PatientOrderMainInfo> orderMains
            , List<OrderToAssessListInfo> orderToAssessList, List<EventSettingInfo> eventSettings
            , List<PatientOrderDetailInfo> orderDetails)
        {
            var patientEvents = await _patientEventRepository.GetByInpatientID(inpatient.ID);
            foreach (var detail in orderDetails)
            {
                var orderMain = orderMains.FirstOrDefault(m => m.PatientOrderMainID == detail.PatientOrderMainID);
                if (orderMain == null)
                {
                    continue;
                }
                var orderToAssess = orderToAssessList.Where(m => m.OrderCode == detail.OrderCode).ToList();
                var events = new List<EventSettingInfo>();
                if (orderMain.EndDate.HasValue)
                {
                    events = eventSettings.Where(m => orderToAssess.Where(m => m.InteractAssessList.HasValue).Select(m => m.InteractAssessList).Distinct().Contains(m.AssessListID)).ToList();
                }
                else
                {
                    events = eventSettings.Where(m => orderToAssess.Select(m => m.AssessListID).Contains(m.AssessListID)).ToList();
                }
                if (events.Count > 0)
                {
                    foreach (var eventSetting in events)
                    {
                        await HandleOrderPatientEventAsync(eventSetting, orderMain, inpatient, patientEvents);
                    }
                }
            }
        }

        /// <summary>
        /// 处理排程
        /// </summary>
        /// <param name="commonOrders"></param>
        /// <param name="patientOrderMains"></param>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private async Task SetPatientSchedule(List<CommonOrderView> commonOrders, List<PatientOrderMainInfo> patientOrderMains, InpatientDataInfo inpatient)
        {
            //停止医嘱
            var stopSchedules = commonOrders.Where(m => m.OredrStatus == ORDER_STATUS_STOP).ToList();
            var stopOrderMains = patientOrderMains.Where(m => stopSchedules.Select(n => n.OrderID).Contains(m.OrderID)).ToList();
            if (stopOrderMains.Count > 0)
            {
                foreach (var item in stopOrderMains)
                {
                    if (!item.EndDate.HasValue)
                    {
                        continue;
                    }
                    await StopOrCancelPatientOrderSchedule(inpatient.CaseNumber, item.PatientOrderMainID, item.EndDate.Value);
                }
            }
            //作废医嘱
            var cancelSchedules = commonOrders.Where(m => m.OredrStatus == ORDER_STATUS_CANCEL).ToList();
            var cancelOrderMains = patientOrderMains.Where(m => cancelSchedules.Select(n => n.OrderID).Contains(m.OrderID)).ToList();
            if (cancelOrderMains.Count > 0)
            {
                foreach (var item in cancelOrderMains)
                {
                    await StopOrCancelPatientOrderSchedule(inpatient.CaseNumber, item.PatientOrderMainID, null);
                }
            }
        }

        /// <summary>
        /// 删除医嘱排程
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="orderID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private async Task StopOrCancelPatientOrderSchedule(string caseNumber, string patientOrderMainID, DateTime? dateTime)
        {
            var patientScheduleMains = await _patientScheduleMainRepository.GetByCaseNumberAndPatientInterventionID(caseNumber, patientOrderMainID);
            if (dateTime.HasValue)
            {
                patientScheduleMains = patientScheduleMains.Where(m => m.PerformDate == null && m.ScheduleDate.Add(m.ScheduleTime) >= dateTime.Value).ToList();
            }
            if (patientScheduleMains.Count == 0)
            {
                _logger.Info($"没有需要停止的排程,patientOrderMainID{patientOrderMainID}");
                return;
            }
            //处理排程           
            foreach (var itemSub in patientScheduleMains)
            {
                itemSub.Delete(PERSONID_TONGBU);
            }
        }

        /// <summary>
        /// 检核工号存在
        /// </summary>
        /// <param name="commonOrder">医嘱</param>
        /// <param name="employeeidList">员工集合</param>
        /// <returns></returns>
        private static bool CheckExistEmployee(CommonOrderView commonOrder, List<UserInfo> employeeidList)
        {
            bool IsEmployeeValid(string physicianID)
            {
                var employee = employeeidList.FirstOrDefault(m => m.UserID == physicianID);
                if (!employeeidList.Any(m => m.UserID == physicianID))
                {
                    _logger.Error($"人员工号 [{physicianID}] 查询信息错误！未找到人员信息!");
                    return false;
                }
                return true;
            }
            if (!IsEmployeeValid(commonOrder.AddEmployeeID))
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 同步医嘱主表数据
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="frequencyList"></param>
        /// <param name="stationList"></param>
        /// <param name="employeeidList"></param>
        /// <param name="oldOrderMain"></param>
        /// <param name="commonOrders"></param>
        /// <returns></returns>
        private async Task<PatientOrderMainInfo> SyncOrderMainAsync(InpatientDataInfo inpatient, List<FrequencyInfo> frequencyList, List<SimpleInfo> stationList,
            List<UserInfo> employeeidList, PatientOrderMainInfo oldOrderMain, List<CommonOrderView> commonOrders, List<PhysicianOrderInfo> physicianOrder)
        {
            var firstOrder = commonOrders.FirstOrDefault();
            var frequency = frequencyList.FirstOrDefault(m => m.Frequency == firstOrder.Frequency);
            var phyOrder = physicianOrder.Find(m => m.OrderCode == firstOrder.OrderCode);
            var main = CreateOrderMain(oldOrderMain, firstOrder, inpatient, employeeidList, frequency, phyOrder, stationList);
            if (oldOrderMain == null)
            {
                await _unitOfWork.GetRepository<PatientOrderMainInfo>().InsertAsync(main);
                return main;
            }
            else
            {
                UpdateOrderMainInfo(main, oldOrderMain);
                return oldOrderMain;
            };
        }

        /// <summary>
        /// 获取工号
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="employeeidList"></param>
        /// <returns></returns>
        private static string GetEmployeeID(string employeeID, List<UserInfo> employeeidList)
        {
            var userInfo = employeeidList.FirstOrDefault(m => m.UserID == employeeID);
            return userInfo != null ? userInfo.UserID : "";
        }

        /// <summary>
        /// 将公共医嘱转换为医嘱主表
        /// </summary>
        /// <param name="oldOrderMain"></param>
        /// <param name="commonOrder"></param>
        /// <param name="inpatientInfo"></param>
        /// <param name="employeeidList"></param>
        /// <param name="frequency"></param>
        /// <param name="phyOrder"></param>
        /// <param name="stationList"></param>
        /// <returns></returns>
        private static PatientOrderMainInfo CreateOrderMain(PatientOrderMainInfo oldOrderMain, CommonOrderView commonOrder, InpatientDataInfo inpatientInfo, List<UserInfo> employeeidList, FrequencyInfo frequency, PhysicianOrderInfo phyOrder, List<SimpleInfo> stationList)
        {
            DateTime? startDate = !string.IsNullOrEmpty(commonOrder.StartDate) && DateTime.TryParse(commonOrder.StartDate, out DateTime date) ? DateTime.Parse(commonOrder.StartDate) : null;
            return new PatientOrderMainInfo
            {
                PatientOrderMainID = Guid.NewGuid().ToString("N"),
                OrderID = commonOrder.OrderID,
                OrderType = commonOrder.OrderType,
                OrderPattern = commonOrder.OrderPattern,
                InpatientID = inpatientInfo.ID,
                PatientID = inpatientInfo.PatientID,
                StationID = stationList.Find(m => m.Code == commonOrder.StationCode)?.ID ?? inpatientInfo.StationID,
                BedID = inpatientInfo.BedID,
                CaseNumber = inpatientInfo.CaseNumber,
                ChartNo = inpatientInfo.ChartNo,
                BedNumber = inpatientInfo.BedNumber,
                StartDate = startDate.HasValue ? startDate.Value.Date : oldOrderMain.StartDate,
                StartTime = startDate.HasValue ? startDate.Value.TimeOfDay : oldOrderMain.StartTime,
                EndDate = !string.IsNullOrEmpty(commonOrder.EndDate) && DateTime.TryParse(commonOrder.EndDate, out DateTime endDate) && endDate != DateTime.MinValue ? endDate : null,
                EndTime = !string.IsNullOrEmpty(commonOrder.EndDate) && DateTime.TryParse(commonOrder.EndDate, out DateTime endTime) && endTime != DateTime.MinValue ? endTime.TimeOfDay : null,
                AddEmployeeID = GetEmployeeID(commonOrder.AddEmployeeID, employeeidList),
                AddDate = DateTime.Now,
                ConfirmPersonID = GetEmployeeID(commonOrder.ConfirmPersonID, employeeidList),
                ConfirmDate = !string.IsNullOrEmpty(commonOrder.ConfirmDate) && DateTime.TryParse(commonOrder.ConfirmDate, out DateTime confirmDate) && confirmDate != DateTime.MinValue ? confirmDate : null,
                CancalPersonID = GetEmployeeID(commonOrder.CancalPersonID, employeeidList),
                CancalDate = !string.IsNullOrEmpty(commonOrder.CancalDate) && DateTime.TryParse(commonOrder.CancalDate, out DateTime cancalDate) && cancalDate != DateTime.MinValue ? cancalDate : null,
                OrderStatus = commonOrder.OredrStatus.ToString(),
                FrequencyID = frequency?.ID,
                HISFrequency = commonOrder.Frequency,
                HISFrequencySchedule = frequency?.FrequencyDescription,
                HISOrderSort = int.TryParse(commonOrder.OrderGroupID, out int orderSort) ? orderSort : 0,
                ModifyPersonID = PERSONID_TONGBU,
                ModifyDate = DateTime.Now,
                RecordFlag = phyOrder != null && phyOrder.RecordFlag,
                PerformFlag = phyOrder != null && phyOrder.PerformFlag,
                PrintFlag = phyOrder == null ? "*" : phyOrder.PrintFlag,
                FirstDayStartTime = startDate.HasValue ? startDate.Value : oldOrderMain.StartDate.Add(oldOrderMain.StartTime),
                DeleteFlag = STRING_EMPTY
            };
        }

        /// <summary>
        /// 写医嘱明细
        /// </summary>
        /// <param name="commonOrders"></param>
        /// <param name="orderMain"></param>
        /// <param name="patientOrderDetails"></param>
        /// <returns></returns>
        private async Task<List<PatientOrderDetailInfo>> SetOrderDetailsAsync(List<CommonOrderView> commonOrders, PatientOrderMainInfo orderMain, List<PatientOrderDetailInfo> patientOrderDetails)
        {
            var orderDetails = new List<PatientOrderDetailInfo>();
            var oldOrderDetails = new List<PatientOrderDetailInfo>();
            foreach (var commonOrder in commonOrders)
            {
                var newDetail = CreateOrderDetailInfo(orderMain, commonOrder);
                var patientOrderDetail = patientOrderDetails.Find(m => m.OrderCode == commonOrder.OrderCode);
                if (patientOrderDetail != null)
                {
                    var inserFlag = UpdateOrderDetailInfo(newDetail, patientOrderDetail);
                    if (inserFlag)
                    {
                        oldOrderDetails.Add(patientOrderDetail);
                    }
                    continue;
                }
                orderDetails.Add(newDetail);
            }
            if (orderDetails.Count > 0)
            {
                await _unitOfWork.GetRepository<PatientOrderDetailInfo>().InsertAsync(orderDetails);
            }
            orderDetails.AddRange(oldOrderDetails);
            return (orderDetails);
        }

        /// <summary>
        /// 更新医嘱明细
        /// </summary>
        /// <param name="newDetail">新医嘱明细</param>
        /// <param name="oldDetail">旧医嘱明细</param>
        private bool UpdateOrderDetailInfo(PatientOrderDetailInfo newDetail, PatientOrderDetailInfo oldDetail)
        {
            bool inserFlag = false;
            oldDetail.PatientOrderMainID = newDetail.PatientOrderMainID;
            oldDetail.OrderID = newDetail.OrderID;
            oldDetail.OrderCode = newDetail.OrderCode;
            oldDetail.OrderPattern = newDetail.OrderPattern;
            oldDetail.OrderContent = newDetail.OrderContent;
            oldDetail.OrderDose = newDetail.OrderDose;
            oldDetail.Unit = newDetail.Unit;
            oldDetail.TotalVolume = newDetail.TotalVolume;
            oldDetail.OrderRule = newDetail.OrderRule;
            oldDetail.Location = newDetail.Location;
            oldDetail.SpecimenCategory = newDetail.SpecimenCategory;
            oldDetail.NumberOfExecution = newDetail.NumberOfExecution;
            oldDetail.StartDate = newDetail.StartDate;
            oldDetail.StartTime = newDetail.StartTime;
            oldDetail.EndDate = newDetail.EndDate;
            oldDetail.EndTime = newDetail.EndTime;
            oldDetail.OrderDetailID = newDetail.OrderDetailID;
            oldDetail.OrderType = newDetail.OrderType;
            oldDetail.OrderAlertFlag = newDetail.OrderAlertFlag;
            oldDetail.OrderDescription = newDetail.OrderDescription;
            oldDetail.OrderNO = newDetail.OrderNO;
            oldDetail.Package = newDetail.Package;
            oldDetail.PackageUnit = newDetail.PackageUnit;
            oldDetail.HISOrderSort = newDetail.HISOrderSort;
            oldDetail.DrugAttention = newDetail.DrugAttention;
            oldDetail.DrugSpec = newDetail.DrugSpec;
            oldDetail.BillingAttribution = newDetail.BillingAttribution;
            oldDetail.LabSheetSN = newDetail.LabSheetSN;
            oldDetail.OrderDetailID = newDetail.OrderDetailID;
            oldDetail.OrderNO = newDetail.OrderNO;
            if (_medicalDbContext.Entry(oldDetail).State != EntityState.Unchanged)
            {
                inserFlag = true;
                oldDetail.PerformDate = null;
                oldDetail.PerformTime = null;
                oldDetail.PerformEmployeeID = string.Empty;
                oldDetail.ModifyPersonID = newDetail.ModifyPersonID;
                oldDetail.ModifyDate = newDetail.ModifyDate;
            }
            return inserFlag;
        }

        /// <summary>
        /// 更新医嘱主表
        /// </summary>
        /// <param name="newMain">新主表记录</param>
        /// <param name="oldMain">旧的主表记录</param>
        private void UpdateOrderMainInfo(PatientOrderMainInfo newMain, PatientOrderMainInfo oldMain)
        {
            oldMain.StartDate = newMain.StartDate;
            oldMain.StartTime = newMain.StartTime;
            oldMain.EndDate = newMain.EndDate;
            oldMain.EndTime = newMain.EndTime;
            oldMain.ConfirmPersonID = newMain.ConfirmPersonID;
            oldMain.CancalPersonID = newMain.CancalPersonID;
            oldMain.CancalDate = newMain.CancalDate;
            oldMain.OrderStatus = newMain.OrderStatus;
            oldMain.LastPerformTime = newMain.LastPerformTime;
            oldMain.Interval = newMain.Interval;
            oldMain.OrderType = newMain.OrderType;
            if (newMain.ConfirmDate.HasValue)
            {
                oldMain.ConfirmDate = newMain.ConfirmDate;
            }
            if (_medicalDbContext.Entry(oldMain).State != EntityState.Unchanged)
            {
                oldMain.ModifyPersonID = newMain.ModifyPersonID;
                oldMain.ModifyDate = newMain.ModifyDate;
            }
        }

        /// <summary>
        /// 创建医嘱明细
        /// </summary>
        /// <param name="newOrderMain"></param>
        /// <param name="commonOrder"></param>
        /// <returns></returns>
        private static PatientOrderDetailInfo CreateOrderDetailInfo(PatientOrderMainInfo newOrderMain, CommonOrderView commonOrder)
        {
            var newOrderDetail = new PatientOrderDetailInfo
            {
                PatientOrderDetailID = Guid.NewGuid().ToString("N"),
                PatientOrderMainID = newOrderMain.PatientOrderMainID,
                OrderID = commonOrder.OrderID ?? STRING_EMPTY,
                OrderType = commonOrder.OrderType ?? STRING_EMPTY,
                OrderCode = commonOrder.OrderCode ?? STRING_EMPTY,
                OrderPattern = commonOrder.OrderPattern ?? STRING_EMPTY,
                OrderContent = commonOrder.OrderContent ?? STRING_EMPTY,
                OrderDescription = commonOrder.Note ?? STRING_EMPTY,
                Frequency = commonOrder.Frequency ?? STRING_EMPTY,
                Unit = commonOrder.Unit ?? STRING_EMPTY,
                OrderRule = commonOrder.OrderRule ?? STRING_EMPTY,
                Location = commonOrder.Location ?? STRING_EMPTY,
                SpecimenCategory = commonOrder.MethodCategory ?? STRING_EMPTY,
                StartDate = newOrderMain.StartDate.Date,
                StartTime = newOrderMain.StartTime,
                EndDate = newOrderMain.EndDate.HasValue ? newOrderMain.EndDate.Value.Date : null,
                EndTime = newOrderMain.EndTime.HasValue ? newOrderMain.EndTime.Value : null,
                DeleteFlag = STRING_EMPTY,
                OrderDetailID = commonOrder.HISOrderID,
                OrderNO = commonOrder.OrderID ?? STRING_EMPTY,
            };
            if (string.IsNullOrEmpty(newOrderDetail.OrderContent))
            {
                newOrderDetail.OrderContent = newOrderDetail.OrderDescription;
            };
            if (!string.IsNullOrWhiteSpace(commonOrder.OrderDose) && decimal.TryParse(commonOrder.OrderDose, out var orderDose))
            {
                newOrderDetail.OrderDose = orderDose;
            };
            if (!string.IsNullOrWhiteSpace(commonOrder.TotalVolume) && decimal.TryParse(commonOrder.TotalVolume, out var totalVolume))
            {
                newOrderDetail.TotalVolume = totalVolume;
            };
            if (!string.IsNullOrWhiteSpace(commonOrder.NumberOfExecution) && byte.TryParse(commonOrder.NumberOfExecution, out var numberOfExecution))
            {
                newOrderDetail.NumberOfExecution = numberOfExecution;
            };
            newOrderDetail.Modify(PERSONID_TONGBU);
            return newOrderDetail;
        }

        /// <summary>
        /// 创建Profile
        /// </summary>
        /// <param name="orderMain"></param>
        /// <param name="patientOrderDetailID"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        private List<PatientProfile> CreateProfiles(PatientOrderMainInfo orderMain, string patientOrderDetailID, List<int> assessListIDs)
        {
            var result = new List<PatientProfile>();
            foreach (var assessListID in assessListIDs)
            {
                var patientProfile = new PatientProfile
                {
                    HospitalID = _config.Value.HospitalID,
                    InpatientID = orderMain.InpatientID,
                    CaseNumber = orderMain.CaseNumber,
                    PatientID = orderMain.PatientID,
                    ChartNo = orderMain.ChartNo,
                    ModelName = "HIS.Order",
                    Source = "I",
                    ProfileDate = orderMain.StartDate,
                    ProfileTime = orderMain.StartTime,
                    AutoAddFlag = STRING_EMPTY,
                    Note = STRING_EMPTY,
                    ModifyPersonID = PERSONID_TONGBU,
                    ModifyDate = DateTime.Now,
                    SerialNumber = patientOrderDetailID,
                    AssessListID = assessListID
                };
                result.Add(patientProfile);
            }
            return result;
        }

        /// <summary>
        /// 患者事件处理
        /// </summary>
        /// <param name="eventSetting">事件AssessListID</param>
        /// <param name="orderMain"></param>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private async Task HandleOrderPatientEventAsync(EventSettingInfo eventSetting, PatientOrderMainInfo orderMain, InpatientDataInfo inpatient, List<PatientEventInfo> patientEvents)
        {
            var eventTime = orderMain.EndDate.HasValue ? orderMain.EndDate.Value.Date.Add(orderMain.EndTime.Value) : orderMain.StartDate.Add(orderMain.StartTime);
            var sourceID = $"Order|{orderMain.OrderID}";
            var unOrderEvent = patientEvents.Find(m => m.AssessListID == eventSetting.AssessListID && m.SourceID == sourceID);
            if (unOrderEvent != null)
            {
                return;
            }
            var changedOrderEvent = patientEvents.Find(m => m.AssessListID == eventSetting.AssessListID && m.SourceID == sourceID && m.OccurDate.Add(m.OccurTime) != eventTime);
            if (changedOrderEvent != null)
            {
                await _patientEventCommonService.DeleteInpatientEventAndLogAsync(orderMain.InpatientID, changedOrderEvent.PatientEventID, _config.Value.HospitalID, _config.Value.Language, PERSONID_TONGBU, changedOrderEvent);
            }
            if (eventSetting.AssessListID == EVENTSETTING_ASSESSLISTID_DEATH)
            {
                eventSetting.LogCode = LOGCODE_DEATH;
                var dischargeEvent = patientEvents.FirstOrDefault(m => m.AssessListID == EVENTSETTING_ASSESSLISTID_DISCHARGE);
                if (dischargeEvent != null)
                {
                    await _patientEventCommonService.DeleteInpatientEventAndLogAsync(dischargeEvent.InpatientID, dischargeEvent.PatientEventID, _config.Value.HospitalID, _config.Value.Language, orderMain.ModifyPersonID, dischargeEvent);
                }
            }
            await _patientEventCommonService.CallEventAPI(inpatient, orderMain.StationID, inpatient.DepartmentListID, orderMain.BedID, orderMain.BedNumber, eventTime, eventSetting.AssessListID, eventSetting.LogCode, eventSetting.ShowName, sourceID);
        }

        /// <summary>
        /// 处理文字匹配医嘱
        /// </summary>
        /// <param name="commonOrders">公共格式医嘱数据</param>
        /// <param name="orderKeyWords">医嘱关键字</param>
        /// <returns></returns>
        private static List<CommonOrderView> FormatOrderKeyWord(List<CommonOrderView> commonOrders, List<ClinicalSettingInfo> orderKeyWords)
        {
            foreach (var item in commonOrders)
            {
                if (!string.IsNullOrWhiteSpace(item.OrderCode))
                {
                    continue;
                }
                var orderKeyWordTemp = orderKeyWords.Find(m => item.OrderContent?.Contains(m.Description) ?? false);
                if (orderKeyWordTemp != null)
                {
                    item.OrderCode = orderKeyWordTemp.TypeValue;
                    continue;
                }
                orderKeyWordTemp = orderKeyWords.Find(m => item.Note?.Contains(m.Description) ?? false);
                if (orderKeyWordTemp != null)
                {
                    item.OrderCode = orderKeyWordTemp.TypeValue;
                    continue;
                }
            }
            return commonOrders;
        }
    }
}
