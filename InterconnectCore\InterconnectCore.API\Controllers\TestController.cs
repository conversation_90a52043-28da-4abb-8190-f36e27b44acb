﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NLog;
namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 测试控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/sync")]
    [EnableCors("any")]
    public class TestController : ControllerBase
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAPISettingService _aPISettingService;
        private readonly IOptions<SystemConfig> _config;
        /// <summary>
        /// 测试控制器的构造器
        /// </summary>
        public TestController(
            IAPISettingService aPISettingService
            , IOptions<SystemConfig> config
            )
        {
            _aPISettingService = aPISettingService;
            _config = config;
        }

        /// <summary>
        /// 获取服务器时间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetServerDateTime")]
        public IActionResult GetServerDateTime()
        {
            var result = new ResponseResult();
            try
            {
                result.Data = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取api配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetSettingBySettingCode")]
        public async Task<IActionResult> GetSettingBySettingCode(string serverCode, string settingCode)
        {
            var result = new ResponseResult();
            var testConfig = _config.Value;
            try
            {
                result.Data = await _aPISettingService.GetSettingBySettingCode(serverCode, settingCode);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }
    }
}