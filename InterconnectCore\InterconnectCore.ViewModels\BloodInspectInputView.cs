﻿namespace InterconnectCore.ViewModels
{
    /// <summary>
    /// 同步输血监测数据到护理记录的输入参数 View
    /// </summary>
    public class BloodInspectInputView
    {
        /// <summary>
        /// 病案号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 血袋号
        /// </summary>
        public string BloodNumber { get; set; }
        /// <summary>
        /// 记录类型
        /// </summary>
        public string RecordType { get; set; }

        /// <summary>
        /// 记录ID
        /// </summary>
        public string RecordID { get; set; }

        /// <summary>
        /// 执行人ID
        /// </summary>
        public string PerformEmployeeID { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime PerformDateTime { get; set; }

        /// <summary>
        /// 动态数据项（键值对）
        /// </summary>
        public Dictionary<string, string> KeyValueItems { get; set; }
    }
}
