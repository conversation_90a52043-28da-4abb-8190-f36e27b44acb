﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class StationToDeptInfoRepository : IStationToDeptInfoRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public StationToDeptInfoRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 获取所有病区信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<StationToDeptInfo>> GetAsync()
        {
            var datas = (List<StationToDeptInfo>)await GetCacheAsync();

            if (datas.Count == 0)
            {
                return new List<StationToDeptInfo>();
            }

            return datas;
        }

        /// <summary>
        /// 获取所有病区信息(数据同步使用)
        /// </summary>
        /// <returns></returns>
        public List<StationToDeptInfo> GetStationToDeptList()
        {
            return _medicalDbContext.StationToDeptInfos.Where(m => m.DeleteFlag != "*").ToList();
        }

        /// <summary>
        /// 获取所有病区信息(数据同步使用)
        /// </summary>
        /// <returns></returns>
        public List<StationToDeptInfo> GetAllStationToDeptList()
        {
            return _medicalDbContext.StationToDeptInfos.ToList();
        }

        public async Task<List<StationToDeptInfo>> GetAsync(int stationID)
        {
            var datas = await GetAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            return datas.Where(m => m.StationID == stationID).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<StationToDeptInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.StationToDeptInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.StationToDepartment.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 无发现引用--GPC
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetDepartmentCodeByStation(int stationID)
        {
            return await (from m in _medicalDbContext.StationToDeptInfos.Where(m => m.StationID == stationID && m.DeleteFlag != "*")
                          join n in _medicalDbContext.departmentListInfos.Where(m => m.DeleteFlag != "*")
                          on m.DepartmentListID equals n.ID
                          select n.DepartmentCode).ToListAsync();
        }
        /// <summary>
        /// 依据病区ID获取对应科室代码（宏力同步程序用）
        /// </summary>
        /// <param name="stationIDs"></param>
        /// <returns></returns>
        public async Task<List<string>> GetDepartmentCodeByStationIDs(List<int> stationIDs)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            return await (from m in _medicalDbContext.StationToDeptInfos.Where(m => stationIDs.Contains(m.StationID) && m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                          join n in _medicalDbContext.departmentListInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == hospitalID)
                          on m.DepartmentListID equals n.ID
                          select n.DepartmentCode).ToListAsync();
        }
    }
}
