﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Hangfire" Version="1.8.15" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\InterconnectCore.Common\InterconnectCore.Common.csproj" />
    <ProjectReference Include="..\InterconnectCore.Data.Interface\InterconnectCore.Data.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Data\InterconnectCore.Data.csproj" />
    <ProjectReference Include="..\InterconnectCore.Models\InterconnectCore.Models.csproj" />
    <ProjectReference Include="..\InterconnectCore.QueryService.Interface\InterconnectCore.QueryService.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Service.Interface\InterconnectCore.Service.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Service\InterconnectCore.Service.csproj" />
    <ProjectReference Include="..\InterconnectCore.ViewModels\InterconnectCore.ViewModels.csproj" />
    <ProjectReference Include="..\Medical\Medical.Common\Medical.Common.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data.Interface\Medical.Data.Interface.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data\Medical.Data.csproj" />
  </ItemGroup>
</Project>