﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientSpecialListImageRepository : IPatientSpecialListImageRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public PatientSpecialListImageRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        ///// <summary>
        ///// 获取伤口图片
        ///// </summary>
        ///// <param name="mainID"></param>
        ///// <param name="num"></param>
        ///// <returns></returns>
        //public async Task<List<PatientSpecialListImageInfo>> GetImagesByWoundAsync(string mainID, int num)
        //{
        //    return await _medicalDbContext.PatientSpecialListImageInfos.Where(m =>
        //    m.PatientSpecialListCareMainID == mainID && m.NumberOfAssessment == num &&
        //    m.DeleteFlag != "*").OrderBy(m => m.SpecialListImageNumber).ToListAsync();
        //}
        /// <summary>
        /// 获取伤口图片
        /// </summary>
        /// <param name="mainID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<List<PatientSpecialListImageInfo>> GetImagesByCareMainIDAsync(string mainID)
        {
            return await _medicalDbContext.PatientSpecialListImageInfos.Where(m =>
            m.PatientSpecialListCareMainID == mainID && m.DeleteFlag != "*")
            .OrderBy(m => m.SpecialListImageNumber).ToListAsync();
        }
        /// <summary>
        /// 根据MainID集合获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<PatientSpecialListImageInfo>> GetImagesByCareMainIDList(List<string> mainID)
        {
            return await _medicalDbContext.PatientSpecialListImageInfos.Where(m =>
           mainID.Contains(m.PatientSpecialListCareMainID) && m.DeleteFlag != "*")
            .OrderBy(m => m.SpecialListImageNumber).ToListAsync();
        }
    }
}
