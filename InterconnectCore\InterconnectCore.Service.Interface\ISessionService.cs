﻿using InterconnectCore.Common.SessionCommon;

namespace InterconnectCore.Service.Interface
{
    public interface ISessionService
    {
        /// <summary>
        /// 记录当先登录医院的用户信息，在登录前因Token不存在，获取缓存使用
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<string> SetUserLoginSession(string hospitalID, int language);

        /// <summary>
        /// 获取session
        /// </summary>
        /// <returns></returns>
        Task<Session> GetSession();

        /// <summary>
        /// 是否含有Session
        /// </summary>
        /// <param name="token">口令</param>
        /// <returns></returns>
        Task<bool> ContainAsync(string token);

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<Session> GetAsync(string token);

        /// <summary>
        /// 设置Session
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        Task SetAsync(Session session);

        /// <summary>
        /// 移除Session
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        Task<bool> RemoveAsync(Session session);

        /// <summary>
        /// 根据key集合获取缓存
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        Task<IDictionary<string, object>> GetAll<T>(IEnumerable<string> keys);
    }
}