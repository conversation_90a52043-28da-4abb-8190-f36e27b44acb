﻿/*
 2021-12-27 2291 护理计划需要可以调整时间,重构时新增取得护理计划-问题方法(GetPlanProblemView) -正元
 2021-12-28 2291 护理计划需要可以调整时间,重构时新增取得简易护理问题内容(GetSimplePatientProblem) -正元
 2021-12-28 2291 护理计划需要可以调整时间,重构时新增取得前一次病人护理问题序号(GetLastPatientProblemID) -正元
 2021-12-28 2291 护理计划需要可以调整时间,重构时新增取得前病人现有护理问题(GetNowPatientProblem) -正元
 */
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientProblemRepository : IPatientProblemRepository
    {
        private MedicalDbContext _dbContext = null;
        /// <summary>
        /// 资料产生方式  S:System建议, U: User自行新增 O:户嘱(集束护理)
        /// </summary>
        private const string DIGNOSEFLAG_O = "O";

        public PatientProblemRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<PatientProblemInfo>> GetAsync(string inpatientID, int? stationID, bool allFlag)
        {
            var query = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();

            if (stationID != null)
            {
                query = query.Where(m => m.StationID == stationID.Value).ToList();
            }
            if (allFlag)
            {
                query = query.Where(m => m.EndDate == null).ToList();
            }
            return query;
        }

        public async Task<List<PatientProblemInfo>> GetPatientProblemByInpatientID(string inpatientID, int stationID)
        {
            var query = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
            && m.DeleteFlag != "*" && m.EndDate == null && m.DiagnoseFlag != "O")
                .Select(m => new PatientProblemInfo
                {
                    StationID = m.StationID,
                    EndDate = m.EndDate,
                    ExpectDate = m.ExpectDate,
                    ExpectTime = m.ExpectTime
                }
                ).ToListAsync();
            return query;
        }


        public async Task<List<PatientProblemInfo>> GetAllAsync(string inpatientID, int? stationID)
        {
            var query = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID).ToListAsync();

            if (stationID != null)
            {
                query = query.Where(m => m.StationID == stationID.Value).ToList();
            }

            return query;
        }
        /// <summary>
        /// 获取病人问题
        /// </summary>      
        /// <param name="assessMainID">主评估记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByAssessMainAsync(string assessMainID)
        {
            return await _dbContext.PatientProblems.Where(m => m.AssessMainID == assessMainID && m.EndDate == null && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientProblemInfo>> GetCountByAssessMainAsync(string assessMainID, string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.AssessMainID == assessMainID && m.DeleteFlag != "*"
            && m.DiagnoseFlag != DIGNOSEFLAG_O).ToListAsync();
        }
        public async Task<List<PatientProblemInfo>> GetAsync(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientProblemInfo> GetByIDAsync(string id)
        {
            return await _dbContext.PatientProblems.Where(m => m.ID == id).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取病人问题
        /// </summary>
        /// <param name="inPatientID">病人在院号</param>
        /// <param name="ids">病人问题ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByIDAsync(string inPatientID, string[] ids)
        {
            var PatientProblemList = await _dbContext.PatientProblems.Where(m => m.InpatientID == inPatientID.Trim()).ToListAsync();
            return PatientProblemList.Where(m => ids.Contains(m.ID) && m.DeleteFlag != "*").ToList();
        }
        /// <summary>
        /// 获取病人问题
        /// </summary>
        /// <param name="ids">病人问题ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByIDAsync(string[] ids)
        {
            var PatientProblemList = new List<PatientProblemInfo>();
            for (int i = 0; i < ids.Length; i++)
            {
                var tempList = await _dbContext.PatientProblems.Where(m => m.ID == ids[i]).ToListAsync();
                PatientProblemList = PatientProblemList.Union(tempList).ToList();
            }
            return PatientProblemList.Where(m => m.DeleteFlag != "*").OrderBy(m => m.PatientProblemSort).ToList();
        }
        /// <summary>
        /// 根据patientproblemID数组获取病人问题
        /// </summary>
        /// <param name="ids">病人问题ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByIDListAsync(List<string> ids)
        {
            var PatientProblemList = new List<PatientProblemInfo>();
            for (int i = 0; i < ids.Count; i++)
            {
                var tempList = await _dbContext.PatientProblems.Where(m => m.ID == ids[i]).ToListAsync();
                PatientProblemList = PatientProblemList.Union(tempList).ToList();
            }
            return PatientProblemList.Where(m => m.DeleteFlag != "*").OrderBy(m => m.PatientProblemSort).ToList();
        }
        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="patientProblemInfos">问题集合</param>
        /// <returns></returns>
        public async Task<bool> UpdateAsync(List<PatientProblemInfo> patientProblemInfos)
        {
            _dbContext.PatientProblems.UpdateRange(patientProblemInfos);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 取得问题统计数据
        /// </summary>
        /// <param name="staticDate"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetBatchStaticProblem(DateTime staticDate)
        {
            return await _dbContext.PatientProblems.Where(m =>
            (m.StartDate <= staticDate && m.EndDate == null) ||
            (m.StartDate <= staticDate && m.EndDate > staticDate)
            && m.DeleteFlag != "*"
            ).ToListAsync();
        }

        /// <summary>
        /// 透过病人护理问题序号取的病人护理问问题
        /// </summary>
        /// <param name="patientProblemID">护理问题序号</param>
        /// <returns></returns>
        public async Task<PatientProblemInfo> GetPatientProblemByID(string patientProblemID)
        {
            return await _dbContext.PatientProblems.Where(m => m.ID == patientProblemID && m.DeleteFlag != "*").SingleOrDefaultAsync();
        }
        public async Task<List<PatientProblemInfo>> GetByInpationAndStationID(string inpatientID, int stationID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                    && m.DiagnoseFlag != DIGNOSEFLAG_O).ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetProblemByDate(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.PatientProblems.Where(m => m.EndDate != null && m.EndDate >= startDate
                    && (m.EndDate <= endDate) && (m.PatientOutcome != "" && m.PatientExceptOutcome != "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetProblemByDate(DateTime startDate, DateTime endDate, int problemID)
        {
            return await _dbContext.PatientProblems.Where(m => m.StartDate <= endDate
                && m.ProblemID == problemID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetByProblemID(string inpatientID, int problemID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && m.ProblemID == problemID
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetByProblemID(string inpatientID, int[] problemIDs)
        {
            var PatientProblemList = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            return PatientProblemList.Where(m => problemIDs.Contains(m.ProblemID)).ToList();
        }

        public async Task<List<PatientProblemInfo>> GetPatientProblemByProblemID(string inpatientID, int[] problemIDs)
        {
            var patientProblemListTemp = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*").ToListAsync();
            var patientProblemList1 = patientProblemListTemp.Where(m => m.EndDate == null).ToList();
            var patientProblemList2 = patientProblemListTemp.Where(m => m.EndDate != null
            && m.EndTime != null && m.EndDate.Value.Add(m.EndTime.Value) > DateTime.Now).ToList();
            var patientProblemListResult = patientProblemList1.Union(patientProblemList2).ToList();
            return patientProblemListResult.Where(m => problemIDs.Contains(m.ProblemID)).ToList();
        }

        public async Task<List<PatientProblemInfo>> GetByInpatientIDs(string[] inpatientIDs, bool overExpected)
        {
            var PatientProblemList = new List<PatientProblemInfo>();
            for (int i = 0; i < inpatientIDs.Length; i++)
            {
                var tempList = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientIDs[i] && m.DeleteFlag != "*").ToListAsync();
                PatientProblemList = PatientProblemList.Union(tempList).ToList();
            }
            var list = PatientProblemList.Where(m => m.EndDate == null).ToList();

            var result = new List<PatientProblemInfo>();

            if (overExpected)
            {
                list = list.Where(m => m.DiagnoseFlag != DIGNOSEFLAG_O).ToList();
                //获取过期的数据
                DateTime dateTime;
                foreach (var item in list)
                {
                    //集束护理没有预计结束时间
                    if (!item.ExpectDate.HasValue)
                    {
                        continue;
                    }
                    //预计结束日期
                    dateTime = item.ExpectDate.Value;
                    if (item.ExpectTime != null)
                    {
                        //如果预计结束时间不为空，预计结束日期加上时间
                        dateTime = dateTime.Add(item.ExpectTime.Value);
                    }
                    //如果预计结束时间小于当前时间，说明已过期
                    if (dateTime < DateTime.Now)
                    {
                        result.Add(item);
                    }
                }
            }
            else
            {
                result = list;
            }
            return result;
        }

        public async Task<List<PatientProblemInfo>> GetPatientProblems(string inpatientID, int language, int stationID)
        {
            var query = _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.EMRFlag != "*");
            query = query.Where(m => m.StationID == stationID);
            return await query.ToListAsync();
        }
        public async Task<List<PatientProblemInfo>> GetByAssessMainIDs(string[] assessMainIDs)
        {
            var PatientProblemList = new List<PatientProblemInfo>();
            for (int i = 0; i < assessMainIDs.Length; i++)
            {
                var tempList = await _dbContext.PatientProblems.Where(m => m.AssessMainID == assessMainIDs[i]).ToListAsync();
                PatientProblemList = PatientProblemList.Union(tempList).ToList();
            }

            return PatientProblemList.Where(m => m.DeleteFlag != "*").ToList();
        }

        /// <summary>
        /// 获取科室未结束问题
        /// </summary>
        /// <param name="stationID">科室代码</param>
        /// <param name="diagnoseFlag">问题来源</param>
        /// <param name="problemIDs">护理问题代码集合</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetUnEndAsync(int stationID, string diagnoseFlag, List<int> problemIDs)
        {
            var list = await _dbContext.PatientProblems.Where(m => m.StationID == stationID
             && !m.EndDate.HasValue
             && m.DeleteFlag == "").ToListAsync();

            if (string.IsNullOrEmpty(diagnoseFlag))
            {
                list = list.Where(m => m.DiagnoseFlag == diagnoseFlag).ToList();
            }

            if (problemIDs.Count > 0)
            {
                list = (from problemID in problemIDs
                        join patientProblem in list
                        on problemID equals patientProblem.ProblemID
                        select patientProblem).ToList();
            }
            return list;
        }

        public async Task<List<PatientProblemInfo>> GetByInpatientIDs(string[] inpatientIDs)
        {
            return await _dbContext.PatientProblems.Where(m =>
                inpatientIDs.Contains(m.InpatientID) && m.EndDate != null && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取未评价
        /// </summary>
        /// <param name="inpatientID">用户在院ID</param>
        /// <param name="stationID">单位代码</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetUnEvaluationAsync(string inpatientID, int stationID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && m.EndDate != null && m.NursingOutcomeID == null && m.DiagnoseFlag != DIGNOSEFLAG_O && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人历史护理问题
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetHistoryPatientProblemAsync(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.EndDate != null).ToListAsync();
        }

        //取得病人未结束集束护理
        public async Task<List<PatientProblemInfo>> GetUnEndClusterOrder(string inpatientID)
        {
            var currentTime = DateTime.Now;
            var problems = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DiagnoseFlag == DIGNOSEFLAG_O
            && m.DeleteFlag != "*").ToListAsync();
            problems = problems.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndTime.HasValue && m.EndDate.Value.Add(m.EndTime.Value) > currentTime)).ToList();
            return problems;
        }

        public async Task<List<PatientProblemInfo>> GetByInpatientIDAsync(string inpatientID, int stationID, string diagnoseFlag)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DiagnoseFlag == diagnoseFlag && m.EndDate == null
            && m.StationID == stationID && m.DeleteFlag != "*" && string.IsNullOrEmpty(m.AssessMainID)).ToListAsync();
        }

        public async Task<List<string>> GetLastNursingPlan(string inpatientID, int language)
        {
            return await (from problems in _dbContext.PatientProblems
                          join nursingOutCome in _dbContext.NursingOutComes on problems.NursingOutcomeID equals nursingOutCome.ID
                          join last in _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.EndDate != null && m.DiagnoseFlag != DIGNOSEFLAG_O && m.DeleteFlag != "*")
                         .OrderByDescending(m => m.EndDate).ThenByDescending(m => m.EndTime)
                          on new { problems.InpatientID, problems.EndDate, problems.EndTime } equals new { last.InpatientID, last.EndDate, last.EndTime }
                          where problems.InpatientID == inpatientID && nursingOutCome.Language == language && problems.ExpectDate != null
                          select nursingOutCome.NursingOutcome).ToListAsync();
        }

        /// <summary>
        /// 根据sourceID获取护理问题
        /// </summary>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetProblemsBySourceID(string sourceID)
        {
            return await _dbContext.PatientProblems.Where(t => t.SourceID == sourceID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据sourceID获取护理问题（包括已停止和已删除的）
        /// </summary>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetAllProblemsBySourceIDAsync(string inpatientID, string sourceID)
        {
            return await _dbContext.PatientProblems.Where(t => t.InpatientID == inpatientID && t.SourceID == sourceID).ToListAsync();
        }

        public async Task<List<int>> GetPatientUnEndProblemID(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.EndDate == null)
                .Select(m => m.ProblemID).ToListAsync();
        }

        public async Task<List<UnEvaluationProblem>> GetUnEvaluationProblem(string inpatientID, int stationID, bool allFlag)
        {
            var datas = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
                                  && m.StationID == stationID && m.DiagnoseFlag != "O"
                                  && m.NursingOutcomeID == null && m.Status != 0 && m.DeleteFlag != "*")
                                  .Select(m => new UnEvaluationProblem
                                  {
                                      ID = m.ID,
                                      ProblemID = m.ProblemID,
                                      NursingGoalID = m.NursingGoalID,
                                      NursingOutcomeID = m.NursingOutcomeID,
                                      EndDate = m.EndDate,
                                      AssessMainID = m.AssessMainID,
                                  }).ToListAsync();

            if (allFlag)
            {
                datas = datas.Where(m => m.EndDate == null).ToList();
            }

            return datas;
        }

        public async Task<List<KeyValueString>> GetPatientProblemOutcome(int? stationID)
        {
            var query = _dbContext.PatientProblems.Where(m => m.PatientExceptOutcome != "" && m.PatientExceptOutcome != null
                                       && m.PatientOutcome != "" && m.PatientOutcome != null
                                       && m.DiagnoseFlag != "O" && m.DeleteFlag != " * ");
            if (stationID != null)
            {
                query = query.Where(m => m.StationID == stationID);
            }

            return await query.Select(m => new KeyValueString
            {
                Key = m.PatientOutcome,
                Value = m.PatientExceptOutcome
            }).ToListAsync();
        }
        /// <summary>
        /// 获取未停止问题ID
        /// </summary>
        /// <param name="inpatientID">患者ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetUnEndIDs(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.EndDate == null && m.DeleteFlag != "*").AsNoTracking()
                .Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 获取护理问题根据inpatientIDArr
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByInpatientIDArr(string[] inpatientIDs)
        {
            return await _dbContext.PatientProblems.Where(m =>
                inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人集束护理
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByInpationAndStationIDTypeIsO(string inpatientID, int stationID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                    && m.DiagnoseFlag == DIGNOSEFLAG_O).ToListAsync();
        }

        /// <summary>
        /// 获取暂存的护理问题
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetTemporarySave(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && m.DeleteFlag != "*" && m.Status == 0 && m.DiagnoseFlag != "O").ToListAsync();
        }

        //2021-12-27 取得护理计划-问题
        public async Task<List<PlanProblemView>> GetPlanProblemView(string inpatientID, int stationID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.EndDate == null && m.DiagnoseFlag != "O" && m.DeleteFlag != "*")
                              .OrderBy(m => m.PatientProblemSort)
                              .Select(m => new PlanProblemView
                              {
                                  PatientProblemID = m.ID,
                                  ProblemID = m.ProblemID,
                                  StartDate = m.StartDate,
                                  StartTime = m.StartTime,
                                  ExpectDate = m.ExpectDate,
                                  ExpectTime = m.ExpectTime,
                                  PatientProblemSort = m.PatientProblemSort,
                                  Status = m.Status,
                                  NursingGoalID = m.NursingGoalID,
                                  PatientExceptOutcome = m.PatientExceptOutcome
                              }).ToListAsync();
        }

        public async Task<PatientProblemSimpleView> GetSimplePatientProblem(string id)
        {
            return await _dbContext.PatientProblems.Where(m => m.ID == id)
                .Select(m => new PatientProblemSimpleView
                {
                    ID = m.ID,
                    AssessMainID = m.AssessMainID,
                    ProblemID = m.ProblemID,
                    InpatientID = m.InpatientID,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    EndDate = m.ExpectDate,
                    EndTime = m.ExpectTime,
                    DiagnoseFlag = m.DiagnoseFlag
                })
                .FirstOrDefaultAsync();
        }

        public async Task<string> GetLastPatientProblemID(string inpatientID, int problemID, string id)
        {
            var data = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.ProblemID == problemID && m.ID != id && m.DeleteFlag != "*")
                .OrderByDescending(m => m.StartDate).ThenByDescending(m => m.StartTime)
                .Select(m => m.ID).FirstOrDefaultAsync();

            if (data == null)
            {
                return "";
            }

            return data;
        }

        public async Task<List<PatientProblemInfo>> GetNowPatientProblems(string inpatientID, int stationID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.EndDate == null && m.DiagnoseFlag != "O" && m.DeleteFlag != "*")
                              .OrderBy(m => m.PatientProblemSort)
                              .ToListAsync();
        }
        public async Task<AssessTimeView> GetTimeByID(string problemID)
        {
            var assessMain = await _dbContext.PatientProblems.Where(m => m.ID == problemID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.StartDate, StartTime = m.StartTime, EndDate = m.EndDate, EndTime = m.EndTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        public async Task<List<int>> GetProblemsByDateTime(DateTime startDateTime, DateTime endDateTime, string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && (m.StartDate >= startDateTime.Date && m.StartDate <= endDateTime.Date)
            && (m.StartTime >= startDateTime.TimeOfDay && m.StartTime <= endDateTime.TimeOfDay)
            && m.DiagnoseFlag != DIGNOSEFLAG_O && m.DeleteFlag != "*" && m.EndDate == null)
                .OrderBy(m => m.PatientProblemSort).Select(m => m.ProblemID).ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetAllByProblemID(string inpatientID, int problemID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
            && m.ProblemID == problemID)
                .Select(m => new PatientProblemInfo
                {
                    InpatientID = m.InpatientID,
                    DeleteFlag = m.DeleteFlag,
                    SourceID = m.SourceID,
                })
                .ToListAsync();
        }

        //取得病人未结束护理问题
        public async Task<int> GetUnEndPatientProblem(string inpatientID)
        {
            var currentTime = DateTime.Now;
            var problems = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DiagnoseFlag != DIGNOSEFLAG_O
               && m.DeleteFlag != "*").ToListAsync();
            var problemsCount = problems.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndTime.HasValue
                  && m.EndDate.Value.Add(m.EndTime.Value) > currentTime)).Count();
            return problemsCount;
        }
        public async Task<List<int>> GetUnEndPatientClusterIDs(string inpatientID, int[] problemIDs)
        {
            var currentTime = DateTime.Now;
            var problems = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID
                && m.DiagnoseFlag == DIGNOSEFLAG_O
                && m.DeleteFlag != "*"
                && m.EndDate == null
                && problemIDs.Contains(m.ProblemID)).Select(m => m.ProblemID).ToListAsync();
            return problems;
        }

        //获取未停止护理问题
        public async Task<int> GetUnEndPatientProblemByInpatientIdAndProProblemID(string inpatientID, int problemID, int stationID)
        {
            var currentTime = DateTime.Now;
            var problems = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.ProblemID == problemID
                && m.StationID == stationID
                && m.DeleteFlag != "*"
               && (m.EndDate == null || (m.EndDate.HasValue && m.EndDate.Value > currentTime.Date)))
                .Select(m => new PatientProblemInfo
                {
                    ID = m.ID,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime
                }).ToListAsync();
            problems = problems.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndTime.HasValue
                 && m.EndDate.Value.Add(m.EndTime.Value) > currentTime)).ToList();
            return problems.Count();
        }

        /// <summary>
        /// 获取患者在当前病区当前有效的集束护理
        /// </summary>
        /// <param name="inPatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="diagnoseFlag"></param>
        /// <param name="problemIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetCurrentPatientProblemIDs(string inPatientID, int stationID, string diagnoseFlag, int[] problemIDs)
        {
            var currentTime = DateTime.Now;
            var problems = await _dbContext.PatientProblems.Where(m => m.DeleteFlag != "*"
            && m.InpatientID == inPatientID && m.StationID == stationID
            && (m.EndDate == null || (m.EndDate.HasValue && m.EndDate.Value > currentTime.Date))).ToListAsync();
            if (diagnoseFlag == DIGNOSEFLAG_O)
            {
                problems = problems.Where(m => m.DiagnoseFlag == DIGNOSEFLAG_O).ToList();
                if (problemIDs.Length > 0)
                {
                    problems = problems.Where(m => problemIDs.Contains(m.ProblemID)).ToList();
                }
            }
            return problems;
        }
        public async Task<List<PatientProblemInfo>> GetNoEndProblems(string assessMainID)
        {
            return await _dbContext.PatientProblems.Where(m => m.AssessMainID == assessMainID && m.DiagnoseFlag != "O" && m.EndDate == null && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientProblemInfo>> GetAllUnEvaluationProblem(List<string> inpatientIDs)
        {
            return await _dbContext.PatientProblems.Where(m => m.DiagnoseFlag != "O" && m.NursingOutcomeID == null && m.Status != 0 && m.DeleteFlag != "*"
            && m.EndDate != null && inpatientIDs.Contains(m.InpatientID))
                .Select(m => new PatientProblemInfo
                {
                    InpatientID = m.InpatientID,
                    StationID = m.StationID
                }).ToListAsync();
        }
        /// <summary>
        /// 获取当前患者所有未结束的护理问题（包含集束护理）
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetNoEndProblemsByInpatientID(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.EndDate == null && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取当前患者所有护理问题（包含集束护理）
        /// </summary>
        /// <param name="inpatientID">患者主键</param>
        /// <param name="problems">护理问题ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetPatientProblemByInpationAndStationID(string inpatientID, List<int> problems)
        {
            var query = await _dbContext.PatientProblems.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            var filterQuery = query.Where(m => m.DiagnoseFlag != DIGNOSEFLAG_O).ToList();
            if (problems.Count > 0)
            {
                var problemIDs = query.Where(m => problems.Contains(m.ProblemID)).ToList();
                return filterQuery.Union(problemIDs).ToList();
            }
            return filterQuery;
        }
        /// <summary>
        /// 获取患者数据包含删除
        /// </summary>
        /// <param name="assessMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientProblemInfo>> GetByAssessMainAndDeleteAsync(string assessMainID)
        {
            return await _dbContext.PatientProblems.Where(m => m.AssessMainID == assessMainID).ToListAsync();
        }

        public async Task<List<PatientProblemInfo>> GetUnEndPatientCluster(string inpatientID)
        {
            return await _dbContext.PatientProblems.Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID
            && m.DiagnoseFlag == DIGNOSEFLAG_O && m.EndDate == null).ToListAsync();
        }
    }
}