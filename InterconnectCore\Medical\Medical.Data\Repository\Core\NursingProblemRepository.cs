﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingProblemRepository : INursingProblemRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public NursingProblemRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<NursingProblemInfo> GetOneAsync(int id)
        {
            var datas = (List<NursingProblemInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas.Where(t => t.ID == id).SingleOrDefault();
            }
            return new NursingProblemInfo();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingProblemInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.NursingProblems.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*")
                .OrderBy(m => m.Priority).ThenBy(m => m.ID).ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingProblem.GetKey(_sessionCommonServer);
        }

        public async Task<List<NursingProblemInfo>> GetAsync()
        {
            var datas = (List<NursingProblemInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas;
            }

            return new List<NursingProblemInfo>();
        }

        public async Task<NursingProblemInfo> GetAsync(int problemID)
        {
            var datas = await GetAsync();

            if (datas != null)
            {
                return datas.Where(m => m.ID == problemID).FirstOrDefault();
            }

            return null;
        }
    }
}