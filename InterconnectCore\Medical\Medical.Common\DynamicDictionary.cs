﻿using System.Collections.Generic;
using System.Dynamic;

namespace Medical.Common
{
    public class DynamicDictionary : DynamicObject
    {
        Dictionary<string, object> dictionary = new Dictionary<string, object>();

        public int Count
        {
            get
            {
                return dictionary.Count;
            }
        }

        public override bool TryGetMember(GetMemberBinder binder, out object result)
        {
            string name = binder.Name.ToLower();

            return dictionary.TryGetValue(name, out result);
        }

        public override bool TrySetMember(SetMemberBinder binder, object value)
        {
            dictionary[binder.Name.ToLower()] = value;

            return true;
        }
        /// <summary>
        /// 检查成员是否存在
        /// </summary>
        /// <param name="memberName">成员名称</param>
        /// <returns></returns>
        public bool HasMember(string memberName)
        {
            return dictionary.ContainsKey(memberName.ToLower());
        }
    }

}
