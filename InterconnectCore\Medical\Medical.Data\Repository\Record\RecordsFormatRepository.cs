﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RecordsFormatRepository : IRecordsFormatRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public RecordsFormatRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<RecordsFormatInfo>> GetAsync(int recordListID)
        {
            var datas = await GetCacheAsync() as List<RecordsFormatInfo>;
            return datas.Where(m => m.RecordListID == recordListID).OrderBy(m => m.Sort).ToList();
        }

        public async Task<List<RecordFormat>> GetByRecordID(int recordListID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.RecordListID == recordListID).OrderBy(m => m.Sort).ToList();
        }

        public async Task<List<int>> GetRecordIDByInterventionMainID(int InterventionMainID)
        {
            var datas = await GetAsync();
            return datas.Where(m => m.NursingInterventionMainID == InterventionMainID).Select(m => m.RecordListID).Distinct().ToList();
        }

        public async Task<List<RecordFormat>> GetAsync()
        {
            var datas = await GetCacheAsync() as List<RecordsFormatInfo>;
            return datas.Select(m => new RecordFormat
            {
                ID = m.ID,
                RecordListID = m.RecordListID,
                RecordsFormatLevel = m.RecordsFormatLevel,
                ParentID = m.ParentID,
                ContentGroup = m.ContentGroup,
                Sort = m.Sort,
                RecordsFormatContent = m.RecordsFormatContent,
                AssessCode = m.AssessCode,
                AssessListID = m.AssessListID,
                ControlerType = m.ControlerType,
                Point = m.Point,
                Description = m.Description,
                MaxPoint = m.MaxPoint,
                FontSize = m.FontSize,
                FontColor = m.FontColor,
                Font = m.Font,
                Width = m.Width,
                BackGroundColor = m.BackGroundColor,
                RowSpan = m.RowSpan,
                ColSpan = m.ColSpan,
                NursingInterventionMainID = m.NursingInterventionMainID,
                DefaultFlag = m.DefaultFlag,
                ItemAssessListID = m.ItemAssessListID,
                ShowMessage = m.ShowMessage,
                API = m.API
            }).OrderBy(m => m.RecordListID).ThenBy(m => m.Sort).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<RecordsFormatInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.RecordsFormat.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*")
                .OrderBy(m => m.RecordListID).ThenBy(m => m.Sort).ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.RecordsFormat.GetKey(_sessionCommonServer);
        }

        public async Task<RecordsFormatInfo> GetByRecordFormatID(int formatID)
        {
            var datas = await GetCacheAsync() as List<RecordsFormatInfo>;
            return datas.Where(m => m.ID == formatID).FirstOrDefault();
        }
    }
}