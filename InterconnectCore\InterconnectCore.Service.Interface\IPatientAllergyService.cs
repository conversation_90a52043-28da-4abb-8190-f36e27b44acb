﻿using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IPatientAllergyService
    {
        /// <summary>
        /// 同步患者过敏史史信息
        /// </summary>
        /// <param name="patientAllergicViewList"></param>
        /// <returns></returns>
        Task<bool> SyncPatientAllergic(List<PatientAllergicView> patientAllergicViewList);
    }
}
