﻿using System.Collections.Generic;

namespace Medical.Common
{
    public class AssessValueUtil
    {
        /// <summary>
        /// 根据给出的AssessListID，从字典中找到，并格式化AssessValue内容，如果没有从集合中找到，则返回null
        /// </summary>
        /// <typeparam name="T">要格式化成的类型</typeparam>
        /// <param name="details">明细字典,Key:AssessListID,Value:AssessValue</param>
        /// <param name="assessListID">要查找的评估项</param>
        /// <returns></returns>
        public static T Parse<T>(Dictionary<int, string> details, int assessListID)
        {
            details.TryGetValue(assessListID, out var detailValue);

            if (string.IsNullOrEmpty(detailValue) || typeof(T) == typeof(string))
            {
                return (T)(object)detailValue;
            }

            if (typeof(T) == typeof(short?) || typeof(T) == typeof(short))
            {
                short.TryParse(detailValue, out var value);
                return (T)(object)value;
            }
            if (typeof(T) == typeof(byte?) || typeof(T) == typeof(byte))
            {
                byte.TryParse(detailValue, out var value);
                return (T)(object)value;
            }
            if (typeof(T) == typeof(decimal?) || typeof(T) == typeof(decimal))
            {
                decimal.TryParse(detailValue, out var value);
                return (T)(object)value;
            }
            if (typeof(T) == typeof(int?) || typeof(T) == typeof(int))
            {
                int.TryParse(detailValue, out var value);
                return (T)(object)value;
            }
            return default;
        }
    }
}
