﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Models;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 同步字典数据控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Dictionary")]
    [EnableCors("any")]
    public class DictionaryController : ControllerBase
    {
        private readonly IDictionaryService _dictionaryService;
        /// <summary>
        /// 同步人员数据构造函数
        /// </summary>
        /// <param name="dictionaryService"></param>
        public DictionaryController(
            IDictionaryService dictionaryService
            )
        {
            _dictionaryService = dictionaryService;
        }
        /// <summary>
        /// 同步床位数据
        /// </summary>
        /// <param name="bedListInfos"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncBedList")]
        public async Task<ActionResult<bool>> SyncBedList([FromBody] List<BedListInfo> bedListInfos)
        {
            return await _dictionaryService.SyncBedListAsync(bedListInfos);
        }
        /// <summary>
        /// 同步科室和病区数据
        /// </summary>
        /// <param name="stationAndDepartmentInfos"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncStationAndDepartment")]
        public async Task<ActionResult<bool>> SyncStationAndDepartment([FromBody] StationAndDepartmentView stationAndDepartmentInfos)
        {
            return await _dictionaryService.SyncStationAndDepartmentAsync(stationAndDepartmentInfos);
        }
        /// <summary>
        /// 同步员工列表数据
        /// </summary>
        /// <param name="userList">员工列表</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncAllEmployeeData")]
        public async Task<IActionResult> SyncAllEmployeeData([FromBody] List<UserView> userList)
        {
            var result = new ResponseResult();
            result.Data = await _dictionaryService.SyncAllEmployeeData(userList);
            return result.ToJson();
        }

        /// <summary>
        /// 根据员工编号同步单个员工
        /// </summary>
        /// <param name="user">员工信息</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncOneEmployeeData")]
        public async Task<IActionResult> SyncOneEmployeeData([FromBody] UserView user)
        {
            var result = new ResponseResult();
            result.Data = await _dictionaryService.SyncOneEmployeeData(user);
            return result.ToJson();
        }
        /// <summary>
        /// 同步医嘱字典
        /// </summary>
        /// <param name="orderDictViewList">HIS系统医嘱字典数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPhysionOrder")]
        public async Task<IActionResult> SyncPhysionOrder([FromBody] List<OrderDictView> orderDictViewList)
        {
            var successFlag = await _dictionaryService.SyncPhysionOrder(orderDictViewList);
            var result = new ResponseResult()
            {
                Code = successFlag ? 1 : -1,
                Data = successFlag,
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步药品字典
        /// </summary>
        /// <param name="drugDictViewList">His药品数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncDrugList")]
        public async Task<IActionResult> SyncDrugList([FromBody] List<DrugDictView> drugDictViewList)
        {
            var successFlag = await _dictionaryService.SyncDrugList(drugDictViewList);
            var result = new ResponseResult()
            {
                Code = successFlag ? 1 : -1,
                Data = successFlag,
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步用户CA数据
        /// </summary>
        /// <param name="employeeCAViews"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncEmployeeCAData")]
        public async Task<IActionResult> SyncEmployeeCAData([FromBody] List<EmployeeCAView> employeeCAViews)
        {
            var successFlag = await _dictionaryService.SyncEmployeeCAData(employeeCAViews);
            var result = new ResponseResult()
            {
                Code = successFlag ? 1 : -1,
                Data = successFlag,
            };
            return result.ToJson();
        }
    }
}
