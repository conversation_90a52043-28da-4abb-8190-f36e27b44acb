﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NoticeColumnsRepository : INoticeColumnsRepository
    {
        #region -- 字段
        private readonly MedicalDbContext _medicalDbContext;
        #endregion

        #region -- 构造函数
        public NoticeColumnsRepository(MedicalDbContext medicalDbContext)
        {
            this._medicalDbContext = medicalDbContext;
        }
        #endregion

        public async Task<NoticeColumnsInfo> GetAsync(int stationID)
        {
            return await _medicalDbContext.NoticeColumns.Where(m => m.StationID == stationID).SingleOrDefaultAsync();
        }
    }
}
