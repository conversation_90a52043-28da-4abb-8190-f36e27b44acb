﻿namespace InterconnectCore.ViewModels
{
    public class InpatientDataView
    {
        /// <summary>
        /// 过敏标志
        /// </summary>
        public string anaphyFlag { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string bedNo { get; set; }

        /// <summary>
        /// 出生地名称
        /// </summary>
        public string? birthArea { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public string birthDate { get; set; }

        /// <summary>
        /// 血型编码
        /// </summary>
        public string? bloodCode { get; set; }

        /// <summary>
        /// 血型
        /// </summary>
        public string? bloodDress { get; set; }

        /// <summary>
        /// 患者CARDNO
        /// </summary>
        public string cardNo { get; set; }

        /// <summary>
        /// 医师代码(主治)
        /// </summary>
        public string chargeDocCode { get; set; }

        /// <summary>
        /// 医师姓名(主治)
        /// </summary>
        public string chargeDocName { get; set; }

        /// <summary>
        /// 医师代码(主任)
        /// </summary>
        public string chiefDocCode { get; set; }

        /// <summary>
        /// 医师姓名(主任)
        /// </summary>
        public string chiefDocName { get; set; }

        /// <summary>
        /// 国籍
        /// </summary>
        public string counCode { get; set; }

        /// <summary>
        /// 病情标志
        /// </summary>
        public string criticalFlag { get; set; }

        /// <summary>
        /// 科室代码
        /// </summary>
        public string deptCode { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>
        public string deptName { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        public string dist { get; set; }

        /// <summary>
        /// 护士代码(责任)
        /// </summary>
        public string dutyNurseCode { get; set; }

        /// <summary>
        /// 护士姓名(责任)
        /// </summary>
        public string dutyNurseName { get; set; }

        /// <summary>
        /// 余额(未结)
        /// </summary>
        public string freeCost { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string genderCode { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public string? height { get; set; }

        /// <summary>
        /// 重大疾病标志
        /// </summary>
        public string hepatitisFlag { get; set; }

        /// <summary>
        /// 患者住院流水号
        /// </summary>
        public string hisPatientNo { get; set; }

        /// <summary>
        /// 家庭地址
        /// </summary>
        public string homeAdd { get; set; }

        /// <summary>
        /// 家庭电话
        /// </summary>
        public string homeTel { get; set; }

        /// <summary>
        /// 家庭邮编
        /// </summary>
        public string homeZip { get; set; }

        /// <summary>
        /// 医师代码(住院)
        /// </summary>
        public string houseDocCode { get; set; }

        /// <summary>
        /// 医师姓名(住院)
        /// </summary>
        public string houseDocName { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string idenno { get; set; }

        /// <summary>
        /// 入院途径
        /// </summary>
        public string inAvenuei { get; set; }

        /// <summary>
        /// 入院情况
        /// </summary>
        public string inCircs { get; set; }

        /// <summary>
        /// 入院日期
        /// </summary>
        public string inDate { get; set; }

        /// <summary>
        /// 入院来源
        /// </summary>
        public string inSource { get; set; }

        /// <summary>
        /// 在院状态
        /// </summary>
        public string inState { get; set; }

        /// <summary>
        /// 入院次数
        /// </summary>
        public string inTimes { get; set; }

        /// <summary>
        /// 是否新生儿
        /// </summary>
        public string? isNewBorn { get; set; }

        /// <summary>
        /// 联系人地址
        /// </summary>
        public string linkmanAdd { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string linkmanName { get; set; }

        /// <summary>
        /// 联系人电话
        /// </summary>
        public string linkmanTel { get; set; }

        /// <summary>
        /// 主诊断
        /// </summary>
        public string mainDiagnose { get; set; }

        /// <summary>
        /// 婚姻状况
        /// </summary>
        public string mari { get; set; }

        /// <summary>
        /// 医疗证号
        /// </summary>
        public string? mcardNo { get; set; }

        /// <summary>
        /// 患者合同类型
        /// </summary>
        public string? medicalType { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 民族
        /// </summary>
        public string? nationCode { get; set; }

        /// <summary>
        /// 护理单元代码
        /// </summary>
        public string nurseCellCode { get; set; }

        /// <summary>
        /// 护理单元名称
        /// </summary>
        public string nurseCellName { get; set; }

        /// <summary>
        /// 出院日期
        /// </summary>
        public string outDate { get; set; }

        /// <summary>
        /// 合同代码
        /// </summary>
        public string pactCode { get; set; }

        /// <summary>
        /// 合同单位名称
        /// </summary>
        public string pactName { get; set; }

        /// <summary>
        /// EMR患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 患者住院号
        /// </summary>
        public string patientNo { get; set; }

        /// <summary>
        /// 结算类别
        /// </summary>
        public string paykindCode { get; set; }

        /// <summary>
        /// 预交金
        /// </summary>
        public string prepayCost { get; set; }

        /// <summary>
        /// 职业代码
        /// </summary>
        public string profCode { get; set; }

        /// <summary>
        /// 职业名称
        /// </summary>
        public string profName { get; set; }

        /// <summary>
        /// 联系人编码关系
        /// </summary>
        public string relaCode { get; set; }

        /// <summary>
        /// 联系人关系
        /// </summary>
        public string relaName { get; set; }

        /// <summary>
        /// 患者姓名拼音码
        /// </summary>
        public string? spellCode { get; set; }

        /// <summary>
        /// 护理级别
        /// </summary>
        public string? tend { get; set; }

        /// <summary>
        /// 费用汇总
        /// </summary>
        public string totCost { get; set; }

        /// <summary>
        /// 体重
        /// </summary>
        public string? weight { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string workName { get; set; }

        /// <summary>
        /// 单位电话
        /// </summary>
        public string? workTel { get; set; }

        /// <summary>
        /// 单位邮编
        /// </summary>
        public string workZip { get; set; }

        /// <summary>
        /// 转归标记
        /// </summary>
        public string? zg { get; set; }
    }
}