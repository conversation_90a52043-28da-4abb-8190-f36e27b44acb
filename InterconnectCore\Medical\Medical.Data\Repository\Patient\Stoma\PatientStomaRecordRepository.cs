﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientStomaRecordRepository : IPatientStomaRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private const string UROSTOMA = "3231";

        public PatientStomaRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据造口ID获取造口记录
        /// </summary>
        /// <param name="StomaRecordID"></param>
        /// <returns></returns>
        public async Task<PatientStomaRecordInfo> GetByStomaRecordIDAsync(string StomaRecordID)
        {
            return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.PatientStomaRecordID == StomaRecordID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据AssessMainID获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="patientAssessMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientStomaRecordInfo>> GetByStomaRecordByAssessMainID(string inpatientID, string patientAssessMainID)
        {
            return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.InpatientID == inpatientID && t.AssessMainID == patientAssessMainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据病人ChartNo获取造口记录
        /// </summary>
        /// <param name="ChartNo">住院号</param>
        /// <returns></returns>
        public async Task<List<PatientStomaRecordInfo>> GetListByChartNoAsync(string ChartNo, bool? hostoryFlag)
        {
            if (hostoryFlag == null)
            {
                return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.ChartNo == ChartNo && t.DeleteFlag != "*").ToListAsync();
            }
            else if (hostoryFlag.Value)
            {
                return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.ChartNo == ChartNo && t.DeleteFlag != "*" && t.EndDate.HasValue).ToListAsync();
            }
            else
            {
                return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.ChartNo == ChartNo && t.DeleteFlag != "*" && !t.EndDate.HasValue).ToListAsync();
            }
        }
        /// <summary>
        /// 根据病人InpatientID获取造口记录
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <returns></returns>
        public async Task<PatientStomaRecordInfo> GetByInpatientIDAsync(string InpatientID)
        {
            return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.InpatientID == InpatientID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        //根据病人InpatientID获取造口记录(电子病历用)
        public async Task<List<PatientStomaRecordInfo>> GetListByInpatientIDAsync(string InpatientID)
        {
            return await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.InpatientID == InpatientID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 取得病人造口照顾内容(交班带入造口内容)
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<HandoverStomaCareIntervention>> GetPatientStomaCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientStomaCareMainInfos
                               join b in _medicalDbContext.PatientStomaRecordInfos on a.PatientStomaRecordID equals b.PatientStomaRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverStomaCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   StomaKind = b.StomaKind,
                                   BodyPartID = b.BodyPartID,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   BodyPartName = "",
                                   StomaCode = b.StomaCode,
                                   PatientStomaRecordID = a.PatientStomaRecordID,
                                   StomaOpen = a.StomaOpen,
                                   //排气
                                   Exhaust = a.Exhaust,
                                   //排便
                                   Defecation = a.Defecation,
                                   //排泄物性质
                                   FecalProperties = a.FecalProperties,
                                   //用物
                                   Utilities = a.Utilities,
                                   //黏膜颜色
                                   MucosalColor = a.MucosalColor,
                                   //周围皮肤完整性
                                   SurroundSkin = a.SurroundSkin,
                                   AssessMainID = b.AssessMainID,
                                   HandOverContent = a.HandOverContent
                               }).ToListAsync();
            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientStomaRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }

        /// <summary>
        ///  获取病人未结束造口记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientStomaRecordInfo>> GetUnFinishedAsync(string inpatientID)
        {
            var data = await GetListByInpatientIDAsync(inpatientID);

            return data.Where(m => m.EndDate.ToString().Length == 0).ToList();
        }

        public async Task<AssessTimeView> GetAssessTimeByID(string assessMainID)
        {
            var assessMain = await _medicalDbContext.PatientStomaRecordInfos.Where(m => m.PatientStomaRecordID == assessMainID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.StartDate, StartTime = m.StartTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }
        /// <summary>
        /// 根据病人住院序号获取造口View
        /// </summary>
        /// <param name="InpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<StomaRecordView>> GetStomaViewsByInpatientID(string InpatientID)
        {
            return await _medicalDbContext.PatientStomaRecordInfos
                .Where(t => t.InpatientID == InpatientID && t.DeleteFlag != "*")
                .Select(m => new StomaRecordView
                {
                    PatientStomaRecordID = m.PatientStomaRecordID,
                    StomaKind = m.StomaKind,
                    BodyPartID = m.BodyPartID
                }).ToListAsync();
        }
        /// <summary>
        /// 获取病人最早未停的泌尿造口开始时间
        /// </summary>
        /// <param name="chartNo">住院号</param>
        /// <returns></returns>
        public async Task<DateTime?> GetEarliestUrostomaStartDateTime(string chartNo)
        {
            var data = await _medicalDbContext.PatientStomaRecordInfos.Where(t => t.ChartNo == chartNo && t.StomaKind == UROSTOMA
             && !t.EndDate.HasValue && t.DeleteFlag != "*").Select(m => new { m.StartDate, m.StartTime }).ToListAsync();
            if (!data.Any())
            {
                return null;
            }
            return data.Min(m => m.StartDate.Add(m.StartTime));
        }
    }
}