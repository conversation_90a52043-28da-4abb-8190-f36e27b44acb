﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class FilesDBContext
    {
        //public DbSet<PatientPDFFilesInfo> PatientPDFFiles { get; set; }
        //public DbSet<PatientMedicineScheduleInternationInfo> PatientMedicineSchedules { get; set; }
        /// <summary>
        /// 医嘱回写中间表
        /// </summary>
        public DbSet<OrderExecLogInfo> OrderExecLogInfos { get; set; }
        /// <summary>
        /// 消息通知表
        /// </summary>
        public DbSet<SendMessageInfo> SendMessageInfos { get; set; }
        /// <summary>
        /// 病历转换日志表
        /// </summary>
        public DbSet<DocumentChangeLogInfo> DocumentChangeLogInfos { get; set; }
        /// <summary>
        /// 病历转换护理记录日志表
        /// </summary>
        public DbSet<DocumentChangeNursingrecordLog> DocumentChangeNursingrecordLogs { get; set; }
        /// <summary>
        /// 病历转换护理问题日志表
        /// </summary>
        public DbSet<DocumentChangeProblemLogInfo> DocumentChangeProblemLogInfos { get; set; }
    }
}
