﻿namespace Medical.Models
{
    public class PatientTestResultView
    {
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 检验号
        /// </summary>
        public string TestNo { get; set; }
        /// <summary>
        /// 检验码
        /// </summary>
        public string TestCode { get; set; }
        /// <summary>
        /// 检验日期
        /// </summary>
        public DateTime TestDate { get; set; }
        /// <summary>
        /// 检验项目
        /// </summary>
        public string TestItem { get; set; }
        /// <summary>
        /// 检验值
        /// </summary>
        public string TestValue { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 值范围
        /// </summary>
        public string NormalRange { get; set; }
        /// <summary>
        /// 值高低
        /// </summary>
        public string NormalAbnormal { get; set; }
        /// <summary>
        /// 检验大项
        /// </summary>
        public string TestGroupName { get; set; }
        /// <summary>
        /// 检验大项码
        /// </summary>
        public string TestGroupCode { get; set; }
        /// <summary>
        /// 检体
        /// </summary>
        public string Specimen { get; set; }
        /// <summary>
        /// 检体编码
        /// </summary>
        public string SpecimenCode { get; set; }
    }
}