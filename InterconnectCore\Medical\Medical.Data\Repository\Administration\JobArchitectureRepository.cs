﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class JobArchitectureRepository : IJobArchitectureRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public JobArchitectureRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }
        public async Task<List<JobArchitectureInfo>> GetByAllData()
        {
            return await _medicalDbContext.JobArchitectureInfos.Where(m => m.DeleteFlag != "*").OrderBy(m => m.JobArchitectureID).ToListAsync();
        }

        public async Task<bool> GetByDelete(int JobId)
        {
            List<JobArchitectureInfo> result = new List<JobArchitectureInfo>();
            result = (from a in _medicalDbContext.JobArchitectureInfos
                      where a.JobArchitectureID == JobId
                      select a).ToList();
            _medicalDbContext.JobArchitectureInfos.RemoveRange(result);
            await _medicalDbContext.SaveChangesAsync();
            return true;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<JobArchitectureInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.JobArchitectureInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }


        public string GetCacheType()
        {
            return CacheType.JobArchitecture.ToString();
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
    }
}
