﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SpecialFieldListRepository : ISpecialFieldListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public SpecialFieldListRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<SpecialFieldListInfo>> GetListAsync()
        {
            return await _medicalDbContext.SpecialFieldListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}