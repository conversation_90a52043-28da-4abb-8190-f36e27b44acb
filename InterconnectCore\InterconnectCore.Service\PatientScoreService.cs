﻿using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using NLog;

namespace InterconnectCore.Service
{
    public class PatientScoreService : IPatientScoreService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAssessScoreRangeRepository _assessScoreRangeRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly ExternalCommonService _externalCommonService;
        private readonly CommonHelper _commonHelper;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        public PatientScoreService(IAssessScoreRangeRepository assessScoreRangeRepository
            , IInpatientDataRepository inpatientDataRepository
            , ExternalCommonService externalCommonService
            , CommonHelper commonHelper
            , IOptions<SystemConfig> config)
        {
            _assessScoreRangeRepository = assessScoreRangeRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _externalCommonService = externalCommonService;
            _commonHelper = commonHelper;
            _config = config;
        }

        /// <summary>
        /// 同步患者VTE数据
        /// </summary>
        /// <param name="patientVTEViews"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientVTE(List<PatientVTEView> patientVTEViews)
        {
            var successFlag = true;
            if (patientVTEViews == null || patientVTEViews.Count == 0)
            {
                _logger.Error("同步患者VTE数据失败，patientVTEViews为空");
                return false;
            }
            var assessScoreRangeInfos = await _assessScoreRangeRepository.GetAsync();
            if (assessScoreRangeInfos == null || assessScoreRangeInfos.Count == 0)
            {
                _logger.Error("同步患者VTE数据失败，AssessScoreRange为空");
                return false;
            }
            foreach (var patientVTEView in patientVTEViews)
            {
                var scoreRange = assessScoreRangeInfos.Where(m => m.RecordListID == patientVTEView.RecordListID && patientVTEView.ScorePoint >= m.ScoreLowerLimit && patientVTEView.ScorePoint <= m.ScoreUpperLimit).FirstOrDefault();
                if (scoreRange == null || !scoreRange.AssessListID.HasValue)
                {
                    _logger.Error($"同步患者VTE数据失败，未获取到评估分数范围数据RecordListID={patientVTEView.RecordListID}，ScorePoint{patientVTEView.ScorePoint}");
                    continue;
                }
                var inpatientData = await _inpatientDataRepository.GetByCaseNumberAsync(patientVTEView.CaseNumber);
                if (inpatientData == null)
                {
                    _logger.Error($"同步患者VTE数据失败，未获取到患者信息CaseNumber={patientVTEView.CaseNumber}");
                    continue;
                }
                var patientProfile = _externalCommonService.CreateProfile(inpatientData, "VTEScore", scoreRange.AssessListID.Value, "", _config.Value.HospitalID, MODIFYPERSONID);
                try
                {
                    await _commonHelper.AddProfile([patientProfile]);
                }
                catch (Exception ex)
                {
                    successFlag = false;
                    _logger.Error("同步患者VTE数据呼叫Medial写PatientProfile失败" + ex.ToString());
                    continue;
                }
            }
            return successFlag;
        }
    }
}