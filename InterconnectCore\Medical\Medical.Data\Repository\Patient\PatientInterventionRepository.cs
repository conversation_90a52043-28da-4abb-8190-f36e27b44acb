﻿/*
 2021-12-27 2291 护理计划需要可以调整时间,重构时新增取得问题勾选的错施方法(GetPatientProrlemSelectInterventions) -正元
 2021-12-30 2291 护理计划需要可以调整时间,重构时新增取得简易病人已选错施方法(GetSimplePatientIntervention) -正元
 2021-12-30 2291 护理计划需要可以调整时间,重构时新增取得病人现有护理措施(GetNowPatientInterventions) -正元
 2021-12-30 2291 护理计划需要可以调整时间,调整取得患者首页计划内容时新增取得简易护理措施内容(GetSimpleNowPatientInterventions) -正元
 2022-1-13 2292 集束护理查询修改，新增集束护理保存处理，护理计划、集束护理提交参数修改 -李欣
 2022-04-26 2266 CDA护理计划数据推送需数据异动状态调整GetNursingPlanView不过滤PatientIntervention.DeleteFlag -En
 2022-01-13 2292 集束护理查询修改，新增集束护理保存处理，护理计划、集束护理提交参数修改 -李欣
 */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.CDADocument;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientInterventionRepository : IPatientInterventionRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientInterventionRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 取得病人现有护理措施
        /// </summary>
        /// <param name="inPatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientInterventionInfo>> GetAsync(string inpatientID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID && m.EndDate == null && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人本病区现有未结束措施
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientInterventionInfo>> GetUnEndAsync(string inpatientID, int stationID)
        {
            //分为3部分
            //1、没有结束日期的
            //2、结束日期大于今天日期的
            //3、结束日期等于今天，结束时间大于当前时间的
            //return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID
            //&& (m.EndDate == null
            //|| (m.EndDate.HasValue && m.EndDate > DateTime.Now.Date)
            //|| (m.EndDate.HasValue && m.EndDate == DateTime.Now.Date && m.EndTime > DateTime.Now.TimeOfDay))
            //&& m.DeleteFlag != "*").ToListAsync();

            var result = await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID
            && m.StationID == stationID
             && m.DeleteFlag != "*").ToListAsync();
            result = result.Where(m => m.EndDate == null || (m.EndDate.HasValue && m.EndDate >= DateTime.Now.Date)).ToList();

            for (int i = result.Count - 1; i >= 0; i--)
            {
                if (!result[i].EndDate.HasValue)
                {
                    continue;
                }
                if (result[i].EndDate == DateTime.Now.Date && result[i].EndTime < DateTime.Now.TimeOfDay)
                {
                    result.Remove(result[i]);
                }
            }

            return result;
        }

        public async Task<List<PatientInterventionInfo>> GetByProblemAsync(DateTime startTime, DateTime endTime, string inpatientID, int problemID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.StartDate <= endTime
                 && m.ProblemID == problemID && m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 取得病人现有护理措施
        /// </summary>
        /// <param name="inPatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientInterventionInfo>> GetByProblemsAsync(string inpatientID, string[] patientProblemIds)
        {
            return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID && patientProblemIds.Contains(m.PatientProblemID)).ToListAsync();

        }

        public async Task<List<PatientInterventionInfo>> GetProblemInterventionAsync(string patientProblemID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientInterventionInfo>> GetProblemInterventionAllAsync(string patientProblemID)
        {
            return await _dbContext.PatientInterventions.OrderByDescending(m => m.ModifyDate).Where(m => m.PatientProblemID == patientProblemID).ToListAsync();
            //return await _dbContext.PatientInterventions.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientInterventionInfo>> GetByInpatientIDAndStationID(string inpatientID, int stationID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID
                && m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientInterventionInfo> GetByID(string ID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.PatientInterventionID == ID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientInterventionInfo>> GetAllAsync(string inpatientID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<DateTime?> GetFirstNursingPlanTime(string hospitalID)
        {
            return await _dbContext.PatientInterventions.Select(m => m.ModifyDate).OrderBy(m => m).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取护理计划CDA需转换数据
        /// </summary>
        /// <param name="hospitalID">医院代码</param>
        /// <param name="NursingPlanTime">获取数据的开始时间</param>
        /// <returns></returns>
        public async Task<List<CDA_NursingPlansInfo>> GetNursingPlanView(string hospitalID, DateTime NursingPlanTime)
        {
            var result = await (from PatientIntervention in _dbContext.PatientInterventions
                                join bedList in _dbContext.BedListInfos on new { ID = PatientIntervention.BedID, HospitalID = hospitalID } equals new { bedList.ID, bedList.HospitalID }
                                join inpatient in _dbContext.InpatientDatas on PatientIntervention.InpatientID equals inpatient.ID
                                join patientBasic in _dbContext.PatientBasicDatas on PatientIntervention.PatientID equals patientBasic.PatientID
                                join station in _dbContext.StationList on new { ID = PatientIntervention.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _dbContext.DepartmentListInfos on inpatient.DepartmentListID equals departmnt.ID
                                join employee in _dbContext.Users on PatientIntervention.ModifyPersonID equals employee.UserID
                                join PatientProblem in _dbContext.NursingProblems on PatientIntervention.ProblemID equals PatientProblem.ID
                                where PatientIntervention.StartDate >= NursingPlanTime && patientBasic.DeleteFlag != "*" && station.DeleteFlag != "*"
                                && departmnt.DeleteFlag != "*" && PatientProblem.Language == 1 && departmnt.HospitalID == hospitalID && employee.HospitalID == hospitalID
                                && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                                select new CDA_NursingPlansInfo
                                {
                                    DCID = PatientIntervention.PatientInterventionID,
                                    InpatientID = PatientIntervention.InpatientID,
                                    ChartNo = PatientIntervention.ChartNo,
                                    LocalChartNO = patientBasic.LocalChartNO,
                                    CaseNumber = PatientIntervention.CaseNumber,
                                    VisitID = inpatient.NumberOfAdmissions.ToString(),
                                    PatientType = "04",
                                    EffectiveFlag = string.IsNullOrEmpty(PatientIntervention.DeleteFlag) ? "1" : "0",
                                    IdCard = patientBasic.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(patientBasic.IdentityID) : patientBasic.IdentityID,
                                    Name = patientBasic.PatientName,
                                    Sex = patientBasic.Gender,
                                    NursingLevel = inpatient.NursingLevel,
                                    Age = inpatient.Age.ToString(),
                                    MonthAge = patientBasic.DateOfBirth.HasValue ? AgeCalculat.GetMonths(patientBasic.DateOfBirth.Value, PatientIntervention.StartDate).ToString() : "",
                                    DeptCode = departmnt.DepartmentCode,
                                    DeptName = departmnt.Department,
                                    WardAreaCode = station.StationCode,
                                    WardAreaName = station.StationName,
                                    SickbedId = PatientIntervention.BedNumber,
                                    SickRoomId = bedList.RoomCode,
                                    ClinicalDiagnosisCode = inpatient.ICDCode,
                                    ClinicalDiagnosis = inpatient.Diagnosis,
                                    TimeStamp = PatientIntervention.ModifyDate,
                                    NursingProblem = PatientProblem.Problem,
                                    NurseSign = employee.Name,
                                    SignDateTime = PatientIntervention.StartDate.Add(PatientIntervention.StartTime),
                                    ResponsibilityNurse = employee.Name,
                                    ResponsibilityNurseSignDT = PatientIntervention.StartDate.Add(PatientIntervention.StartTime),
                                    DeleteFlag = PatientIntervention.DeleteFlag
                                }).ToListAsync();
            return result;
        }
        /// <summary>
        /// 根据inpatientID集合获取数据
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientInterventionInfo>> GetByInpatientIDsAsync(string[] inpatientIDs)
        {
            var patientInterventionList = new List<PatientInterventionInfo>();
            for (int i = 0; i < inpatientIDs.Length; i++)
            {
                var tempList = await _dbContext.PatientInterventions.Where(m => m.PatientProblemID == inpatientIDs[i]).ToListAsync();
                patientInterventionList = patientInterventionList.Union(tempList).ToList();
            }
            return patientInterventionList;
        }

        public async Task<List<PatientInterventionInfo>> GetByAssessMainID(string assessMainID)
        {
            var datas = await _dbContext.PatientInterventions.Where(m => m.PatientAssessMainID == assessMainID && m.DeleteFlag != "*").ToListAsync();

            return datas;
        }

        //2021-12-27 一次取得问题勾选的措施,后续用PatientProblemID Count有多少量判断是否有勾选
        public async Task<List<string>> GetPatientProrlemSelectInterventions(string inpatientID, int stationID)
        {
            var query = await (from m in _dbContext.PatientProblems
                               join n in _dbContext.PatientInterventions on m.ID equals n.PatientProblemID
                               where m.InpatientID == inpatientID && m.StationID == stationID && m.EndDate == null && m.DiagnoseFlag != "O" && m.DeleteFlag != "*"
                                   && n.DeleteFlag != "*"
                               select n.PatientProblemID)
                               .ToListAsync();

            return query;
        }

        //2021-12-28 取得简易病人已选错施
        public async Task<List<PatientInterventionSimpleView>> GetSimplePatientIntervention(string patientProblemID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.PatientProblemID == patientProblemID && m.DeleteFlag != "*")
                .Select(m => new PatientInterventionSimpleView
                {
                    PatientInterventionID = m.PatientInterventionID,
                    InterventionID = m.InterventionID,
                    Sort = m.Sort,
                    Frequency = m.Frequency,
                    FrequencyID = m.FrequencyID,
                    FirstDayFlag = m.FirstDayFlag,
                    StartDate = m.StartDate,
                    StartTime = m.StartTime,
                    EndDate = m.EndDate,
                    EndTime = m.EndTime,
                    NursingProblemID = m.ProblemID
                })
                .ToListAsync();
        }

        public async Task<List<PatientInterventionInfo>> GetNowPatientInterventions(string inpatientID, int stationID)
        {
            return await (from a in _dbContext.PatientProblems
                          join b in _dbContext.PatientInterventions on a.ID equals b.PatientProblemID
                          where a.InpatientID == inpatientID && a.StationID == stationID && a.EndDate == null && a.DeleteFlag != "O" && a.DeleteFlag != "*"
                             && b.StationID == stationID && b.DeleteFlag != "*"
                          select b
                               ).ToListAsync();
        }

        public async Task<List<PatientInterventionSimpleView>> GetSimpleNowPatientInterventions(string inpatientID, int stationID)
        {
            return await (from a in _dbContext.PatientProblems
                          join b in _dbContext.PatientInterventions on a.ID equals b.PatientProblemID
                          where a.InpatientID == inpatientID && a.StationID == stationID && a.EndDate == null && a.DeleteFlag != "O" && a.DeleteFlag != "*"
                             && b.StationID == stationID && b.DeleteFlag != "*"
                          select new PatientInterventionSimpleView
                          {
                              InterventionID = b.InterventionID,
                              FrequencyID = b.FrequencyID,
                              NursingProblemID = a.ProblemID,
                              EndDate = b.EndDate,
                              EndTime = b.EndTime
                          }).ToListAsync();
        }

        public async Task<List<KeyValueString>> GetEmployeeIDByID(List<string> dcids)
        {
            return await _dbContext.PatientInterventions.Where(m => dcids.Contains(m.PatientInterventionID)).Select(m => new KeyValueString
            {
                Key = m.PatientInterventionID,
                Value = m.ModifyPersonID
            }).ToListAsync();
        }
        public async Task<List<int>> GetPatienterventions(string inpatientID)
        {
            var query = await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID && m.EndDate == null && m.DeleteFlag != "*")
                .Select(m => m.ProblemID).ToListAsync();
            return query.Distinct().ToList();
        }
        public async Task<List<PatientInterventionInfo>> GetAsyncByShift(string inpatientID, DateTime endDate)
        {
            return await _dbContext.PatientInterventions.Where(p => p.InpatientID == inpatientID && (p.EndDate == null || p.EndDate >= endDate) && p.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientInterventionInfo>> GetByInterventionID(string inpatientID, int interventionID)
        {
            return await _dbContext.PatientInterventions.Where(m => m.InpatientID == inpatientID && m.InterventionID == interventionID && m.EndDate == null && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
