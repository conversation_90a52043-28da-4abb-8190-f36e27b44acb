﻿using Medical.Models;
using System.ComponentModel;

namespace InterconnectCore.ViewModels
{
    public static class EnumUtility
    {
        public enum EventType
        {
            /// <summary>
            /// 病人基本信息同步
            /// </summary>
            [Description("病人基本信息同步")]
            PatientBasicDataSync = 1,
            /// <summary>
            /// 病人在院信息同步
            /// </summary>
            [Description("病人在院信息同步")]
            InPatientDataSync = 2,
            /// <summary>
            /// 检验结果同步
            /// </summary>
            [Description("检验结果同步")]
            TestResultSync = 3,
            /// <summary>
            /// 单位床位同步
            /// </summary>
            [Description("单位床位同步")]
            BedSync = 4,
            /// <summary>
            /// 用户信息同步
            /// </summary>
            [Description("用户信息同步")]
            UserSync = 5,
            /// <summary>
            /// 医嘱信息同步
            /// </summary>
            [Description("医嘱信息同步")]
            OrderSync = 6,
            /// <summary>
            /// 血袋同步
            /// </summary>
            [Description("血袋同步")]
            BloodBag = 7
        }
    }
}