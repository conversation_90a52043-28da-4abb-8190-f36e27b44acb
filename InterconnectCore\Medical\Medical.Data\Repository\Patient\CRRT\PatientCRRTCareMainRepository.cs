﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientCRRTCareMainRepository : IPatientCRRTCareMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientCRRTCareMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }


        /// <summary>
        /// 根据MainID获取主表记录
        /// </summary>
        /// <param name="mainID">主表记录</param>
        /// <returns></returns>
        public async Task<PatientCRRTCareMainInfo> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => m.PatientCRRTCareMainID == mainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据RecordID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientCRRTCareMainInfo>> GetInfosByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取上一次的维护记录ByRecordID
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<CRRTLastCareView> GetLastCareMainByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos
                .Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .ThenByDescending(m => m.NumberOfAssessment)
                .Select(m => new CRRTLastCareView
                {
                    PatientCRRTCareMainID = m.PatientCRRTCareMainID,
                    StationID = m.StationID,
                    NumberOfAssessment = m.NumberOfAssessment
                })
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据RecordID获取开始评估记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientCRRTCareMainInfo> GetStartCareMainByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos.Where(t => t.PatientCRRTRecordID == recordID && t.DeleteFlag != "*" && t.RecordsCode.Contains("Start")).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主记录ID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<CRRTCareMainView>> GetCareMainViewsByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*")
                .Select(m => new CRRTCareMainView
                {
                    PatientCRRTCareMainID = m.PatientCRRTCareMainID,
                    PatientCRRTRecordID = m.PatientCRRTRecordID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    RecordsCode = m.RecordsCode,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    TempSetting = m.TempSetting,
                    AnticoagulantLoadingDose = m.AnticoagulantLoadingDose,
                    AnticoagulantInfusion = m.AnticoagulantInfusion,
                    AccessPressure = m.AccessPressure,
                    ReturnPressure = m.ReturnPressure,
                    TMP = m.TMP,
                    FilterPressureDrop = m.FilterPressureDrop,
                    FilterPressure = m.FilterPressure,
                    EffluentPressure = m.EffluentPressure,
                    FluidRemovalAccumulation = m.FluidRemovalAccumulation,
                    BloodFlowRate = m.BloodFlowRate,
                    PlasmaFlowrate = m.PlasmaFlowrate,
                    PBPFlowRate = m.PBPFlowRate,
                    PreReplacementFlowRate = m.PreReplacementFlowRate,
                    PostReplacementFlowRate = m.PostReplacementFlowRate,
                    DialysateFlowRate = m.DialysateFlowRate,
                    FluidRemoval = m.FluidRemoval,
                    InformPhysician = m.InformPhysician,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    NumberOfAssessment = m.NumberOfAssessment,
                    InpatientID = m.InpatientID,
                }).ToListAsync();
        }

        /// <summary>
        /// 获取当前病人所有主记录的选框勾选状态
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientBringView>> GetRecordsBringViewsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.RecordsCode == "CRRTStart"
            )
                .Select(m => new PatientBringView
                {
                    RecordID = m.PatientCRRTRecordID,
                    CareMainID = m.PatientCRRTCareMainID,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InformPhysician = m.InformPhysician
                }).ToListAsync();
        }

        /// <summary>
        /// 获取构造评估模板时，所需要的历史评估主表数据
        /// </summary>
        /// <param name="recordID">主记录</param>
        /// <param name="modifyTableName">异动表表名分类</param>
        /// <returns>
        /// Item1: 病区ID
        /// Item2：主表ID
        /// </returns>
        public async Task<Tuple<int, string>> GetAssessHistoryDataByRecordID(string recordID, string modifyTableName)
        {
            if (modifyTableName == "Record")
            {
                // 主记录修改，取出指定的开始评估数据
                return await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => m.PatientCRRTRecordID == recordID && m.DeleteFlag != "*" && m.RecordsCode.Contains("Start"))
                    .Select(m => new Tuple<int, string>(m.StationID, m.PatientCRRTCareMainID)).FirstOrDefaultAsync();
            }
            else
            {
                // 主表新增，取本次记录最近一次例行评估的历史数据
                var historyCareMainInfo = await GetLastCareMainByRecordID(recordID);
                if (historyCareMainInfo == null)
                {
                    return new Tuple<int, string>(0, null);
                }
                return new Tuple<int, string>(historyCareMainInfo.StationID, historyCareMainInfo.PatientCRRTCareMainID);
            }
        }
        /// <summary>
        /// 根据主记录ID集合获取例行评估数据（电子病历使用）
        /// </summary>
        /// <param name="recordIDs">主记录ID集合</param>
        /// <returns></returns>
        public async Task<List<IGrouping<string, CRRTCareMainView>>> GetMaintainCareGroupByRecordIDs(List<string> recordIDs)
        {
            var datas = await _medicalDbContext.PatientCRRTCareMainInfos.Where(m => recordIDs.Contains(m.PatientCRRTRecordID) && m.DeleteFlag != "*" && m.RecordsCode == "CRRTMaintain")
                .Select(m => new CRRTCareMainView
                {
                    PatientCRRTRecordID = m.PatientCRRTRecordID,
                    PatientCRRTCareMainID = m.PatientCRRTCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    RecordsCode = m.RecordsCode,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    TempSetting = m.TempSetting,
                    AnticoagulantLoadingDose = m.AnticoagulantLoadingDose,
                    AnticoagulantInfusion = m.AnticoagulantInfusion,
                    AccessPressure = m.AccessPressure,
                    ReturnPressure = m.ReturnPressure,
                    TMP = m.TMP,
                    FilterPressureDrop = m.FilterPressureDrop,
                    FilterPressure = m.FilterPressure,
                    EffluentPressure = m.EffluentPressure,
                    FluidRemovalAccumulation = m.FluidRemovalAccumulation,
                    BloodFlowRate = m.BloodFlowRate,
                    PlasmaFlowrate = m.PlasmaFlowrate,
                    PBPFlowRate = m.PBPFlowRate,
                    PreReplacementFlowRate = m.PreReplacementFlowRate,
                    PostReplacementFlowRate = m.PostReplacementFlowRate,
                    DialysateFlowRate = m.DialysateFlowRate,
                    FluidRemoval = m.FluidRemoval,
                    InformPhysician = m.InformPhysician,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    NumberOfAssessment = m.NumberOfAssessment,
                    UserID = m.AddEmployeeID
                }).OrderBy(t => t.AssessDate).ThenBy(t => t.AssessTime).ThenBy(t => t.NumberOfAssessment).ToListAsync();
            return datas.GroupBy(m => m.PatientCRRTRecordID).ToList();
        }
        /// <summary>
        /// 获取主记录与其维护记录的对应关系
        /// </summary>
        /// <param name="recordIDs">主记录ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetStartCareMainIDDictByRecordIDs(List<string> recordIDs)
        {
            return await _medicalDbContext.PatientCRRTCareMainInfos
                .Where(m => recordIDs.Contains(m.PatientCRRTRecordID) && m.DeleteFlag != "*" && m.RecordsCode == "CRRTStart")
                .ToDictionaryAsync(m => m.PatientCRRTRecordID, n => n.PatientCRRTCareMainID);
        }
    }
}
