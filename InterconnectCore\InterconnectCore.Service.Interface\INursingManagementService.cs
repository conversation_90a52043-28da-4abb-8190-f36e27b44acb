﻿using InterconnectCore.ViewModels;

namespace InterconnectCore.Service.Interface
{
    public interface INursingManagementService
    {
        /// <summary>
        /// 同步排班数据
        /// </summary>
        /// <param name="nurseShiftList">排班数据</param>
        /// <returns></returns>
        Task<bool> SyncNursingManagementShiftData(List<NurseShiftView> nurseShiftList);
        /// <summary>
        /// 同步护理管理人员数据
        /// </summary>
        /// <param name="employeeList">人员信息</param>
        /// <returns></returns>
        Task<bool> SyncNursingManagementEmployeeData(List<EmployeeBasicDataView> employeeList);
    }
}
