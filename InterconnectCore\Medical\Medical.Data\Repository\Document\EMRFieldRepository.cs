﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EMRFieldRepository : IEMRFieldRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public EMRFieldRepository(
              MedicalDbContext medicalDb
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDb;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EMRFieldInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.EMRFieldInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EMRField.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据医院ID和主键集合获取数据
        /// </summary>
        /// <param name="fieldIDList"></param>
        /// <returns></returns>
        public async Task<List<FieldView>> GetAsyncByFieldIDs(int[] fieldIDList)
        {
            var returnData = (await GetCacheAsync()) as List<EMRFieldInfo>;
            return returnData.Where(m => fieldIDList.Contains(m.ID)).Select(m => new FieldView
            {
                ID = m.ID,
                ShowName = m.Content,
                Unit = m.Unit,
                Source = m.Source,
                SourceID = m.SourceID,
                TPRFlag = m.TPRFlag
            }).ToList();
        }

        public async Task<List<AssessListSelectView>> GetByFieldIDs(List<int> fieldIDList)
        {
            var returnData = (await GetCacheAsync()) as List<EMRFieldInfo>;
            return returnData.Where(m => fieldIDList.Contains(m.ID)).Select(m => new AssessListSelectView
            {
                Value = m.ID,
                Label = m.Description.Trim()
            }).ToList();
        }
    }
}
