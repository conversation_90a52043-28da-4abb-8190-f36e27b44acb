﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 同步护理管理信息
    /// </summary>
    [Produces("application/json")]
    [Route("api/NursingManagement")]
    [EnableCors("any")]
    public class NursingManagementController : ControllerBase
    {
        private readonly INursingManagementService _nursingManagementService;
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="nursingManagementService"></param>
        public NursingManagementController(
            INursingManagementService nursingManagementService
            )
        {
            _nursingManagementService = nursingManagementService;
        }
        /// <summary>
        /// 同步排班数据
        /// </summary>
        /// <param name="nurseShiftList"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncNursingManagementShiftData")]
        public async Task<ResponseResult> SyncNursingManagementShiftData([FromBody] List<NurseShiftView> nurseShiftList)
        {
            var result = new ResponseResult
            {
                Data = await _nursingManagementService.SyncNursingManagementShiftData(nurseShiftList)
            };
            return result;
        }

        /// <summary>
        /// 同步护理管理人员数据
        /// </summary>
        /// <param name="employeeList">人员信息</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncNursingManagementEmployeeData")]
        public async Task<ResponseResult> SyncNursingManagementEmployeeData([FromBody] List<EmployeeBasicDataView> employeeList)
        {
            var result = new ResponseResult
            {
                Data = await _nursingManagementService.SyncNursingManagementEmployeeData(employeeList)
            };
            return result;
        }
    }
}
