﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_VitalSignRecRepository : ICDA_VitalSignRecRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_VitalSignRecRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<CDA_VitalSignRecInfo> GetByID(string id)
        {
            return await _cDADBConnect.CDA_VitalSignRecInfos.Where(m => m.DCID == id).FirstOrDefaultAsync();
        }

        public async Task<DateTime?> GetLast()
        {
            return await _cDADBConnect.CDA_VitalSignRecInfos.OrderByDescending(m => m.TimeStamp).Select(t => t.TimeStamp).FirstOrDefaultAsync();

        }


        public async Task<bool> Save(CDA_VitalSignRecInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA生命体征写入数据失败:" + ex.InnerException + "\n异常数据:" + Common.ListToJson.ToJson(data));
                return false;
            }
        }

        public async Task<bool> Update(CDA_VitalSignRecInfo data)
        {
            var old = await _cDADBConnect.CDA_VitalSignRecInfos.Where(m => m.DCID == data.DCID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() >= 0;
            }
            catch (Exception)
            {

                _logger.Error("CDA生命体征更新数据失败,异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }

        /// <summary>
        /// 获取未推送的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<CDA_VitalSignRecInfo>> GetSyncData(DateTime? startDateTime, DateTime? endDateTime)
        {
            var now = DateTime.Now.Date;
            if (!startDateTime.HasValue)
            {
                startDateTime = now;
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = now.AddDays(1);
            }
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }
            return await _cDADBConnect.CDA_VitalSignRecInfos.Where(m => m.DataPumpFlag != "*"
            && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime).Take(1000).ToListAsync();
        }
    }
}
