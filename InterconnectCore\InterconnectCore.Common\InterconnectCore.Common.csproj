﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.1.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.10" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Medical\Medical.Data\Medical.Data.csproj" />
    <ProjectReference Include="..\Medical\Medical.ViewModels\Medical.ViewModels.csproj" />
  </ItemGroup>
</Project>