﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleMeasuresRepository : IPatientScheduleMeasuresRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientScheduleMeasuresRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<PatientScheduleMeasuresInfo>> GetAsync(DateTime startTime, DateTime endTime, string inpatientID, int? stationID)
        {
            var query = _dbContext.PatientScheduleMeasuresInfos.Where(t => t.PerformDate >= startTime.Date
                && t.PerformDate <= endTime.Date && t.PerformTime >= startTime.TimeOfDay
                && t.PerformTime < endTime.TimeOfDay && t.InpatientID == inpatientID && t.DeleteFlag != "*"
                //&& t.SourceID==null
                );
            if (stationID != null)
            {
                query = query.Where(t => t.StationID == stationID);
            }
            return await query.OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToListAsync();
        }

        public async Task<PatientScheduleMeasuresInfo> GetAsync(int id)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(t => t.ID == id).FirstOrDefaultAsync();
        }

        public async Task<PatientScheduleMeasuresInfo> GetBySource(string sourceID)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(t => t.SourceID == sourceID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientScheduleMeasuresInfo>> GetListBySource(string sourceID)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(t => t.SourceID == sourceID).ToListAsync();
        }

        public async Task<List<PatientScheduleMeasuresInfo>> GetByInpatientID(string inpatientID)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(t => t.InpatientID == inpatientID
                                 && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientScheduleMeasuresInfo>> GetAsync(string inpatientID, DateTime shiftDate, string shift, bool? bringToShift)
        {
            var result = await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.BringToShift == "1"
            && m.ShiftDate == shiftDate.Date && m.Shift == shift && m.DeleteFlag != "*").ToListAsync();

            return result;
        }

        public async Task<List<PatientScheduleMeasuresInfo>> GetByStartAndEndDate(DateTime startDate, DateTime endDate, string inpatientID, int stationID)
        {
            var query = _dbContext.PatientScheduleMeasuresInfos.Where(
                   m => m.InpatientID == inpatientID
                && m.StationID == stationID
                && m.PerformDate >= startDate
                && m.PerformDate <= endDate && m.DeleteFlag != "*" && m.SourceID == null);

            return await query.OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToListAsync();
        }
        /// <summary>
        /// 获取观察措施列表
        /// </summary>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="inpatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientMeasuresView>> GetPatientObservationList(DateTime startTime, DateTime endTime, string inpatientID)
        {
            var returnData = await (from a in _dbContext.PatientScheduleMeasuresInfos
                                    where a.PerformDate >= startTime.Date && a.PerformDate <= endTime.Date
                                    && a.InpatientID == inpatientID && a.DeleteFlag != "*" && a.SourceType == "S"
                                    select new PatientMeasuresView
                                    {
                                        ID = a.ID,
                                        Date = a.PerformDate,
                                        Time = a.PerformTime,
                                        Text = a.PerformText,
                                        PerformEmployeeID = a.PerformUserID,
                                        InformPhysician = a.InformPhysician ?? false,
                                        BringToShift = a.BringToShift == "1",
                                        ExpectEvalutionDate = a.ExpectEvalutionDate + a.ExpectEvalutionTime,
                                        EvalutionDate = a.EvalutionDate + a.EvalutionTime,
                                        ObserveTemplateID = a.ObserveTemplateID,
                                        OutCome = a.OutComeID,
                                        PerformDate = a.PerformDate + a.PerformTime,
                                        SourceID = a.SourceID,
                                        EvalutionEmployeeID = a.EvalutionEmployeeID
                                    }).ToListAsync();
            return returnData;
        }
        /// <summary>
        /// 根据排程ID获取对应措施
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        public async Task<PatientMeasuresView> GetPatientObservationByScheduleMainID(string scheduleMainID)
        {
            var schedule = await _dbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == scheduleMainID).FirstOrDefaultAsync();
            if (schedule == null)
            {
                return null;
            }
            var returnData = await (from a in _dbContext.PatientScheduleMeasuresInfos
                                    where a.SourceID == schedule.PatientInterventionID && a.DeleteFlag != "*"
                                               && a.SourceType == "S"
                                    select new PatientMeasuresView
                                    {
                                        ID = a.ID,
                                        Date = a.PerformDate,
                                        Time = a.PerformTime,
                                        Text = a.PerformText,
                                        PerformEmployeeID = a.PerformUserID,
                                        InformPhysician = a.InformPhysician ?? false,
                                        ExpectEvalutionDate = a.ExpectEvalutionDate + a.ExpectEvalutionTime,
                                        EvalutionDate = a.EvalutionDate + a.EvalutionTime,
                                        ObserveTemplateID = a.ObserveTemplateID,
                                        OutCome = a.OutComeID,
                                        PerformDate = a.PerformDate + a.PerformTime,
                                        SourceID = a.SourceID,
                                        EvalutionEmployeeID = a.EvalutionEmployeeID,
                                        BringToShift = a.BringToShift == "1" ? true : false
                                    }).FirstOrDefaultAsync();

            return returnData;
        }
        public async Task<List<PatientScheduleMeasuresInfo>> GetHandoverView(DateTime startDate, DateTime endDate, string inpatientID, int stationID)
        {
            var query = _dbContext.PatientScheduleMeasuresInfos.Where(
                   m => m.InpatientID == inpatientID
                && m.StationID == stationID
                && m.PerformDate >= startDate
                && m.PerformDate <= endDate && m.DeleteFlag != "*" && m.BringToShift == "1");

            return await query.OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToListAsync();
        }

        /// <summary>
        /// 获取班内最新的一次指定模板的病情观察
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="observeTemplateID">模板ID</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<PatientMeasuresView> GetShiftLastMeasuresViewByInpatientID(string inpatientID, string observeTemplateID, string shift, DateTime shiftDate)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.ObserveTemplateID == observeTemplateID
            && m.Shift == shift && m.ShiftDate == shiftDate && m.BringToShift == "1").OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime)
            .Select(m => new PatientMeasuresView
            {
                PerformDate = m.PerformDate,
                Time = m.PerformTime,
                SourceID = m.SourceID
            })
            .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取班内最新的一次指定模板的病情观察ID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="observeTemplateID">模板ID</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<string> GetShiftLastMeasuresSourceIDByInpatient(string inpatientID, string observeTemplateID, string shift, DateTime shiftDate)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.ObserveTemplateID == observeTemplateID
            && m.Shift == shift && m.ShiftDate == shiftDate && m.BringToShift == "1").OrderByDescending(m => m.PerformDate).ThenByDescending(m => m.PerformTime)
            .Select(m => m.SourceID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取一段时间内且带入交班的病情观察填写内容
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="stationID">病区序号</param>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<List<PatientMeasuresView>> GetPerformTextViewByDate(string inpatientID, int stationID, DateTime startDateTime, DateTime endDateTime)
        {
            var data = await _dbContext.PatientScheduleMeasuresInfos.Where(t => t.PerformDate >= startDateTime.Date
                && t.PerformDate <= endDateTime.Date && t.InpatientID == inpatientID && t.StationID == stationID && t.DeleteFlag != "*")
                .Select(m => new PatientMeasuresView
                {
                    PerformDate = m.PerformDate,
                    PerformTime = m.PerformTime,
                    EvalutionDate = m.EvalutionDate,
                    EvalutionTime = m.EvalutionTime,
                    StationID = m.StationID,
                    SourceID = m.SourceID,
                    Text = m.PerformText,
                    BringToShift = m.BringToShift == "1"
                }).ToListAsync();
            return data.Where(m => m.PerformDate.Add(m.PerformTime) >= startDateTime && m.PerformDate.Add(m.PerformTime) <= endDateTime).ToList();
        }
        /// <summary>
        /// 根据病人ID及病情观察模板ID获取班别内病情观察
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="observeTemplateID"></param>
        /// <param name="shift"></param>
        /// <param name="shiftDate"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMeasuresInfo>> GetByStationShiftAndTemplate(string inpatientID, string observeTemplateID, string shift, DateTime shiftDate)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.ObserveTemplateID == observeTemplateID
            && m.Shift == shift && m.ShiftDate == shiftDate && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据模板ID获取病人数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="observeTemplateID"></param>
        /// <returns></returns>
        public async Task<List<PatientScheduleMeasuresInfo>> GetByObserveTemplateID(string inpatientID, string observeTemplateID)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.ObserveTemplateID == observeTemplateID && !string.IsNullOrEmpty(m.PerformText) && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScheduleMeasuresInfo>> GetByperformDateAndTemplate(string inpatientID, string observeTemplateID, DateTime performDate)
        {
            var query = await _dbContext.PatientScheduleMeasuresInfos.Where(m => m.InpatientID == inpatientID && m.PerformDate == performDate && m.DeleteFlag == "*" && m.ModifyPersonID == null).ToListAsync();
            return query.Where(m => m.ObserveTemplateID == observeTemplateID).ToList();
        }
        /// <summary>
        /// 获取某时间点的病情观察数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="performTime"></param>
        /// <param name="performDate"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMeasuresInfo> GetByperformDateTimeAnd(string inpatientID, TimeSpan performTime, DateTime performDate)
        {
            return await _dbContext.PatientScheduleMeasuresInfos.FirstOrDefaultAsync(m => m.InpatientID == inpatientID && m.PerformDate == performDate && m.PerformTime == performTime && m.DeleteFlag == "*");
        }
    }
}