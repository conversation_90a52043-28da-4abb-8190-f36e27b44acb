﻿using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using NLog;

namespace InterconnectCore.Service
{
    public class SyncBloodInspectService: ISyncBloodInspectService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IPatientNursingRecordDetailRepository _recordDetailRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IEMRSourceRepository _emrSourceRepository;

        public SyncBloodInspectService(
            IPatientNursingRecordDetailRepository recordDetailRepository,
            IInpatientDataRepository inpatientDataRepository,
            IEMRSourceRepository emrSourceRepository)
        {
            _recordDetailRepository = recordDetailRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _emrSourceRepository = emrSourceRepository;
        }

        /// <summary>
        /// 同步输血监测数据到护理记录明细表
        /// </summary>
        /// <param name="inputViews"></param>
        /// <returns>(successFlag:成功标记，errorMessage=执行错误信息 )</returns>
        public Task<(bool successFlag, string errorMessage)> SyncBloodInspectDataToNursingRecordDetail(List<BloodInspectInputView> inputViews)
        {

            // 1.检查必要参数 CaseNumber、唯一主键 RecordID,键值对信息 KeyValueItems、血袋号 BloodNumber、执行人 PerformEmployeeID 、执行时间 PerformDateTime 不满足条件的记录日志，然后筛选出满足的数据
            // 2.根据参数集合,汇总CaseNumber 预查找所有用到的患者信息
            // 3.加载EMRSource缓存配置集合
            // 4.将1中获取到的数据按照CaseNumber分组，然后找到对应患者的InpatientDataInfo信息，构造NursingRecordDetailInfo实例对象,
            // 4.1.根据唯一ID和患者InpatientID,查找PatientNursingRecordDetail数据，如果存在修改，删除后新增，否则直接新增（存入一个临时集合，最后在循环结束后统一新增保存）
            throw new NotImplementedException();
        }
    }
}
