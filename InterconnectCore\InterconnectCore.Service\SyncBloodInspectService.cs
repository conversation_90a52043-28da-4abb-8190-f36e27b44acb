﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InterconnectCore.Service
{
    public class SyncBloodInspectService: ISyncBloodInspectService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IPatientNursingRecordDetailRepository _recordDetailRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IEMRSourceRepository _emrSourceRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;

        public SyncBloodInspectService(
            IPatientNursingRecordDetailRepository recordDetailRepository,
            IInpatientDataRepository inpatientDataRepository,
            IEMRSourceRepository emrSourceRepository,
            IUnitOfWork<MedicalDbContext> unitOfWork)
        {
            _recordDetailRepository = recordDetailRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _emrSourceRepository = emrSourceRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 同步输血监测数据到护理记录明细表
        /// </summary>
        /// <param name="inputViews"></param>
        /// <returns>(successFlag:成功标记，errorMessage=执行错误信息 )</returns>
        public async Task<(bool successFlag, string errorMessage)> SyncBloodInspectDataToNursingRecordDetail(List<BloodInspectInputView> inputViews)
        {
            try
            {
                if (inputViews == null || inputViews.Count == 0)
                {
                    return (false, "输入参数为空");
                }

                // 1.检查必要参数并筛选有效数据
                var validInputs = ValidateAndFilterInputs(inputViews);
                if (validInputs.Count == 0)
                {
                    return (false, "没有有效的输入数据");
                }

                // 2.根据参数集合,汇总CaseNumber 预查找所有用到的患者信息
                var caseNumbers = validInputs.Select(x => x.CaseNumber).Distinct().ToList();
                var patientDataDict = await GetPatientDataByCaseNumbers(caseNumbers);
                if (patientDataDict.Count == 0)
                {
                    return (false, "未找到对应的患者信息");
                }

                // 3.加载EMRSource缓存配置集合
                var emrSourceConfigs = await _emrSourceRepository.GetAsync();
                if (emrSourceConfigs == null || emrSourceConfigs.Count == 0)
                {
                    return (false, "未找到EMR配置信息");
                }

                // 4.处理数据并保存
                var result = await ProcessAndSaveData(validInputs, patientDataDict, emrSourceConfigs);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步输血监测数据失败: {ex.Message}");
                return (false, $"同步失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证并筛选有效的输入数据
        /// </summary>
        /// <param name="inputViews"></param>
        /// <returns></returns>
        private List<BloodInspectInputView> ValidateAndFilterInputs(List<BloodInspectInputView> inputViews)
        {
            var validInputs = new List<BloodInspectInputView>();

            foreach (var input in inputViews)
            {
                var validationErrors = new List<string>();

                if (string.IsNullOrWhiteSpace(input.CaseNumber))
                    validationErrors.Add("病案号为空");

                if (string.IsNullOrWhiteSpace(input.RecordID))
                    validationErrors.Add("记录ID为空");

                if (string.IsNullOrWhiteSpace(input.BloodNumber))
                    validationErrors.Add("血袋号为空");

                if (string.IsNullOrWhiteSpace(input.PerformEmployeeID))
                    validationErrors.Add("执行人ID为空");

                if (input.PerformDateTime == default(DateTime))
                    validationErrors.Add("执行时间无效");

                if (input.KeyValueItems == null || input.KeyValueItems.Count == 0)
                    validationErrors.Add("键值对信息为空");

                if (validationErrors.Count > 0)
                {
                    _logger.Warn($"输血监测数据验证失败 - 病案号: {input.CaseNumber}, 记录ID: {input.RecordID}, 错误: {string.Join(", ", validationErrors)}");
                    continue;
                }

                validInputs.Add(input);
            }

            _logger.Info($"输血监测数据验证完成，总数: {inputViews.Count}, 有效数据: {validInputs.Count}");
            return validInputs;
        }

        /// <summary>
        /// 根据病案号获取患者信息
        /// </summary>
        /// <param name="caseNumbers"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, InpatientDataInfo>> GetPatientDataByCaseNumbers(List<string> caseNumbers)
        {
            var patientDataDict = new Dictionary<string, InpatientDataInfo>();

            foreach (var caseNumber in caseNumbers)
            {
                try
                {
                    var patientData = await _inpatientDataRepository.GetByCaseNumberAsync(caseNumber);
                    if (patientData != null)
                    {
                        patientDataDict[caseNumber] = patientData;
                    }
                    else
                    {
                        _logger.Warn($"未找到病案号为 {caseNumber} 的患者信息");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"获取病案号 {caseNumber} 的患者信息失败: {ex.Message}");
                }
            }

            return patientDataDict;
        }

        /// <summary>
        /// 处理数据并保存到数据库
        /// </summary>
        /// <param name="validInputs"></param>
        /// <param name="inpatientDataDict"></param>
        /// <param name="emrSourceConfigs"></param>
        /// <returns></returns>
        private async Task<(bool successFlag, string errorMessage)> ProcessAndSaveData(
            List<BloodInspectInputView> validInputs,
            Dictionary<string, InpatientDataInfo> inpatientDataDict,
            List<EMRSourceInfo> emrSourceConfigs)
        {
            var recordsToAdd = new List<PatientNursingRecordDetailInfo>();
            var recordsToDelete = new List<PatientNursingRecordDetailInfo>();

            // 按病案号分组处理
            var groupedInputs = validInputs.GroupBy(x => x.CaseNumber);

            foreach (var group in groupedInputs)
            {
                var caseNumber = group.Key;
                if (!inpatientDataDict.TryGetValue(caseNumber, out var inpatientData))
                {
                    _logger.Warn($"跳过病案号 {caseNumber}，未找到对应患者信息");
                    continue;
                }

                foreach (var input in group)
                {
                    try
                    {
                        // 检查是否存在相同记录ID的数据
                        var existingRecords = await _recordDetailRepository.GetByParam(inpatientData.ID, new List<string> { input.RecordID });

                        // 如果存在，标记为删除
                        if (existingRecords != null && existingRecords.Count > 0)
                        {
                            foreach (var existingRecord in existingRecords)
                            {
                                existingRecord.Delete(input.PerformEmployeeID);
                                recordsToDelete.Add(existingRecord);
                            }
                        }

                        // 为每个键值对创建护理记录明细
                        foreach (var kvp in input.KeyValueItems)
                        {
                            var newRecord = CreateNursingRecordDetail(input, inpatientData, kvp.Key, kvp.Value);
                            if (newRecord != null)
                            {
                                recordsToAdd.Add(newRecord);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"处理记录ID {input.RecordID} 失败: {ex.Message}");
                    }
                }
            }

            // 保存数据
            return await SaveRecords(recordsToAdd, recordsToDelete);
        }

        /// <summary>
        /// 创建护理记录明细对象
        /// </summary>
        /// <param name="input"></param>
        /// <param name="patientData"></param>
        /// <param name="fieldKey"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        private PatientNursingRecordDetailInfo CreateNursingRecordDetail(
            BloodInspectInputView input,
            InpatientDataInfo patientData,
            string fieldKey,
            string fieldValue)
        {
            try
            {
                var record = new PatientNursingRecordDetailInfo
                {
                    InpatientID = patientData.ID,
                    PatientID = patientData.PatientID,
                    DepartmentListID = patientData.DepartmentListID,
                    StationID = patientData.StationID,
                    BedID = patientData.BedID,
                    BedNumber = patientData.BedNumber,
                    PerformDateTime = input.PerformDateTime,
                    PerformPersonID = input.PerformEmployeeID,
                    EmrFilesID = 0, // 需要根据fieldKey映射到具体的EMRSourceID
                    DataValue = fieldValue,
                    ConditionValue = input.BloodNumber, // 使用血袋号作为条件值
                    SourceID = input.RecordID,
                    DeleteFlag = "",
                    AddDateTime = DateTime.Now,
                    AddPersonID = input.PerformEmployeeID,
                    ModifyDateTime = DateTime.Now,
                    ModifyPersonID = input.PerformEmployeeID,
                    NursingRecordFlag = true,
                    ConditionType = "BloodInspect", // 标识为输血监测数据
                    SourceMainID = input.RecordID
                };

                return record;
            }
            catch (Exception ex)
            {
                _logger.Error($"创建护理记录明细失败 - 记录ID: {input.RecordID}, 字段: {fieldKey}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 保存记录到数据库
        /// </summary>
        /// <param name="recordsToAdd"></param>
        /// <param name="recordsToDelete"></param>
        /// <returns></returns>
        private async Task<(bool successFlag, string errorMessage)> SaveRecords(
            List<PatientNursingRecordDetailInfo> recordsToAdd,
            List<PatientNursingRecordDetailInfo> recordsToDelete)
        {
            try
            {
                var totalCount = recordsToAdd.Count + recordsToDelete.Count;

                if (totalCount == 0)
                {
                    return (true, "");
                }

                // 处理删除的记录（实际是标记删除）
                foreach (var record in recordsToDelete)
                {
                    _unitOfWork.GetRepository<PatientNursingRecordDetailInfo>().Update(record);
                }

                // 处理新增的记录
                foreach (var record in recordsToAdd)
                {
                    await _unitOfWork.GetRepository<PatientNursingRecordDetailInfo>().InsertAsync(record);
                }

                // 保存所有更改
                var saveResult = await _unitOfWork.SaveChangesAsync();

                if (saveResult > 0)
                {
                    _logger.Info($"输血监测数据同步成功 - 总处理记录数: {totalCount}, 删除标记: {recordsToDelete.Count}, 新增: {recordsToAdd.Count}");
                    return (true, "");
                }
                else
                {
                    _logger.Warn($"输血监测数据同步未产生变更 - 总处理记录数: {totalCount}");
                    return (true, "");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"保存输血监测数据失败: {ex.Message}");
                return (false, $"保存失败: {ex.Message}");
            }
        }
    }
}
