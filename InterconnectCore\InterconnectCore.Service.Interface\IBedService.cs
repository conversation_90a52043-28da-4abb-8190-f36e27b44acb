using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IBedService
    {
        /// <summary>
        /// 创建一张床位
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="departmentID"></param>
        /// <param name="bedNumber"></param>
        /// <returns></returns>
        Task<bool> CreateOneBed(int stationID, int departmentID, string bedNumber, short bedSort);
    }
}
