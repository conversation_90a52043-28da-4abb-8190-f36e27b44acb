﻿namespace InterconnectCore.ViewModels
{
    public class SyncVitalSignsView
    {
        /// <summary>
        /// 评估主键ID
        /// </summary>
        public string PatientAssessMainID { get; set; }
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        public string HospitalCode { get; set; }
        /// <summary>
        /// 医院名称
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 患者主键
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病人姓名 
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 病区
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 病区名称
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 入院次数
        /// </summary>
        public int NumberOfAdmissions { get; set; }
        /// <summary>
        /// 表单序号
        /// </summary>
        public int RecordListID { get; set; }
        /// <summary>
        /// 评估日期
        /// </summary>
        public DateTime AssessDate { get; set; }
        /// <summary>
        /// 评估时间
        /// </summary>
        public TimeSpan AssessTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 新增人工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 风险分数
        /// </summary>
        public int ScorePoint { get; set; }
        /// <summary>
        /// 风险距ID
        /// </summary>
        public int ScoreRangeID { get; set; }
        /// <summary>
        /// 风险描述
        /// </summary>
        public string ScoreRangeContent { get; set; }
        /// <summary>
        /// 新增人姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 体温
        /// </summary>
        public string Temperature { get; set; }
        /// <summary>
        /// 体温单位
        /// </summary>
        public string TemperatureUnit { get; set; }
        /// <summary>
        /// 脉搏
        /// </summary>
        public string Pulse { get; set; }
        /// <summary>
        /// 脉搏单位
        /// </summary>
        public string PulseUnit { get; set; }
        /// <summary>
        /// 心律
        /// </summary>
        public string HeartRate { get; set; }
        /// <summary>
        /// 心率单位
        /// </summary>
        public string HeartRateUnit { get; set; }
        /// <summary>
        /// 心律类型
        /// </summary>
        public string HeartRateType { get; set; }
        /// <summary>
        /// 起搏器心率（次/min）
        /// </summary>
        public string PacemakerHeartRate { get; set; }
        /// <summary>
        /// 疼痛分数
        /// </summary>
        public string PainScore { get; set; }
        /// <summary>
        /// 呼吸
        /// </summary>
        public string Breath { get; set; }
        /// <summary>
        /// 呼吸单位
        /// </summary>
        public string BreathUnit { get; set; }
        /// <summary>
        /// 舒张压
        /// </summary>
        public string Dbp { get; set; }
        /// <summary>
        /// 收缩压
        /// </summary>
        public string Sbp { get; set; }
        /// <summary>
        /// 血压单位
        /// </summary>
        public string PresessUnit { get; set; }
        /// <summary>
        /// 身高
        /// </summary>
        public string Height { get; set; }
        /// <summary>
        /// 身高单位
        /// </summary>
        public string HeightUnit { get; set; }
        /// <summary>
        /// 身高测量方式（卧床,正常）
        /// </summary>
        public string HeightType { get; set; }
        /// <summary>
        /// 体重
        /// </summary>
        public string Weight { get; set; }
        /// <summary>
        /// 体重单位
        /// </summary>
        public string WeightUnit { get; set; }
        /// <summary>
        /// 体重指数
        /// </summary>
        public string Bmi { get; set; }
        /// <summary>
        /// 血氧
        /// </summary>
        public string BloodOxygen { get; set; }
        /// <summary>
        /// 血氧单位
        /// </summary>
        public string BloodOxygenUnit { get; set; }
        /// <summary>
        /// 过敏史
        /// </summary>
        public string AllergyHistory { get; set; }
        /// <summary>
        /// 面色
        /// </summary>
        public string FaceColor { get; set; }
        /// <summary>
        /// 住院天数
        /// </summary>
        public string InDay { get; set; }
        /// <summary>
        /// 是否为儿童
        /// </summary>
        public bool IsChild { get; set; }
    }
}
