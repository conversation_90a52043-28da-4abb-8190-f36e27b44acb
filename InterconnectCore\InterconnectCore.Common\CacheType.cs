﻿using InterconnectCore.Common.SessionCommon;

namespace InterconnectCore.Common
{
    /// <summary>
    /// 缓存类型
    /// </summary>
    public enum CacheType
    {
    }

    public static class CacheTypeExtensions
    {
        public static string GetKey(this CacheType cacheType, SessionCommonServer _sessionCommonServer)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return cacheType.ToString() + "_" + session.HospitalID + "_" + session.Language.ToString();
        }

        /// <summary>
        /// 医院ID及语言使用参数  前端模板配置页面使用 其它页面不允许使用
        /// </summary>
        /// <param name="cacheType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static string GetKeyNotBySession(this CacheType cacheType, string hospitalID, int language)
        {
            return cacheType.ToString() + "_" + hospitalID + "_" + language.ToString();
        }
    }
}