﻿/**
 *  2022-04-27 2575 调整计算输液剩余量逻辑,扩展可以泵速的计算 -杨欣欣
 */
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatrolRecordRepository : IPatrolRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatrolRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="patrolRecordID"></param>
        /// <returns></returns>
        public async Task<PatientPatrolRecordInfo> GetDataByID(string patrolRecordID)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => m.PatientPatrolRecordID == patrolRecordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据来源ID 获取数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<PatientPatrolRecordInfo>> GetDataBySourceID(string sourceID)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据病人ID获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientPatrolRecordInfo>> GetDataByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PatientPatrolRecordInfo> GetCountByInpatientIDAndGroupID(string inpatientID, string GroupID)
        {
            var result = await _medicalDbContext.PatrolRecordInfos.Where(m => m.InpatientID == inpatientID && m.GroupID == GroupID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.PerformData).ThenByDescending(m => m.PerformTime).FirstOrDefaultAsync();
            return result;
        }
        /// <summary>
        /// 获取本组药品总个数
        /// </summary>
        /// <param name="inpatientID">病人住院号</param>
        /// <param name="GroupID">组号</param>
        /// <returns></returns>
        public async Task<int?> GetComboOrderCountByInpatientIDAndGroupID(string inpatientID, string GroupID)
        {
            var count = await _medicalDbContext.PatrolRecordInfos.Where(m => m.RecordsCode == "InfusionStart"
            && m.InpatientID == inpatientID && m.GroupID == GroupID && m.DeleteFlag != "*").Select(m => m.ComboOrderCount)
            .FirstOrDefaultAsync();
            return count;
        }
        public async Task<List<PatientPatrolRecordInfo>> GetAllUNInfusionEndData(DateTime dateTime)
        {
            return await (from m in _medicalDbContext.PatrolRecordInfos
                          where m.RecordsCode != "InfusionEnd" && m.ModifyDate >= dateTime
                           && !_medicalDbContext.PatrolRecordInfos.Any(n => n.RecordsCode == "InfusionEnd" && n.ModifyDate >= dateTime && m.GroupID == n.GroupID)
                          select m).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 跟据时间获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="shiftDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientPatrolRecordInfo>> GetDataByDateTime(string inpatientID, DateTime shiftDate, TimeSpan startTime, TimeSpan endTime)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => m.InpatientID == inpatientID && m.PerformData == shiftDate && m.PerformTime >= startTime && m.PerformTime <= endTime && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据GroupID获取最后一条或第一条记录
        /// </summary>
        /// <param name="groupID">医嘱组号</param>
        /// <param name="Type">类型：E最后一次；F第一次</param>
        /// <param name="patientPatrolRecordID">巡视记录ID</param>
        /// <returns></returns>
        public async Task<PatientPatrolRecordInfo> GetLastOrFirstDataByGroupID(string groupID, string Type = "E", string patientPatrolRecordID = null)
        {
            if (Type == "F")
            {
                return await _medicalDbContext.PatrolRecordInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*").OrderBy(m => m.PerformData).ThenBy(m => m.PerformTime).FirstOrDefaultAsync();
            }
            else
            {
                var lastTwoRecords = await _medicalDbContext.PatrolRecordInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*").OrderByDescending(m => m.PerformData).ThenByDescending(m => m.PerformTime).Take(2).ToListAsync();
                if (lastTwoRecords.Count == 0)
                {
                    return null;
                }
                var last = lastTwoRecords[0];
                if (lastTwoRecords.Count == 2)
                {
                    // 如果patientPatrolRecordID不为空，并且最后一次是否和patientPatrolRecordID相同，取patientPatrolRecordID的上一次记录                
                    if (!string.IsNullOrEmpty(patientPatrolRecordID) && last.PatientPatrolRecordID == patientPatrolRecordID)
                    {
                        last = lastTwoRecords[1];
                    }
                    //执行时间相同 取量剩余少的
                    if (lastTwoRecords[0].PerformData.Add(lastTwoRecords[0].PerformTime) == lastTwoRecords[1].PerformData.Add(lastTwoRecords[1].PerformTime))
                    {
                        last = lastTwoRecords.OrderBy(m => m.Surplus).FirstOrDefault();
                    }
                }
                return last;
            }
        }
        /// <summary>
        /// 获取最后一次写IO记录
        /// </summary>
        /// <param name="groupID"></param>
        /// <param name="patientPatrolRecordID"></param>
        /// <returns></returns>
        public async Task<PatientPatrolRecordInfo> GetLastSaveIODataByGroupID(string groupID, string patientPatrolRecordID = null)
        {
            var lastTwoRecords = await _medicalDbContext.PatrolRecordInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*" && m.SaveIntakeFlag.HasValue && m.SaveIntakeFlag.Value).OrderByDescending(m => m.PerformData).ThenByDescending(m => m.PerformTime).Take(2).ToListAsync();
            if (lastTwoRecords.Count == 0)
            {
                return null;
            }
            var last = lastTwoRecords[0];
            if (lastTwoRecords.Count == 2)
            {
                // 如果patientPatrolRecordID不为空，并且最后一次是否和patientPatrolRecordID相同，取patientPatrolRecordID的上一次记录                
                if (!string.IsNullOrEmpty(patientPatrolRecordID) && last.PatientPatrolRecordID == patientPatrolRecordID)
                {
                    last = lastTwoRecords[1];
                }
            }
            return last;
        }
        /// <summary>
        ///  获取最后一次写IO记录剩余量
        /// </summary>
        /// <param name="groupID"></param>
        /// <param name="patientPatrolRecordID"></param>
        /// <returns></returns>
        public async Task<decimal?> GetLastSurplusDataByGroupID(string groupID, string patientPatrolRecordID = null)
        {
            var lastTwoRecords = await _medicalDbContext.PatrolRecordInfos.Where(m => m.GroupID == groupID && m.DeleteFlag != "*" && m.SaveIntakeFlag.HasValue && m.SaveIntakeFlag.Value).OrderByDescending(m => m.PerformData).ThenByDescending(m => m.PerformTime).Take(2).ToListAsync();
            if (lastTwoRecords.Count == 0)
            {
                return null;
            }
            var last = lastTwoRecords[0];
            if (lastTwoRecords.Count == 2)
            {
                //取剩余量小的
                last = lastTwoRecords.OrderBy(m => m.Surplus).FirstOrDefault();
            }
            return last.Surplus;
        }
        public async Task<List<PatientPatrolRecordInfo>> GetSimpleDataByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").AsNoTracking()
                .Select(m => new PatientPatrolRecordInfo
                {
                    PerformData = m.PerformData,
                    PerformTime = m.PerformTime,
                    RecordsCode = m.RecordsCode,
                    PatrolName = m.PatrolName,
                    Speed = m.Speed,
                    PatrolContent = m.PatrolContent,
                    ModifyPersonID = m.ModifyPersonID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据来源ID 获取数据
        /// </summary>
        /// <param name="groupList">组号集合</param>
        /// <param name="recordCode">类型编码</param>
        /// <returns></returns>
        public async Task<List<PatientPatrolRecordInfo>> GetDataBySourceID(string[] groupList, string recordCode)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => groupList.Contains(m.GroupID) && m.RecordsCode == recordCode && m.DeleteFlag != "*")
                .Select(m => new PatientPatrolRecordInfo
                {
                    PerformData = m.PerformData,
                    PerformTime = m.PerformTime,
                    ModifyPersonID = m.ModifyPersonID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据GroupID集合获取数据
        /// </summary>
        /// <param name="groupIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientPatrolRecordInfo>> GetListByGroupIDList(List<string> groupIDs)
        {
            return await _medicalDbContext.PatrolRecordInfos.Where(m => groupIDs.Any(n => n == m.GroupID) && m.DeleteFlag != "*")
                 .Select(m => new PatientPatrolRecordInfo
                 {
                     RecordsCode = m.RecordsCode,
                     GroupID = m.GroupID,
                     PerformData = m.PerformData,
                     PerformTime = m.PerformTime,
                 }).ToListAsync();
        }
    }
}