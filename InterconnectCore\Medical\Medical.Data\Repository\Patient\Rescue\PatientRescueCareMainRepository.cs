﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientRescueCareMainRepository : IPatientRescueCareMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientRescueCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PatientRescueCareMainInfo> GetByID(string mainID)
        {
            return await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueCareMainID == mainID
            && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        public async Task<PatientRescueCareMainInfo> GetCareByRecordsCodeAsync(string patientRescueRecordID, string recordsCode)
        {
            var list = await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == patientRescueRecordID
           && t.RecordsCode == recordsCode
           && t.DeleteFlag != "*").ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public PatientRescueCareMainInfo GetFirstByRecordsCode(string patientRescueRecordID, string recordsCode)
        {
            var list = _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == patientRescueRecordID
           && t.RecordsCode == recordsCode
           && t.DeleteFlag != "*").OrderBy(t => t.NumberOfAssessment).ToList();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<PatientRescueCareMainInfo> GetLastByNumAsync(string patientRescueRecordID)
        {
            var list = await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == patientRescueRecordID.Trim()
            && t.DeleteFlag != "*").OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<PatientRescueCareMainInfo> GetLastByTimeAsync(string patientRescueRecordID)
        {
            var list = await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == patientRescueRecordID
            && t.DeleteFlag != "*").OrderByDescending(t => t.AssessDate).ThenByDescending(t => t.AssessTime).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        public async Task<List<PatientRescueCareMainInfo>> GetListByRecordIDAsync(string recordID)
        {
            return await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == recordID
            && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRescueCareMainInfo>> GetRescueMaintainByRecordIDAsync(string recordID, string rescueMaintain)
        {
            return await _medicalDbContext.PatientRescueCareMainInfos.Where(t => t.PatientRescueRecordID == recordID
            && t.DeleteFlag != "*" && t.RecordsCode == rescueMaintain).ToListAsync();
        }

        //透过评估序号取得内容
        public async Task<List<PatientRescueCareMainInfo>> GetByAssessMainID(string assessMainID)
        {
            return await _medicalDbContext.PatientRescueCareMainInfos.Where(m => m.PatientAssessMainID == assessMainID
           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRescueCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _medicalDbContext.PatientRescueCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientRescueCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientRescueCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        public async Task<List<SpecificHandoverView>> GetHandoverView(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var list = await (from m in _medicalDbContext.PatientRescueCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                  && m.AssessDate >= startDate.Date && m.AssessDate <= endDate.Date)
                              join n in _medicalDbContext.PatientRescueRecordInfos.Where(n => n.InpatientID == inpatientID && n.DeleteFlag != "*")
                              on m.PatientRescueRecordID equals n.PatientRescueRecordID
                              select new SpecificHandoverView
                              {
                                  RecordID = m.PatientRescueRecordID,
                                  CareMainID = m.PatientRescueCareMainID,
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  BringToShift = m.BringToShift ?? false,
                                  RecordsCode = m.RecordsCode,
                                  CareIntervention = m.CareIntervention,
                              }).ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate && m.AssessDate.Add(m.AssessTime) <= endDate).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}