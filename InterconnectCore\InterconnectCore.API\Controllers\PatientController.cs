﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Models;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 同步人员接口信息
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncInpatient")]
    [EnableCors("any")]
    public class PatientController : ControllerBase
    {
        private readonly IInpatientService _inpatientService;
        private readonly IPatientOperationService _patientOperationService;
        private readonly IPatientOrderService _patientOrderService;
        private readonly IPatientScoreService _patientScoreService; 
        private readonly IPatientTestService _patientTestService;
        private readonly IPatientMedicineSchedule _patientMedicineSchedule;
        private readonly IPatientAllergyService _patientAllergyService;
        private readonly IPatientDiagnosisService _patientDiagnosisService;


        /// <summary>
        /// 同步人员接口信息构造函数
        /// </summary>
        /// <param name="inpatientService"></param>
        /// <param name="patientOperationService"></param>
        /// <param name="patientOrderService"></param>
        /// <param name="patientScoreService"></param>
        /// <param name="patientTestService"></param>
        /// <param name="patientMedicineSchedule"></param>
        /// <param name="patientAllergyService"></param>
        /// <param name="patientDiagnosisService"></param>
        public PatientController(IInpatientService inpatientService
            , IPatientOperationService patientOperationService
            , IPatientOrderService patientOrderService
            , IPatientScoreService patientScoreService
            , IPatientTestService patientTestService
            , IPatientMedicineSchedule patientMedicineSchedule
            , IPatientAllergyService patientAllergyService
            , IPatientDiagnosisService patientDiagnosisService
            )
        {
            _inpatientService = inpatientService;
            _patientOperationService = patientOperationService;
            _patientOrderService = patientOrderService;
            _patientScoreService = patientScoreService;
            _patientTestService = patientTestService;
            _patientMedicineSchedule = patientMedicineSchedule;
            _patientAllergyService = patientAllergyService;
            _patientDiagnosisService = patientDiagnosisService;
        }

        /// <summary>
        /// 同步病人在院信息
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncInpatientData")]
        public async Task<ResponseResult> SyncInpatientData([FromBody] List<InPatientDataView> inpatientListView)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncInpatientData(inpatientListView)
            };
            return result;
        }
        /// <summary>
        /// 同步患者主诉
        /// </summary>
        /// <param name="stringKeyValue">患者主诉数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncInpatientChiefComplaint")]
        public async Task<ResponseResult> SyncInpatientChiefComplaint([FromBody] StringKeyValueView stringKeyValue)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncInpatientChiefComplaintAsync(stringKeyValue)
            };
            return result;
        }
        /// <summary>
        /// 同步患者手术记录
        /// </summary>
        /// <param name="operationViewList"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientOperation")]
        public async Task<ResponseResult> InsertPatientOperation([FromBody] OperationParamView operationViewList)
        {
            var result = new ResponseResult
            {
                Data = await _patientOperationService.SyncPatientOperation(operationViewList)
            };
            return result;
        }
        /// <summary>
        /// 同步患者医嘱数据
        /// </summary>
        /// <param name="commonOrders">患者医嘱数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncInpatientOrder")]
        public async Task<ResponseResult> SyncInpatientOrder([FromBody] List<CommonOrderView> commonOrders)
        {
            var result = new ResponseResult
            {
                Data = await _patientOrderService.SyncPatientOrderAsync(commonOrders)
            };
            return result;

        }
        /// <summary>
        /// 同步患者过敏史
        /// </summary>
        /// <param name="patientAllergicViewList">患者过敏史</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientAllergic")]
        public async Task<ResponseResult> SyncPatientAllergic([FromBody] List<PatientAllergicView> patientAllergicViewList)
        {
            var result = new ResponseResult
            {
                Data = await _patientAllergyService.SyncPatientAllergic(patientAllergicViewList)
            };
            return result;
        }
        /// <summary>
        /// 同步患者检验
        /// </summary>
        /// <param name="patientTestResultViewList">患者主诉数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientTestResults")]
        public async Task<ResponseResult> SyncPatientTestResults([FromBody] List<PatientTestResultView> patientTestResultViewList)
        {
            var result = new ResponseResult
            {
                Data = await _patientTestService.SyncPatientTestResults(patientTestResultViewList)
            };
            return result;
        }
        /// <summary>
        /// 同步患者出院信息
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncDischargeInpatientDataAsync")]
        public async Task<ResponseResult> SyncDischargeInpatientData([FromBody] List<InPatientDataView> inpatientListView)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncDischargeInpatientData(inpatientListView)
            };
            return result;
        }

        /// <summary>
        /// 同步病人诊断信息
        /// </summary>
        /// <param name="pateintDiagnosis"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientDiagnosisAsync")]
        public async Task<ResponseResult> SyncPatientDiagnosisAsync([FromBody] List<PatientDiagnosisItemView> pateintDiagnosis)
        {
            var result = new ResponseResult
            {
                Data = await _patientDiagnosisService.SyncPatientDiagnosisAsync(pateintDiagnosis)
            };
            return result;
        }

        /// <summary>
        /// 同步患者VTE数据
        /// </summary>
        /// <param name="patientVTEViews"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientVTE")]
        public async Task<ActionResult<bool>> SyncPatientVTE([FromBody] List<PatientVTEView> patientVTEViews)
        {
            return await _patientScoreService.SyncPatientVTE(patientVTEViews);
        }

        /// <summary>
        /// 同步单患者用药信息
        /// </summary>
        /// <param name="patientMedicineScheduleViews"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncSinglePatientMedicineSchedule")]        
        public async Task<ActionResult<bool>> SyncSinglePatientMedicineSchedule([FromBody] List<PatientMedicineScheduleView> patientMedicineScheduleViews)
        {
            return await _patientMedicineSchedule.SyncSinglePatientMedicineSchedule(patientMedicineScheduleViews);
        }

        /// <summary>
        /// 同步无费出院信息
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncNoFeeDischargeInpatientData")]
        public async Task<ResponseResult> SyncNoFeeDischargeInpatientData([FromBody] List<InPatientDataView> inpatientListView)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncNoFeeDischargeInpatientData(inpatientListView)
            };
            return result;
        }
        /// <summary>
        /// 同步患者转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncInpatientTransferDept")]
        public async Task<ResponseResult> SyncInpatientTransferDept([FromBody] InpatientTransferDataView inpatientTransferDataView)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncInpatientTransferDeptData(inpatientTransferDataView)
            };
            return result;
        }
        /// <summary>
        /// 同步患者取消转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncCancelInpatientTransferDept")]
        public async Task<ResponseResult> SyncCancelInpatientTransferDept([FromBody] InpatientTransferDataView inpatientTransferDataView)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncCancelInpatientTransferDeptData(inpatientTransferDataView)
            };
            return result;
        }

        /// <summary>
        /// 同步新生儿记录
        /// </summary>
        /// <param name="newBornRecordViews"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncNewBornData")]
        public async Task<ResponseResult> SyncNewBornData([FromBody] List<NewBornRecordView> newBornRecordViews)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncNewBornData(newBornRecordViews)
            };
            return result;
        }
        /// <summary>
        /// 同步患者仪器数据
        /// </summary>
        /// <param name="clinicDataViews">患者仪器数据</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientMachineData")]
        public async Task<ResponseResult> SyncPatientMachineData([FromBody] List<ViewModels.ClinicDataView> clinicDataViews)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.SyncPatientMachineDataAsync(clinicDataViews)
            };
            return result;
        }
    }
}