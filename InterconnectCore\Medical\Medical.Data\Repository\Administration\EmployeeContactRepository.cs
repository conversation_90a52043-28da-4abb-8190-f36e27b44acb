﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeContactRepository : IEmployeeContactRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public EmployeeContactRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<EmployeeContactInfo> GetByEmployeeBasicIDAndContactWayAsync(int employeeDataID, string contactWay)
        {
            return await _medicalDbContext.EmployeeContactInfos.Where(
                m => m.DeleteFlag != "*"
                && m.EmployeeDataID == employeeDataID
                && m.ContactWay == contactWay).SingleOrDefaultAsync();
        }

        public async Task<List<EmployeeContactInfo>> GetByEmployeeBasicIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.EmployeeContactInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeDataID == employeeDataID).ToListAsync();
        }

        public async Task<List<EmployeeContactInfo>> GetList()
        {
            return await _medicalDbContext.EmployeeContactInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<EmployeeContactInfo> GetList(int ID)
        {
            return await _medicalDbContext.EmployeeContactInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeContactID == ID).SingleOrDefaultAsync();
        }
    }
}