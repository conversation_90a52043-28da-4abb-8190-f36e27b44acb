﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{

    public class QualityControlMainRepository : IQualityControlMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public QualityControlMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        ///根据主ID获取质控主表
        /// </summary>
        /// <param name="qualityControlMainID"></param>
        /// <returns></returns>
        public async Task<QCCheckRecordInfo> GetByIDAsync(string qualityControlMainID)
        {
            return await _medicalDbContext.QualityControlMainInfos.Where(t => t.QCCheckRecordID == qualityControlMainID && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }
        /// <summary>
        /// 根
        /// </summary>
        /// <param name="qualityControlMainID"></param>
        /// <returns></returns>

        public async Task<List<QCCheckRecordInfo>> GetQualityControlListAsync()
        {

            var item = await _medicalDbContext.QualityControlMainInfos.Where(t => t.DeleteFlag != "*").ToListAsync();

            return item;
        }
        /// <summary>
        /// 根据病区和主题ID获取质控记录
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="qcCheckLever"></param>
        /// <param name="qCSubjactiveID"></param>
        /// <param name="formType"></param>
        /// <returns></returns>
        public async Task<List<QCCheckRecordInfo>> GetByQCSubjactiveIDAsync(int stationID, int? qcCheckLever, int? qCSubjactiveID, string formType)
        {
            //根据subjactiveID查询质控主表模板
            var QualityControlMainInfoList =
                 await _medicalDbContext.QualityControlMainInfos.Where(t =>
                            t.DeleteFlag != "*"
                           && t.StationID == stationID
                           && (!qcCheckLever.HasValue || t.QCCheckLevel == qcCheckLever.Value.ToString())
                           && (!qCSubjactiveID.HasValue || t.QCCheckSubjectID == qCSubjactiveID)
                           && (string.IsNullOrEmpty(formType) || t.FormType == formType)
                     ).OrderByDescending(t => t.QCCheckTime).OrderByDescending(t => t.QCCheckDate).ToListAsync();
            return QualityControlMainInfoList;
        }
        public async Task<List<QCCheckRecordInfo>> GetByQualityControlPerson(string qualityControlPerson, int? qCSubjactiveID, string formType)
        {

            var data = await _medicalDbContext.QualityControlMainInfos.Where(t => t.DeleteFlag != "*" && t.QCCheckPerson == qualityControlPerson).ToListAsync();
            if (qCSubjactiveID.HasValue)
            {
                data = data.Where(t => t.QCCheckSubjectID == qCSubjactiveID).ToList();
            }
            if (!string.IsNullOrEmpty(formType))
            {
                data = data.Where(t => t.FormType == formType).ToList();
            }
            return data;
        }
        /// <summary>
        /// 根据主键ID获取数据
        /// </summary>
        /// <param name="qualityControlMainID"></param>
        /// <returns></returns>
        public async Task<QCCheckRecordInfo> GetByQualityControlByID(string qualityControlMainID)
        {
            return await _medicalDbContext.QualityControlMainInfos.Where(t => t.DeleteFlag != "*" && t.QCCheckRecordID == qualityControlMainID).FirstOrDefaultAsync();
        }

    }
}
