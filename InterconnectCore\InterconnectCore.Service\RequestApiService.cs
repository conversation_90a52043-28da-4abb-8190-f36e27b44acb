﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Newtonsoft.Json;
using NLog;
using System.Reflection;

namespace InterconnectCore.Service
{
    public class RequestApiService : IRequestApiService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IAPISettingService _apiSettingService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly CommonHelper _commonHelper;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;


        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private readonly string MODIFYPERSIONID = "InterconnectCore";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="apiSettingService"></param>
        /// <param name="appConfigSettingRepository"></param>
        /// <param name="commonHelper"></param>
        public RequestApiService(IAPISettingService apiSettingService
            , IAppConfigSettingRepository appConfigSettingRepository
            , CommonHelper commonHelper
            , IUnitOfWork<MedicalDbContext> unitOfWork)
        {
            _apiSettingService = apiSettingService;
            _appConfigSettingRepository = appConfigSettingRepository;
            _commonHelper = commonHelper;
            _unitOfWork = unitOfWork;
        }


        /// <summary>
        /// 根据settingCode调用API并返回数据
        /// </summary>
        /// <param name="settingCode">ApiSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="token">可不传</param>
        /// <param name="seconds">访问限时</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <returns></returns>
        public async Task<object> RequestAPI(string settingCode, string param, string inpatientID = "", string caseNumber = "", string token = null, int seconds = 30, string contentType = "application/json")
        {
            if (string.IsNullOrEmpty(settingCode))
            {
                return null;
            }
            //获取API
            var apiStr = await _apiSettingService.GetAPIAddressByCode(settingCode);
            if (apiStr == null || string.IsNullOrEmpty(apiStr.ApiUrl))
            {
                _logger.Error("获取地址失败！code：" + settingCode);
                return null;
            }
            Dictionary<string, string> httpHeader = null;
            if (!string.IsNullOrEmpty(token))
            {
                httpHeader = new Dictionary<string, string>
                {
                    { "medical-token", token}
                };
            }
            var url = apiStr.ApiUrl.Trim();
            _logger.Info("APiUrl||" + url);
            string result;
            // 根据配置的访问方式访问API-----1 Post,2 Get
            if (apiStr.CallType == 1)
            {
                // Post方式请求数据
                result = await HttpHelper.HttpPostAsync(url, param, contentType, seconds, httpHeader);
            }
            else
            {
                // Get方式请求数据
                if (!string.IsNullOrEmpty(param))
                {
                    url += param;
                }
                //呼叫api
                result = HttpHelper.HttpGet(url, "application/json", httpHeader);
            }
            //写入日志
            if (string.IsNullOrEmpty(inpatientID) && string.IsNullOrEmpty(caseNumber))
            {
                var syncLog = SaveLog(url, param, inpatientID, caseNumber, _unitOfWork, true);
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            return result;
        }

        /// <summary>
        /// 根据settingCode调用API并返回数据(Post请求,单独参数数据数据)
        /// </summary>
        /// <param name="settingCode">ApiSetting表中SettingCode字段</param>
        /// <param name="param">单个的参数按照get请求参数拼接起来的字符串，如：param="a=1&b=2"</param>
        /// <param name="token">可不传</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <param name="seconds">超时时间，默认30s</param>
        /// <returns></returns>
        public async Task<object> RequestAPIPostAndParam(string settingCode, string param, string inpatientID = "", string caseNumber = "", string token = null, int seconds = 30, string contentType = "application/json")
        {
            if (string.IsNullOrEmpty(settingCode))
            {
                return null;
            }
            //获取API
            var apiStr = await _apiSettingService.GetAPIAddressByCode(settingCode);
            if (apiStr == null || string.IsNullOrEmpty(apiStr.ApiUrl))
            {
                _logger.Error("获取地址失败！code：" + settingCode);
                return null;
            }
            Dictionary<string, string> httpHeader = null;
            if (!string.IsNullOrEmpty(token))
            {
                httpHeader = new Dictionary<string, string>
                {
                    { "medical-token", token}
                };
            }
            var url = apiStr.ApiUrl.Trim();
            _logger.Info("APiUrl||" + url);
            string result;
            if (!string.IsNullOrEmpty(param))
            {
                url += param;
            }
            // 根据配置的访问方式访问API-----1 Post,2 Get
            if (apiStr.CallType == 1)
            {
                // Post方式请求数据
                result = await HttpHelper.HttpPostAsync(url, null, contentType, seconds, httpHeader);
            }
            else
            {
                // Get方式请求数据
                result = HttpHelper.HttpGet(url, "application/json", httpHeader);
            }
            //写入日志
            if (string.IsNullOrEmpty(inpatientID) && string.IsNullOrEmpty(caseNumber))
            {
                var syncLog = SaveLog(url, "", inpatientID, caseNumber, _unitOfWork, true);
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            return result;
        }

        /// <summary>
        /// 获取API执行结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="syncLog"></param>
        /// <param name="_unitOfWork"></param>
        public void GetAPIExecResult(string result, SynchronizeLogInfo syncLog, IUnitOfWork<MedicalDbContext> _unitOfWork)
        {
            try
            {
                ResponseResult response = JsonConvert.DeserializeObject<ResponseResult>(result);
                if (response != null && response.Code == 1 && response.Data != null && (bool)response.Data)
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.Modify(MODIFYPERSIONID);
                    _unitOfWork.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                _logger.Error("获取API执行结果失败！" + ex.Message);
            }
        }
        public SynchronizeLogInfo SaveLog(string apiURL, string arg, string inpatientID,
            string caseNumber, IUnitOfWork<MedicalDbContext> _unitOfWork, bool isCommit = true, bool isPost = true)
        {
            var syncLog = CreateLogInfo(apiURL, arg, inpatientID, caseNumber, isPost);
            _unitOfWork.GetRepository<SynchronizeLogInfo>().Insert(syncLog);
            if (isCommit)
            {
                _unitOfWork.SaveChanges();
            }
            return syncLog;
        }
        //AddLogInfo
        public SynchronizeLogInfo CreateLogInfo(string apiURL, string arg, string inpatientID,
            string caseNumber, bool isPost)
        {
            var syncLog = new SynchronizeLogInfo
            {
                SynchronizeDate = DateTime.Now,
                ApiUrl = apiURL,
                PostOrGet = isPost ? "POST" : "Get",
                InpatientID = inpatientID,
                CaseNumber = caseNumber,
                Arguments = arg,
                SuccessFlag = "",
                ModifyDate = DateTime.Now,
                ModifyPersonID = MODIFYPERSIONID,
                DeleteFlag = ""
            };
            syncLog.ID = syncLog.GetId();
            return syncLog;
        }


        /// <summary>
        /// 根据settingCode调用AppConfigSettingAPI并返回数据
        /// </summary>
        /// <param name="settingType">AppConfigSetting表中SettingType字段，配置类型</param>
        /// <param name="settingCode">AppConfigSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="httpType">请求类型，1：Post请求；2：Get请求</param>
        /// <param name="token">可不传</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <returns></returns>
        public async Task<ResponseResult> RequestAPIByAppconfigSetting(string settingType, string settingCode, string param, int httpType, string token = null, string contentType = "application/json", Dictionary<string, string> headers = null)
        {
            if (string.IsNullOrEmpty(settingType) || string.IsNullOrEmpty(settingCode))
            {
                _logger.Error($"获取AppConfigSetting接口地址,传入参数为空，SettingType：{settingType}，SettingCode：{settingCode}");
                return null;
            }
            var url = await _appConfigSettingRepository.GetConfigSettingValue(settingType, settingCode);
            //var url = "https://localhost:1056/api/8/Employee/GetAllEmployeeData";
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"获取AppConfigSetting接口地址失败，SettingType：{settingType}，SettingCode：{settingCode}");
                return null;
            }
            Dictionary<string, string> httpHeader = null;
            if (!string.IsNullOrEmpty(token))
            {
                httpHeader = new Dictionary<string, string> { { "medical-token", token } };
            }
            if (headers != null)
            {
                httpHeader = headers;
            }
            _logger.Info("APiUrl||" + url);
            string result = "";
            // 根据配置的访问方式访问API-----1 Post,2 Get
            if (httpType == 1)
            {
                url += param;
                // Post方式请求数据
                result = await HttpHelper.HttpPostAsync(url, null, contentType, 30, httpHeader);
            }
            else
            {
                // Get方式请求数据
                if (!string.IsNullOrEmpty(param))
                {
                    url += param;
                }
                result = HttpHelper.HttpGet(url, "application/json", httpHeader);
            }
            if (string.IsNullOrEmpty(result))
            {
                _logger.Error($"获取接口信息失败，接口返回数据为空，SettingType：{settingType}，SettingCode：{settingCode}，参数Param：{param}");
                return null;
            }
            var response = ListToJson.ToList<ResponseResult>(result);
            if (response == null || response.Data == null)
            {
                _logger.Error($"接口信息转换ResponseResult失败，SettingType：{settingType}，SettingCode：{settingCode}，参数Param：{param}");
                return null;
            }
            return response;
        }

        #region 请求调用Interconnect同步程序
        /// <summary>
        /// 请求同步程序
        /// </summary>
        /// <param name="settingCode">ApiSetting表中SettingCode字</param>
        /// <param name="dataView">请求参数</param>
        /// <param name="inpatientID">患者ID</param>
        /// <param name="caseNumber">住院流水号</param>
        /// <returns></returns>
        public async Task<bool> RequestInterconnect(string settingCode, object dataView, string inpatientID, string caseNumber)
        {
            if (dataView == null)
            {
                _logger.Error($"APISetting.SettingCode=[{settingCode}]发送请求时，传参为空，不执行调用");
                return false;
            }
            var apiUrlView = await _apiSettingService.GetAPIAddressByCode(settingCode);
            if (apiUrlView == null)
            {
                _logger.Error($"APISetting.SettingCode=[{settingCode}]缺少Url调用配置");
                return false;
            }
            if (string.IsNullOrEmpty(inpatientID) && string.IsNullOrEmpty(caseNumber))
            {
                _logger.Error($"APISetting.SettingCode=[{settingCode}]患者信息InpatientID，CaseNumber为空");
                return false;
            }
            (string responseStr, string exStr, SynchronizeLogInfo syncLog) = await StartHttpRequest(dataView, apiUrlView, inpatientID, caseNumber);
            if (!string.IsNullOrEmpty(exStr) || responseStr == null)
            {
                return false;
            }
            try
            {
                var respResult = ListToJson.ToList<ResponseResult>(responseStr);
                if (respResult != null && (respResult.Code == 200 || respResult.Code == 1) && respResult.Data is bool success && success)
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.Modify(MODIFYPERSIONID);
                    _unitOfWork.SaveChanges();
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"解析返回参数失败，参会参数={responseStr}，异常ex={ex}");
                return false;
            }
            _logger.Info("回传请求返回参数：" + responseStr);
            return false;
        }
        /// <summary>
        /// 开始发送请求
        /// </summary>
        /// <param name="dataView">传递的参数</param>
        /// <param name="apiUrlView">请求相关信息</param>
        /// <param name="inpatientID">ccc住院号</param>
        /// <param name="caseNumber">住院流水号</param>
        /// <returns></returns>
        private async Task<(string, string, SynchronizeLogInfo)> StartHttpRequest(object dataView, ApiUrlView apiUrlView, string inpatientID, string caseNumber)
        {
            string responseStr = null;
            string exStr = null;
            SynchronizeLogInfo syncLog = null;
            if (apiUrlView.CallType == 1)
            {
                var postData = ListToJson.ToJson(dataView);
                syncLog = SaveLog(apiUrlView.ApiUrl, postData, inpatientID, caseNumber, _unitOfWork, true);
                try
                {
                    responseStr = await HttpHelper.HttpPostAsync(apiUrlView.ApiUrl, postData);
                }
                catch (Exception ex)
                {
                    exStr = ex.ToString();
                    _logger.Error($"Post请求失败，Url={apiUrlView.ApiUrl}，Params={postData}");
                }
            }
            else if (apiUrlView.CallType == 2)
            {
                string url = SpliceUrl(dataView, apiUrlView);
                if (url == null)
                {
                    return (null, "Get请求解析拼接获取Url失败！", null);
                }
                syncLog = SaveLog(url, null, inpatientID, caseNumber, _unitOfWork, true);
                try
                {
                    responseStr = await HttpHelper.HttpGetAsync(url);
                }
                catch (Exception ex)
                {
                    exStr = ex.ToString();
                    _logger.Error($"Get请求失败，Url={apiUrlView.ApiUrl}");
                }
            }
            else
            {
                throw new InvalidOperationException($"未知的调用类型：{apiUrlView.CallType}");
            }
            return (responseStr, exStr, syncLog);
        }

        /// <summary>
        /// 拼接参数
        /// </summary>
        /// <param name="dataView">请求参数</param>
        /// <param name="apiUrlView">请求view</param>
        /// <returns></returns>
        private static string SpliceUrl(object dataView, ApiUrlView apiUrlView)
        {
            // GET请求，拼接查询参数
            var url = apiUrlView.ApiUrl;
            var queryString = ToQueryString(dataView);
            if (!string.IsNullOrEmpty(queryString))
            {
                // 判断URL是否已经含有参数
                if (!url.Contains("?"))
                {
                    url += "?" + queryString;
                }
                else
                {
                    url += "&" + queryString;
                }
            }

            return url;
        }
        /// <summary>
        /// 将对象的值类型和字符串属性拼接成GET请求参数字符串
        /// </summary>
        /// <param name="obj">要转换的对象</param>
        /// <returns>URL编码的查询字符串</returns>
        public static string ToQueryString(object dataView)
        {
            if (dataView == null)
            {
                return null;
            }
            var parameters = new List<string>();
            var type = dataView.GetType();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var prop in properties)
            {
                if (prop.CanRead)
                {
                    var value = prop.GetValue(dataView, null);
                    if (IsValueTypeOrString(value))
                    {
                        var key = Uri.EscapeDataString(prop.Name);
                        var val = value != null ? Uri.EscapeDataString(value.ToString()) : "";
                        parameters.Add($"{key}={val}");
                    }
                }
            }
            return string.Join("&", parameters);
        }

        /// <summary>
        /// 判断对象是否是值类型或字符串
        /// </summary>
        private static bool IsValueTypeOrString(object obj)
        {
            if (obj == null)
            { 
                return false;
            }
            var type = obj.GetType();
            // 判断是否为值类型或字符串
            return type.IsValueType || type == typeof(string);
        }
        #endregion
    }
}
