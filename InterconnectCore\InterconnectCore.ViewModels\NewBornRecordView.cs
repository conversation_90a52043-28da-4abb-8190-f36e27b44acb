﻿namespace InterconnectCore.ViewModels
{
    /// <summary>
    /// 清洗后排班数据View
    /// </summary>
    public class NewBornRecordView
    {
        /// <summary>
        /// 母亲姓名
        /// </summary>
        public string ParentName { get; set; }
        /// <summary>
        /// 婴儿姓名
        /// </summary>
        public string BabyName { get; set; }
        /// <summary>
        /// 婴儿性别代码
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 婴儿性别名称
        /// </summary>
        public string GenderName { get; set; }
        /// <summary>
        /// 婴儿出生日期
        /// </summary>
        public DateTime? Birthday { get; set; }
        /// <summary>
        /// 母亲住院流水号
        /// </summary>
        public string ParentCaseNumber { get; set; }
        /// <summary>
        /// 母亲住院号
        /// </summary>
        public string ParentChartNo { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 胎序
        /// </summary>
        public int OrderSN { get; set; }
        /// <summary>
        /// 身高
        /// </summary>
        public string Height { get; set; }
        /// <summary>
        /// 体重
        /// </summary>
        public string Weight { get; set; }
        /// <summary>
        ///死亡时间
        ///</summary>
        public DateTime? DeathTime { get; set; }
        /// <summary>
        ///记录人ID
        ///</summary>
        public string RecordPersonID { get; set; }
    }
}
