﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDAVaginalDeliveryRecRepository : ICDAVaginalDeliveryRecRepository
    {
        private readonly CDADBContext _cDADBContext;

        public CDAVaginalDeliveryRecRepository(CDADBContext cDADBContext)
        {
            _cDADBContext = cDADBContext;
        }

        public void AddRecord(CDA_VaginalDeliveryRecInfo record)
        {
            _cDADBContext.CDA_VaginalDeliveryRecInfos.Add(record);
        }

        /// <summary>
        /// 取得最后一笔数据异动时间
        /// </summary>
        /// <returns>DateTime</returns>
        public async Task<DateTime> GetLastTimeSpanAsync()
        {
            return await _cDADBContext.CDA_VaginalDeliveryRecInfos.Select(m => m.TimeStamp).OrderByDescending(m => m).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键获取
        /// </summary>
        /// <param name="dCID">主键</param>
        /// <returns>CDR_VaginalDeliveryRecInfo</returns>
        public async Task<CDA_VaginalDeliveryRecInfo> GetRecordByDCIDAsync(string dCID)
        {
            return await _cDADBContext.CDA_VaginalDeliveryRecInfos.Where(m => m.DCID == dCID).FirstOrDefaultAsync();

        }

        public int SaveAllChange()
        {
            return _cDADBContext.SaveChanges();
        }
    }
}
