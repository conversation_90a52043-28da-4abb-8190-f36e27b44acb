﻿using Medical.Models;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class CDADBContext
    {
        public DbSet<CDR_AdmissionAssessmentInfo> CDR_AdmissionAssessmentInfos { get; set; }

        public DbSet<CDA_DischargeEvaluationInfo> CDR_DischargeEvaluationInfos { get; set; }

        public DbSet<CDA_GeneralNursingRecInfo> CDA_GeneralNursingRecInfos { get; set; }
        public DbSet<CDA_GeneralNursingRec_ItemInfo> CDA_GeneralNursingRec_ItemInfos { get; set; }
        public DbSet<CDA_CriticalNursingRecord_DocInfo> CDA_CriticalNursingRecord_DocInfos { get; set; }
        public DbSet<CDA_VitalSignRecInfo> CDA_VitalSignRecInfos { get; set; }
        public DbSet<CDA_VitalSignRec_ItemInfo> CDA_VitalSignRec_ItemInfos { get; set; }

        public DbSet<CDA_DiscrepancyRecordInfo> CDA_DiscrepancyRecordInfos { get; set; }

        public DbSet<CDA_DiscrepancyRecord_ItemInfo> CDA_DiscrepancyRecord_ItemInfos { get; set; }

        public DbSet<CDA_NursingPlansInfo> CDA_NursingPlansInfos { get; set; }
        public DbSet<CDA_NursingPlans_ItemInfo> CDA_NursingPlans_ItemInfos { get; set; }
        /// <summary>
        /// 皮肤压伤记录
        /// </summary>
        public DbSet<CDA_SkinUlcerRecInfo> CDA_SkinUlcerRecInfos { get; set; }
        /// <summary>
        /// 皮肤压伤记录—压疮部位
        /// </summary>
        public DbSet<CDA_SkinUlcerRec_ItemInfo> CDA_SkinUlcerRec_ItemInfos { get; set; }
        /// <summary>
        /// 阴道分娩记录CDR
        /// </summary>
        public DbSet<CDA_VaginalDeliveryRecInfo> CDA_VaginalDeliveryRecInfos { get; set; }
        /// <summary>
        /// 待产记录CDA
        /// </summary>
        public DbSet<CDA_PreDeliveryRecordInfo> CDA_PreDeliveryRecordInfos { get; set; }

    }
}