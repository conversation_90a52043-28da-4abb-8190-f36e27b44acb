﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientArteriovenousFistulaCareMainRepository : IPatientArteriovenousFistulaCareMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientArteriovenousFistulaCareMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        /// <summary>
        /// 根据MainID获取主表记录
        /// </summary>
        /// <param name="mainID">主表记录</param>
        /// <returns></returns>
        public async Task<PatientArteriovenousFistulaCareMainInfo> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.PatientArteriovenousFistulaCareMainID == mainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据RecordID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientArteriovenousFistulaCareMainInfo>> GetInfosByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.PatientArteriovenousFistulaRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取上一次的维护记录ByRecordID
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<ArteriovenousFistulaLastCareView> GetLastCareMainByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos
                .Where(m => m.PatientArteriovenousFistulaRecordID == recordID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .ThenByDescending(m => m.NumberOfAssessment)
                .Select(m => new ArteriovenousFistulaLastCareView
                {
                    PatientArteriovenousFistulaCareMainID = m.PatientArteriovenousFistulaCareMainID,
                    StationID = m.StationID,
                    NumberOfAssessment = m.NumberOfAssessment
                })
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据RecordID获取开始评估记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientArteriovenousFistulaCareMainInfo> GetStartCareMainByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(t => t.PatientArteriovenousFistulaRecordID == recordID && t.DeleteFlag != "*" && t.RecordsCode.Contains("Start")).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主记录ID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<ArteriovenousFistulaCareMainView>> GetCareMainViewsByRecordID(string recordID)
        {
            //获取科室
            var departmentList = await _medicalDbContext.departmentListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            var result = await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.PatientArteriovenousFistulaRecordID == recordID && m.DeleteFlag != "*")
                .OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime)
                .Select(m => new ArteriovenousFistulaCareMainView
                {
                    PatientArteriovenousFistulaCareMainID = m.PatientArteriovenousFistulaCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    RecordsCode = m.RecordsCode,
                    PatientScheduleMainID = m.PatientScheduleMainID,
                    ExudateVolume = m.ExudateVolume,
                    ExudateColor = m.ExudateColor,
                    Incision = m.Incision,
                    SurroundSkin = m.SurroundSkin,
                    Tremor = m.Tremor,
                    Noise = m.Noise,
                    Intervention = m.Intervention,
                    InformPhysician = m.InformPhysician,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    NumberOfAssessment = m.NumberOfAssessment,
                    UserID = m.AddEmployeeID
                }).ToListAsync();
            foreach (var item in result)
            {
                var departName = departmentList.Where(m => m.ID == item.DepartmentListID).FirstOrDefault();
                if (departName != null)
                {
                    item.DepartmentName = departName.Department ?? "";
                }
            }
            return result;
        }

        /// <summary>
        /// 获取当前病人所有主记录的选框勾选状态
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientBringView>> GetRecordsBringViewsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.RecordsCode.Contains("Start")
            )
                .Select(m => new PatientBringView
                {
                    RecordID = m.PatientArteriovenousFistulaRecordID,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InformPhysician = m.InformPhysician
                }).ToListAsync();
        }

        /// <summary>
        /// 获取构造评估模板时，所需要的历史评估主表数据
        /// </summary>
        /// <param name="recordID">主记录</param>
        /// <param name="recordModifyFlag">主表修改标记</param>
        /// <returns>
        /// Item1: 病区ID
        /// Item2：主表ID
        /// </returns>
        public async Task<Tuple<int, string>> GetAssessHistoryDataByRecordID(string recordID, bool recordModifyFlag)
        {
            if (recordModifyFlag)
            {
                // 主记录修改，取出指定的开始评估数据
                return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.PatientArteriovenousFistulaRecordID == recordID && m.DeleteFlag != "*" && m.RecordsCode.Contains("Start"))
                    .Select(m => new Tuple<int, string>(m.StationID, m.PatientArteriovenousFistulaCareMainID)).FirstOrDefaultAsync();
            }
            else
            {
                // 主表新增，取本次记录最近一次例行评估的历史数据
                var historyCareMainInfo = await GetLastCareMainByRecordID(recordID);
                if (historyCareMainInfo == null)
                {
                    return new Tuple<int, string>(0, null);
                }
                return new Tuple<int, string>(historyCareMainInfo.StationID, historyCareMainInfo.PatientArteriovenousFistulaCareMainID);
            }
        }
        /// <summary>
        /// 获取班内的评估照护数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<List<string>> GetCareInterventionsByShift(string inpatientID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" &&
            m.Shift == shift && m.ShiftDate == shiftDate && m.BringToShift == true).Select(m => m.CareIntervention).ToListAsync();
        }
        /// <summary>
        /// 根据时间范围获取病人的动静脉内瘘维护记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns>List<PatientArteriovenousFistulaCareMainInfo></returns>
        public async Task<List<PatientArteriovenousFistulaCareMainInfo>> GetCareMainsByInpatientIDAsNoTrackAsync(
            string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await _medicalDbContext.PatientArteriovenousFistulaCareMainInfos
                .AsNoTracking().Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" &&
                m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date).
                Select(m => new PatientArteriovenousFistulaCareMainInfo
                {
                    PatientArteriovenousFistulaRecordID = m.PatientArteriovenousFistulaRecordID,
                    PatientArteriovenousFistulaCareMainID = m.PatientArteriovenousFistulaCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    RecordsCode = m.RecordsCode,
                    BringToShift = m.BringToShift
                })
                .ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) < endDateTime).ToList();
        }

        public async Task<List<SpecificHandoverView>> GetHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await (from m in _medicalDbContext.PatientArteriovenousFistulaCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                              && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date)
                              join n in _medicalDbContext.PatientArteriovenousFistulaRecordInfos.Where(n => n.InpatientID == inpatientID && n.DeleteFlag != "*")
                              on m.PatientArteriovenousFistulaRecordID equals n.PatientArteriovenousFistulaRecordID
                              select new SpecificHandoverView
                              {
                                  RecordID = m.PatientArteriovenousFistulaRecordID,
                                  CareMainID = m.PatientArteriovenousFistulaCareMainID,
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  BringToShift = m.BringToShift ?? false,
                                  RecordsCode = m.RecordsCode,
                                  BodyPartName = n.BodyPartName,
                              }).ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}