﻿namespace InterconnectCore.ViewModels
{
    public class MailBaseViewInfo
    {
        /// <summary>
        /// 发送方昵称 举例：xml
        /// </summary>
        public string SentFromUser { get; set; }
        /// <summary>
        /// 发送方邮件地址 举例："<EMAIL>"
        /// </summary>
        public string SentFromAddress { get; set; }
        /// <summary>
        /// SMTP连接用户85759437，有时候要求和SentFromAddress一样
        /// </summary>
        public string SmtpUserName { get; set; }
        /// <summary>
        /// 发送方SMTP授权码(SMTP授权码需要在邮箱获取)， 举例："oaqqgatslinvbjad"
        /// </summary>
        public string SmtpAuthorizationCode { get; set; }
        /// <summary>
        /// 发送方邮件服务器地址，举例："smtp.qq.com"
        /// </summary>
        public string MailHost { get; set; }
        /// <summary>
        /// 发送方邮件服务器端口 ,举例：587
        /// </summary>
        public Int32 MailHostPost { get; set; }
    }
}