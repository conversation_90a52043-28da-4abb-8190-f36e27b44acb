﻿using Medical.Common;
using Medical.Data.Interface;
using Medical.ViewModels.View;
using Newtonsoft.Json;
using NLog;

namespace MedicalExternalCommon.Service
{
    public class MQCommonService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        public MQCommonService(IAppConfigSettingRepository appConfigSettingRepository)
        {
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        /// <summary>
        /// 发送MQ消息
        /// </summary>
        /// <param name="messages">发送内容</param>
        /// <returns></returns>
        public async Task SendingMessage(List<MessageModel> messages)
        {
            if (messages == null || messages.Count <= 0)
            {
                return;
            }
            var _sendMQMessageAPIUrl = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "SendMQMessageAPI");

            if (string.IsNullOrEmpty(_sendMQMessageAPIUrl))
            {
                _logger.Warn("获取SendMQMessageAPI失败,发送地址为空");
                return;
            }

            var result = "";
            try
            {
                result = await HttpHelper.HttpPostAsync(_sendMQMessageAPIUrl, JsonConvert.SerializeObject(messages), "application/json");
            }
            catch (System.Exception ex)
            {
                _logger.Error("MQ消息推送失败" + ex.ToString());
                return;
            }

            if (!string.IsNullOrEmpty(result))
            {
                _logger.Info("MQ消息推送结果:" + result);
                var ret = JsonConvert.DeserializeObject<ResponseResult>(result);

                if (ret == null || ret.Code != 1)
                {
                    _logger.Error("MQ消息推送失败");
                }
            }
        }
    }
}

