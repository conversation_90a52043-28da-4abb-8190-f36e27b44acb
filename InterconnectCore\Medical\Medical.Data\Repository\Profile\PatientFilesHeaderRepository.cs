﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientFilesHeaderRepository : IPatientFilesHeaderRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public PatientFilesHeaderRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<PatientFilesHeaderInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.PatientFilesHeader.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.PatientFilesHeader.GetKey(_sessionCommonServer);
        }

        public async Task<List<PatientFilesHeaderInfo>> GetAsync(int? fileClass = null)
        {
            var data = await GetCacheAsync() as List<PatientFilesHeaderInfo>;
            var resultData = data.Where(m => m.DeleteFlag != "*" && m.FileClass == (fileClass ?? 999)).OrderBy(m => m.Order).ToList();
            if (resultData.Count <= 0)
            {
                resultData = data.Where(m => m.DeleteFlag != "*" && m.FileClass == 999).OrderBy(m => m.Order).ToList();
            }
            return resultData;
        }
    }
}
