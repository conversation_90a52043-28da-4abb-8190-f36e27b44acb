﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services.Interface;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System.Text;

namespace InterconnectCore.Services
{
    public class CommonHelper
    {
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IAppConfigSettingService _appConfigSettingService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISyncLogService _syncLogService;

        /// <summary>
        /// 配置类别
        /// </summary>
        private readonly string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        /// <summary>
        /// 写入Profile的MedicalAPI地址
        /// </summary>
        private readonly string PATIENTPROFILEAPI = "";

        /// <summary>
        /// 医疗院代码
        /// </summary>
        private readonly string HOSPITALID = "";

        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private readonly string MODIFYPERSIONID = "";

        public CommonHelper(IOptions<ViewModels.SystemConfig> config
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IAppConfigSettingService appConfigSettingService
            , IAppConfigSettingRepository appConfigSettingRepository
            , ISyncLogService syncLogService
            )
        {
            _config = config;
            _unitOfWork = unitOfWork;
            _appConfigSettingService = appConfigSettingService;
            _appConfigSettingRepository = appConfigSettingRepository;
            _syncLogService = syncLogService;
            HOSPITALID = _config.Value.HospitalID;
            PATIENTPROFILEAPI = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PatientProfileAPI").Result;
            //PATIENTPROFILEAPI = "http://localhost:56194/api/PatientProfile/Add";
            MODIFYPERSIONID = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
        }

        /// <summary>
        /// 获取变更内容
        /// </summary>
        /// <param name="builder">变更内容</param>
        /// <param name="key">字段</param>
        /// <param name="oldValue">原有值</param>
        /// <param name="newValue">变更值</param>
        public void GetChange(StringBuilder builder, string key, string oldValue, string newValue)
        {
            if (builder.ToString().Length > 0)
            {
                builder.Append("; ");
            }
            builder.Append(key);
            builder.Append(":");
            builder.Append(oldValue);
            builder.Append("-->");
            builder.Append(newValue);
        }
        /// <summary>
        /// 呼叫Profile
        /// </summary>
        /// <param name="patientProfileList"></param>
        public async Task AddProfile(List<PatientProfile> patientProfileList)
        {
            var guid = Guid.NewGuid().ToString("N");
            var hospitalID = _config.Value.HospitalID;
            if (patientProfileList == null || patientProfileList.Count <= 0)
            {
                return;
            }
            WebRequestSugar wrs = new WebRequestSugar();
            List<string> inps = patientProfileList.Where(m=>m.InpatientID!=null).Select(m => m.InpatientID).Distinct().ToList();
            if (string.IsNullOrEmpty(PATIENTPROFILEAPI))
            {
                _logger.Error("获取PatientProfileAPI为空");
                return;
            }
            //按病人分组提交，在profile中会有按病人过滤
            _logger.Info("循环呼叫API:" + PATIENTPROFILEAPI + "||" + inps.Count.ToString() + "次");
            foreach (var item in inps)
            {
                var tmpPatientProfiles = patientProfileList.Where(m => m.InpatientID == item).ToList();
                if (tmpPatientProfiles.Count == 0)
                {
                    continue;
                }
                var arg = JsonConvert.SerializeObject(tmpPatientProfiles);
                var syncLog = SaveLog(PATIENTPROFILEAPI, arg, tmpPatientProfiles[0].InpatientID
                    , tmpPatientProfiles[0].CaseNumber, _unitOfWork, true, true);
                var result = "";
                try
                {
                    result = await wrs.SendObjectAsJsonInBody(PATIENTPROFILEAPI, tmpPatientProfiles);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫：AddProfile失败，结束循环：" + ex.ToString());
                    return;
                }
                try
                {
                    GetAPIExecResult(result, syncLog, _unitOfWork);
                }
                catch (Exception ex)
                {
                    _logger.Error("呼叫：AddProfile失败!" + ex.ToString());
                    return;
                }
            }
        }
 

        /// <summary>
        /// 缓存更新
        /// </summary>
        /// <param name="query"></param>
        public async Task<string> UpdateCache(string api, string key)
        {
            WebRequestSugar wrs = new WebRequestSugar();
            return await wrs.SendObjectAsJsonInBody(api, key);
        }

        #region --数据分割

        public List<List<T>> Split<T>(List<T> list, int partNum)
        {
            var partCount = list.Count / partNum;
            if (list.Count % partNum > 0)
            {
                partCount++;
            }
            List<List<T>> result = new List<List<T>>();
            int j = partNum;
            for (int i = 0; i < list.Count; i += partNum)
            {
                var tmp = list.Take(j).Skip(i).ToList();
                j += partNum;
                result.Add(tmp);
            }
            return result;
        }

        public IEnumerable<IEnumerable<T>> Split<T>(IEnumerable<T> list, int parts)
        {
            int i = 0;
            var splits = from item in list
                         group item by i++ % parts into part
                         select part.AsEnumerable();
            return splits;
        }

        #endregion --数据分割

        #region --Sync日志

        //AddLogInfo
        public SynchronizeLogInfo CreateLogInfo(string apiURL, string arg, string inpatientID,
            string caseNumber, bool isPost)
        {
            var syncLog = new SynchronizeLogInfo
            {
                SynchronizeDate = DateTime.Now,
                ApiUrl = apiURL,
                PostOrGet = isPost ? "POST" : "Get",
                InpatientID = inpatientID,
                CaseNumber = caseNumber,
                Arguments = arg,
                SuccessFlag = "",
                ModifyDate = DateTime.Now,
                ModifyPersonID = MODIFYPERSIONID,
                DeleteFlag = ""
            };
            syncLog.ID = syncLog.GetId();
            return syncLog;
        }

        public SynchronizeLogInfo SaveLog(string apiURL, string arg, string inpatientID,
          string caseNumber, IUnitOfWork<MedicalDbContext> _unitOfWork, bool isCommit = true, bool isPost = true)
        {
            var syncLog = CreateLogInfo(apiURL, arg, inpatientID, caseNumber, isPost);
            _unitOfWork.GetRepository<SynchronizeLogInfo>().Insert(syncLog);
            if (isCommit)
            {
                int num = _unitOfWork.SaveChanges();
                if (num == 0)
                {
                    _logger.Error("写入Sync日志错误，API:" + syncLog.ApiUrl + "，请求：" + syncLog.PostOrGet + "，参数：" + arg);
                }
            }
            return syncLog;
        }

        /// <summary>
        /// 获取API执行结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="syncLog"></param>
        /// <param name="_unitOfWork"></param>
        public void GetAPIExecResult(string result, SynchronizeLogInfo syncLog, IUnitOfWork<MedicalDbContext> _unitOfWork)
        {
            try
            {
                ResponseResult response = JsonConvert.DeserializeObject<ResponseResult>(result);
                if (response != null && response.Code == 1 && response.Data != null && (bool)response.Data)
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.Modify(MODIFYPERSIONID);
                    _unitOfWork.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                _logger.Error("获取API执行结果失败：" + ex.ToString());
            }
        }

        #endregion --Sync日志

        public PatientEventInfo GetEvent(string inpatientID, string patientID, string chartNO,
            string caseNumber, int deptID, int stationID, int bedID, string bedNumber, DateTime occurDate,
            TimeSpan occurTime, int assessListID, string userID, string sourceID)
        {
            var patientEvent = new PatientEventInfo
            {
                InpatientID = inpatientID,
                PatientID = patientID,
                CaseNumber = caseNumber,
                ChartNo = chartNO,
                DepartmentListID = deptID,
                StationID = stationID,
                BedID = bedID,
                BedNumber = bedNumber,
                OccurDate = occurDate,
                OccurTime = occurTime,
                AssessListID = assessListID,
                SourceID = sourceID,
                AddEmployeeID = userID,
                AddDate = DateTime.Now,
                ModifyDate = DateTime.Now,
                ModifyPersonID = userID,
                DeleteFlag = ""
            };
            patientEvent.PatientEventID = patientEvent.GetId();

            return patientEvent;
        }

        public PatientEventInfo GetEvent(PatientOrderMainInfo orderMainInfo, DateTime occurDate, TimeSpan occurTime,
            int assessListID, string userID, string sourceID)
        {
            var patientEvent = new PatientEventInfo
            {
                SourceID = sourceID,
                InpatientID = orderMainInfo.InpatientID,
                PatientID = orderMainInfo.PatientID,
                CaseNumber = orderMainInfo.CaseNumber,
                ChartNo = orderMainInfo.ChartNo,
                DepartmentListID = 0, //这里DepartmentListID为0，另有定时任务补充完成
                StationID = orderMainInfo.StationID,
                BedID = orderMainInfo.BedID,
                BedNumber = orderMainInfo.BedNumber,
                OccurDate = occurDate,
                OccurTime = occurTime,
                AssessListID = assessListID,
                AddEmployeeID = userID,
                AddDate = DateTime.Now,
                ModifyDate = DateTime.Now,
                ModifyPersonID = userID,
                DeleteFlag = ""
            };
            patientEvent.PatientEventID = patientEvent.GetId();

            return patientEvent;
        }

        public PatientEventInfo GetEvent(InpatientDataInfo inpatient, DateTime occurDate,
            TimeSpan occurTime, int assessListID, string userID, string sourceID)
        {
            var patientEvent = new PatientEventInfo
            {
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                CaseNumber = inpatient.CaseNumber,
                ChartNo = inpatient.ChartNo,
                DepartmentListID = inpatient.DepartmentListID,
                StationID = inpatient.StationID,
                BedID = inpatient.BedID,
                BedNumber = inpatient.BedNumber,
                OccurDate = occurDate,
                OccurTime = occurTime,
                SourceID = sourceID,
                AssessListID = assessListID,
                AddEmployeeID = userID,
                AddDate = DateTime.Now,
                ModifyDate = DateTime.Now,
                ModifyPersonID = userID,
                DeleteFlag = ""
            };
            patientEvent.PatientEventID = patientEvent.GetId();

            return patientEvent;
        }

        /// <summary>
        /// 同步检验数据时，
        /// 过滤PatientProfile数据中的异常检验值，
        /// 发送MQ消息
        /// </summary>
        /// <param name="patientTestResultInfos"></param>
        /// <param name="inpatient"></param>
        public async Task SendExceptionLabMessage(List<PatientTestResultInfo> patientTestResultInfos, InpatientDataInfo inpatient)
        {
            try
            {
                List<string> caseNumbers = patientTestResultInfos.Select(m => m.CaseNumber).Distinct().ToList();
                foreach (var caseNumber in caseNumbers)
                {
                    var tmp = patientTestResultInfos.Where(m => m.CaseNumber == caseNumber).ToList();
                    if (tmp.Count == 0)
                    {
                        continue;
                    }
                    var arg = JsonConvert.SerializeObject(tmp);
                    var syncLog = SaveLog("http://localhost:56194/api/PatientProfile/SendExceptionLabMessage", arg
                   , inpatient.ID, tmp[0].CaseNumber, _unitOfWork, true);

                    WebRequestSugar wrs = new WebRequestSugar();
                    string result = await wrs.SendObjectAsJsonInBody("http://localhost:56194/api/PatientProfile/SendExceptionLabMessage", tmp);

                    GetAPIExecResult(result, syncLog, _unitOfWork);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "<MQ消息发送失败>");
            }
        }

        public PatientProfile CreateProfile(InpatientDataInfo inPatient, int assessListID, string assessValue, string hospitalID, string modifyPersonID, string modelName, string serialNum)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatient.ID,
                CaseNumber = inPatient.CaseNumber,
                ChartNo = inPatient.ChartNo,
                PatientID = inPatient.PatientID,
                ModelName = "HIS." + modelName,
                Source = "I",
                ProfileDate = DateTime.Now.Date,
                ProfileTime = DateTime.Now.TimeOfDay,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = modifyPersonID,
                SerialNumber = serialNum,
                AssessListID = assessListID,
                AssessValue = assessValue
            };
            return patientProfile;
        }

        public PatientProfile CreateProfile(InpatientDataInfo inPatient, int assessListID, DateTime assessDateTime, string hospitalID, string modifyPersonID, string modelName, string serialNum)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatient.ID,
                CaseNumber = inPatient.CaseNumber,
                ChartNo = inPatient.ChartNo,
                PatientID = inPatient.PatientID,
                ModelName = "HIS." + modelName,
                Source = "I",
                ProfileDate = assessDateTime.Date,
                ProfileTime = assessDateTime.TimeOfDay,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = modifyPersonID,
                SerialNumber = serialNum,
                AssessListID = assessListID,
                AssessValue = ""
            };
            return patientProfile;
        }
        /// <summary>
        /// 发送Post请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="inpatientID"></param>
        /// <param name="submit">请求体参数</param>
        /// <returns></returns>
        public async Task<bool> CallPostAPI(string url, string inpatientID, string casenumber, object submit)
        {
            try
            {
                var arg = JsonConvert.SerializeObject(submit);
                WebRequestSugar wrs = new WebRequestSugar();
                var syncLog = SaveLog(url, arg, inpatientID, casenumber, _unitOfWork, true);
                string result = await wrs.SendObjectAsJsonInBody(url, submit);
                GetAPIExecResult(result, syncLog, _unitOfWork);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("呼叫呼叫API" + url + ",失败" + ex.ToString());
                return false;
            }
        }
        /// <summary>
        ///病人信息发生变化，验证排程中的病人信息
        /// </summary>
        /// <param name="inPatientID"></param>
        public void CheckSchedulePatientInfo(string inPatientID, string caseNumber, bool isTransfer)
        {
            WebRequestSugar wrs = new WebRequestSugar();
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                { "inpatientID", inPatientID },
                { "dateTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm") },
                { "isTransfer", isTransfer.ToString() }
            };
            var hospitalID = _config.Value.HospitalID;
            var url = _config.Value.Status == "2" ? (_config.Value.SyncMedicalApi + "/api/PatientSchedule/CheckSchedulePatientInfo") : _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CheckSchedulePatientInfoAPI").Result;
            var arg = JsonConvert.SerializeObject(data);
            try
            {
                _logger.Info("病人信息发生变化，验证排程中的病人信息 呼叫API：" + url);
                var syncLog = SaveLog(url, arg, inPatientID, caseNumber, _unitOfWork, true, false);
                string result = wrs.HttpGet(url, data);
                _logger.Info("更新API执行结果：" + url);
                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _logger.Error("验证排程中病人信息失败|住院号" + caseNumber + "。url:"+ url +","+ ex.ToString());
            }
        }
        public async Task StopProblem(string inPatientID, string nursingLevel, bool stopProblem, MedicalEnumUtility.StopProblemType type)
        {
            var hospitalID = _config.Value.HospitalID;
            var modifyPersonId = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID");
            WebRequestSugar wrs = new WebRequestSugar();

            var submit = new StopProlemSubmit
            {
                InpatientID = inPatientID,
                EmployeeID = modifyPersonId,
                NursingLevel = nursingLevel,
                StopProlem = stopProblem,
                Type = type
            };

            try
            {
                var arg = JsonConvert.SerializeObject(submit);
                // 从配置档中获取数据
                var stopPatientProblemAPI = _config.Value.Status == "2" ? (_config.Value.SyncMedicalApi + "/api/External/StopPatientProblem") : await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "StopPatientProblemAPI");
                _logger.Info(" 停止病人问题 呼叫API：" + stopPatientProblemAPI);

                var syncLog = SaveLog(stopPatientProblemAPI,
                arg, inPatientID, "", _unitOfWork, true);

                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "StopProblem"
                   , "inPatientID[" + inPatientID + "] 呼叫Profile停止护理问题，" + stopPatientProblemAPI, "TongBu", true);
                string result = await wrs.SendObjectAsJsonInBody(stopPatientProblemAPI, submit);

                GetAPIExecResult(result, syncLog, _unitOfWork);
            }
            catch (Exception ex)
            {
                _syncLogService.InsertSyncLog(1, "1", "SyncInpatient", "StopProblem"
                   , "inPatientID[" + inPatientID + "] 呼叫Profile停止护理问题,失败", "TongBu", true);
                _logger.Error(ex, "停止病人问题失败");
            }
        }
        public string GetInterconnectData(string apiStr, Dictionary<string, string> data)
        {
            WebRequestSugar wrs = new WebRequestSugar();
            var result = new ResponseResult();
            //呼叫前写入log 
            _logger.Info("API||" + apiStr + "获取数据");
            //呼叫登入API
            string resultApi = "";
            try
            {
                resultApi = wrs.HttpGet(apiStr, data);
                if (resultApi.IndexOf("errorcode") > 0)
                {
                    _logger.Info("错误||获取失败||" + resultApi);
                    result.Code = 0;
                    result.Message = "获取失败";
                    return resultApi;
                }
            }
            catch (Exception ex)
            {
                _logger.Info("错误||验证人员||" + resultApi + "||" + ex.ToString());
                result.Code = 0;
                result.Message = ex.ToString();
                return resultApi;
            }
            return resultApi;
        }

    }
}