﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RankListRepository : IRankListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public RankListRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        public async Task<List<RankListInfo>> GetListAsync()
        {
            return await _medicalDbContext.RankListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
    }
}
