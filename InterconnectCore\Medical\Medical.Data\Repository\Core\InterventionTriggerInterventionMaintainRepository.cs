﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InterventionTriggerInterventionMaintainRepository : IInterventionTriggerInterventionMaintainRepository
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private MedicalDbContext _medicalDbContext;
        private readonly IAssessListRepository _assessListRepository;
        private readonly IFrequencyRepository _frequencyRepository;
        private readonly INursingInterventionMainRepository _nursingInterventionMainRepository;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly IUnitOfWork _unitOfWork;


        public InterventionTriggerInterventionMaintainRepository(
            MedicalDbContext medicalDbContext
            , IAssessListRepository assessListRepository
            , IFrequencyRepository frequencyRepository
            , INursingInterventionMainRepository nursingInterventionMainRepository
            , IClinicSettingRepository clinicSettingRepository
            , IUnitOfWork unitOfWork
            )
        {
            _medicalDbContext = medicalDbContext;
            _assessListRepository = assessListRepository;
            _frequencyRepository = frequencyRepository;
            _nursingInterventionMainRepository = nursingInterventionMainRepository;
            _clinicSettingRepository = clinicSettingRepository;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// 获取字典数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InterventionTriggerInterventionMaintainInfo>> GetAllData(string hospitalID)
        {
            return await _medicalDbContext.InterventionTriggerInterventionMaintainInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == int.Parse(hospitalID)).OrderByDescending(m => m.ModifyDate).ToListAsync();
        }
        /// <summary>
        ///根据主键获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InterventionTriggerInterventionMaintainInfo> GetDataByID(int id)
        {
            return await _medicalDbContext.InterventionTriggerInterventionMaintainInfos.Where(m => m.DeleteFlag != "*" && m.ID == id).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取触发措施字典表前端表格数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InterventionTriggerInterventionTableView>> GetTriggerInterventionTableData(string hospitalID)
        {
            var tableView = new List<InterventionTriggerInterventionTableView>();
            var triggerInterventionInfos = await GetAllData(hospitalID);
            var assessList = await _assessListRepository.GetAsync();
            var frequencyInfos = await _frequencyRepository.GetAllAsync<FrequencyInfo>();
            var nursingInterventionMain = await _nursingInterventionMainRepository.GetAsync();
            //药物触发
            var drugTriggerIntervention = triggerInterventionInfos.Where(m => !string.IsNullOrEmpty(m.DrugTriggerType) || m.AssessListID != 0).ToList();
            var drugTriggerTypeSetting = await _clinicSettingRepository.GetSettingByTypeCodeAndTypeVaule("RouteOfDrugUse", "PainDrug");
            foreach (var item in drugTriggerIntervention)
            {
                var itemView = new InterventionTriggerInterventionTableView();
                var sucIntervention = nursingInterventionMain.Find(m => m.ID == item.TriggerInterventionID);
                if (sucIntervention == null)
                {
                    continue;
                }
                if (!string.IsNullOrEmpty(item.DrugTriggerType))
                {
                    var sucDrugTriggerTypeSetting = drugTriggerTypeSetting.Find(m => m.SettingValue == item.DrugTriggerType);
                    if (string.IsNullOrEmpty(item.DrugTriggerType) || sucDrugTriggerTypeSetting == null)
                    {
                        continue;
                    }
                    itemView.AssessCode = "PainDrug";
                    itemView.AssessName = "疼痛药物";
                    itemView.DrugTriggerType = sucDrugTriggerTypeSetting.Description;
                    itemView.DrugTriggerTypeCode = sucDrugTriggerTypeSetting.SettingValue;
                }
                if (item.AssessListID != 0)
                {
                    var sucAssessList = assessList.Find(m => m.ID == item.AssessListID);
                    if (sucAssessList == null)
                    {
                        continue;
                    }
                    itemView.AssessCode = sucAssessList.ID.ToString();
                    itemView.AssessName = sucAssessList.Description;
                }
                itemView.ID = item.ID;
                itemView.Sex = item.Gender;
                itemView.SexName = item.Gender == "S" ? "" : "";//????
                itemView.MinAge = item.AgeFrom;
                itemView.MaxAge = item.AgeTo;
                itemView.MinValue = item.LowNotify.ToString("0.#####");
                itemView.MaxValue = item.UpNotify.ToString("0.#####");
                itemView.FrequencyID = item.FrequencyID;
                var sucFrequency = frequencyInfos.Find(m => m.ID == item.FrequencyID);
                itemView.Frequency = sucFrequency == null ? "" : sucFrequency.FrequencyDescription;
                itemView.TriggerInterventionID = item.TriggerInterventionID;
                itemView.TriggerIntervention = sucIntervention.Intervention;
                itemView.ModifyDate = item.ModifyDate.Value;
                tableView.Add(itemView);
            }
            return tableView.OrderByDescending(m => m.ModifyDate).ToList();
        }
        /// <summary>
        /// 获取触发措施下拉框配置
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> GetTriggerInterventionSelectOption(string hospitalID)
        {
            Dictionary<string, object> view = new Dictionary<string, object>();
            var drugTriggerTypeSetting = await _clinicSettingRepository.GetSettingByTypeCodeAndTypeVaule("RouteOfDrugUse", "PainDrug");
            if (drugTriggerTypeSetting.Count > 0)
            {
                var drugTypeList = new Dictionary<string, string>();
                drugTriggerTypeSetting.ForEach(m =>
                {
                    drugTypeList[m.SettingValue] = m.Description;
                });
                view["drugTypeList"] = drugTypeList;
            }
            var triggerInterventionInfos = await GetAllData(hospitalID);
            triggerInterventionInfos = triggerInterventionInfos.Where(m => m.AssessListID != 0 || !string.IsNullOrEmpty(m.DrugTriggerType)).ToList();
            var assessList = await _assessListRepository.GetAsync();
            var nursingInterventionMain = await _nursingInterventionMainRepository.GetAsync();
            var interventionList = new Dictionary<int, string>();
            var assessTypeList = new Dictionary<object, string>() {
                {"PainDrug","疼痛药物" }
            };
            foreach (var item in triggerInterventionInfos)
            {
                var sucIntervention = nursingInterventionMain.Find(m => m.ID == item.TriggerInterventionID);
                if (sucIntervention != null && !interventionList.ContainsKey(sucIntervention.ID))
                {
                    interventionList[sucIntervention.ID] = sucIntervention.Intervention;
                }
                if (item.AssessListID != 0)
                {
                    var sucAssessList = assessList.Find(m => m.ID == item.AssessListID);
                    if (sucAssessList == null || assessTypeList.ContainsKey(sucAssessList.ID))
                    {
                        continue;
                    }
                    assessTypeList[sucAssessList.ID] = sucAssessList.Description;
                    continue;
                }
            };
            view["interventionList"] = interventionList;
            view["assessTypeList"] = assessTypeList;
            return view;
        }
        /// <summary>
        /// 保存 修改
        /// </summary>
        /// <param name="saveView"></param>
        /// <param name="user"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SaveTriggerIntervention(InterventionTriggerInterventionTableView saveView, string user, string hospitalID)
        {
            var addFlag = true;
            var triggerInterventionInfo = new InterventionTriggerInterventionMaintainInfo();
            if (saveView.ID.HasValue)
            {
                addFlag = false;
                triggerInterventionInfo = await GetDataByID(saveView.ID.Value);
                if (triggerInterventionInfo == null)
                {
                    _logger.Error("修改数据失败，库中不存在该数据！");
                    return false;
                }
            }
            triggerInterventionInfo.Gender = saveView.Sex;
            triggerInterventionInfo.AgeFrom = saveView.MinAge;
            triggerInterventionInfo.AgeTo = saveView.MaxAge;
            triggerInterventionInfo.LowNotify = string.IsNullOrEmpty(saveView.MinValue) ? 0 : Convert.ToDecimal(saveView.MinValue.Trim());
            triggerInterventionInfo.UpNotify = string.IsNullOrEmpty(saveView.MaxValue) ? 0 : Convert.ToDecimal(saveView.MaxValue.Trim());
            triggerInterventionInfo.FrequencyID = saveView.FrequencyID;
            triggerInterventionInfo.TriggerInterventionID = saveView.TriggerInterventionID;
            triggerInterventionInfo.ConditionType = "TriggerOnce";
            triggerInterventionInfo.Frequency = "";
            triggerInterventionInfo.NumberOfTimes = 1;
            triggerInterventionInfo.DeleteFlag = "";
            triggerInterventionInfo.HospitalID = Convert.ToInt16(hospitalID);
            if (saveView.AssessCode == "PainDrug")
            {
                triggerInterventionInfo.DrugTriggerType = saveView.DrugTriggerTypeCode;
            }
            else
            {
                triggerInterventionInfo.AssessListID = string.IsNullOrEmpty(saveView.AssessCode) ? 0 : Convert.ToInt32(saveView.AssessCode);
            }
            if (addFlag)
            {
                triggerInterventionInfo.Modify(user);
                await _unitOfWork.GetRepository<InterventionTriggerInterventionMaintainInfo>().InsertAsync(triggerInterventionInfo);
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            var updataFlag = _medicalDbContext.Entry(triggerInterventionInfo).State != EntityState.Unchanged;
            if (updataFlag)
            {
                triggerInterventionInfo.Modify(user);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<bool> DeleteTriggerIntervention(int ID, string user)
        {
            var triggerInterventionInfo = await GetDataByID(ID);
            if (triggerInterventionInfo == null)
            {
                _logger.Error("删除数据失败，库中不存在该数据！");
                return false;
            }
            triggerInterventionInfo.Delete(user);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
