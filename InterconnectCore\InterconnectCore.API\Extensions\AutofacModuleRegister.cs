﻿using Autofac;
using NLog;
using System.Reflection;
using Module = Autofac.Module;

namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// 动态注册服务
    /// </summary>
    public class AutofacModuleRegister : Module
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        private readonly string[] DllNames = [
            "Medical.Data",
            "Medical.Common",
            "InterconnectCore.Data",
            "InterconnectCore.Service",
            "InterconnectCore.QueryService",
            "MedicalExternalCommon.Service"
        ];
        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        protected override void Load(ContainerBuilder builder)
        {
            var basePath = AppContext.BaseDirectory;
            foreach (var dllName in DllNames)
            {
                var dllFile = Path.Combine(basePath, $"{dllName}.dll");
                if (!File.Exists(dllFile))
                {
                    var msg = $"{dllName}.dll 丢失，因为项目解耦了，所以需要先生成，再运行，请检查 bin 文件夹，并拷贝。";
                    _logger.Error(msg);
                    continue;
                }
                var assemblyServices = Assembly.LoadFrom(dllFile);
                // 注册有接口的类
                builder.RegisterAssemblyTypes(assemblyServices)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();
                // 注册无接口的类
                builder.RegisterAssemblyTypes(assemblyServices)
                    .Where(t => t.GetInterfaces().Length == 0)
                    .AsSelf()
                    .InstancePerLifetimeScope();

                builder.RegisterAssemblyTypes(typeof(Program).Assembly)
                    .Where(m => m.Name.EndsWith("Service") && m.IsClass && !m.IsAbstract)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();
                builder.RegisterAssemblyTypes(typeof(Program).Assembly)
                    .Where(m => m.Name.EndsWith("Service") && m.IsClass && !m.IsAbstract)
                    .Where(m => m.GetInterfaces().Length == 0)
                    .AsSelf()
                    .InstancePerLifetimeScope();
                builder.RegisterType(typeof(SessionService)).AsSelf().InstancePerLifetimeScope();
            }
        }
    }
}
