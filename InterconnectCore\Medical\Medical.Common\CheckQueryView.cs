﻿using System;
using System.Collections.Generic;

namespace Medical.Common
{
    public class CheckQueryView
    {
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 医疗院所
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 病区序号
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 检核用日期
        /// </summary>
        public DateTime QueryDate { get; set; }
        /// <summary>
        /// 检核用时间
        /// </summary>
        public TimeSpan QueryTime { get; set; }
        /// <summary>
        /// 查询用表字段
        /// </summary>
        public Dictionary<string, string> QueryID { get; set; }

    }
}
