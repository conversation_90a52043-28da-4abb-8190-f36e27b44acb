﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class RelatedFactorRepository : IRelatedFactorRepository
    {
        private readonly MedicalDbContext _dbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public RelatedFactorRepository(MedicalDbContext db
              , IMemoryCache memoryCache
              , SessionCommonServer sessionCommonServer
              , GetCacheService getCacheService

            )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<RelatedFactorInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _dbContext.RelatedFactors.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.RelatedFactor.GetKey(_sessionCommonServer);
        }

        public async Task<List<RelatedFactorInfo>> GetAsync()
        {
            var datas = (List<RelatedFactorInfo>)await GetCacheAsync();
            if (datas != null)
            {
                return datas;
            }
            return new List<RelatedFactorInfo>();
        }
    }
}