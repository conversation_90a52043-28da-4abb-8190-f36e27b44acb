<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\InterconnectCore.Data.Interface\InterconnectCore.Data.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Models\InterconnectCore.Models.csproj" />
    <ProjectReference Include="..\InterconnectCore.Service.Interface\InterconnectCore.Service.Interface.csproj" />
  </ItemGroup>
</Project>