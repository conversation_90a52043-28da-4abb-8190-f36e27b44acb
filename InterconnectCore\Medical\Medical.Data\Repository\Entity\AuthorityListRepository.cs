﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AuthorityListRepository : IAuthorityListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public AuthorityListRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据类型和客户端类型获取权限
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        public async Task<List<AuthorityListInfo>> GetListAsync(string type, string clientType)
        {
            var list = await GetCacheAsync() as List<AuthorityListInfo>;
            return list = list.Where(t => t.FunctionClass.Trim() == type && t.ClientType.Trim() == clientType).ToList();
        }

        /// <summary>
        /// 查询所有权限
        /// </summary>
        /// <returns></returns>
        public async Task<List<AuthorityListInfo>> GetAllAuthorityLists()
        {
            return await GetCacheAsync() as List<AuthorityListInfo>;
        }
        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AuthorityListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.AuthorityListInfos.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AuthorityList.GetKey(_sessionCommonServer);
        }
    }
}