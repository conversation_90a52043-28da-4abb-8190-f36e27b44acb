﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class UserRepository : IUserRepository
    {
        private MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public UserRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<UserInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var datas = await _dbContext.Users.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            if (hospitalID.ToString() == "5")
            {
                return datas.Select(m => new UserInfo
                {
                    ID = m.ID,
                    HospitalID = m.HospitalID,
                    UserID = m.UserID,
                    PhysicianID = m.PhysicianID,
                    Password = EncryptionAndDecryption.DecryptStr(m.Password),
                    Name = m.Name,
                    StationID = m.StationID,
                    DepartmentID = m.DepartmentID,
                    Title = m.Title,
                    Rank = m.Rank,
                    JobID = m.JobID,
                    PhoneNumber = EncryptionAndDecryption.DecryptStr(m.PhoneNumber),
                    SpecialFlag = m.SpecialFlag,
                    HREmployeeID = m.HREmployeeID,
                    ExpirationDate = m.ExpirationDate,
                    EmployeeType = m.EmployeeType,
                    CapabilityLevelID = m.CapabilityLevelID,
                }).ToList();
            }
            return datas;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeelData.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 获取全部用户
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserInfo>> GetAsync()
        {
            var list =  await GetCacheAsync() as List<UserInfo>;
            return list.Where(m => m.ModifyPersonID != "ElectBoardUser").ToList();
        }
        /// <summary>
        /// 根据用户ID ,获取用户姓名
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<string> GetUserNameByEmployeeID(string employeeID)
        {
            var userInfo = await GetEmployeeByEmployeeIDAsync(employeeID);
            if (userInfo != null)
            {
                return userInfo.Name;
            }
            return "";
        }
        /// <summary>
        /// 根据用户ID获取用户信息（有缓存取缓存，没缓存取数据库）
        /// </summary>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        public async Task<UserInfo> GetEmployeeByEmployeeIDAsync(string modifyPersonID)
        {
            //先从缓存获取
            var userList = await GetCacheAsync() as List<UserInfo>;
            var userInfo = userList.Where(m => m.UserID == modifyPersonID).FirstOrDefault();
            if (userInfo != null)
            {
                return userInfo;
            }
            //没有从数据库取
            var session = await _sessionCommonServer.GetSession();
            userInfo = await _dbContext.Users.Where(m => m.UserID == modifyPersonID && m.HospitalID == session.HospitalID).FirstOrDefaultAsync();
            return userInfo;
        }
        public async Task<List<UserInfo>> GetAllUser()
        {
            var session = await _sessionCommonServer.GetSession();
            return await _dbContext.Users.Where(m => m.HospitalID == session.HospitalID).ToListAsync();
        }
        public async Task<List<UserInfo>> GetUserByEmployeeIDsNoCache(List<string> employeeIDs)
        {
            var list = await GetCacheAsync() as List<UserInfo>;
            if (list.Count >= 0)
            {
                return list.Where(m => employeeIDs.Contains(m.UserID)).ToList();
            }
            var session = await _sessionCommonServer.GetSession();
            var userInfo = await _dbContext.Users.Where(m => employeeIDs.Contains(m.UserID) && m.HospitalID == session.HospitalID).ToListAsync();
            return userInfo;
        }

    }
}