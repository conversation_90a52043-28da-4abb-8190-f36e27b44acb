﻿namespace InterconnectCore.Services.Interface
{
    public interface IDocumentQueryService
    {
        /// <summary>
        /// 获取患者病历集合列表
        /// </summary>
        /// <param name="caseNumber">住院号</param>
        /// <returns></returns>
        Task<object> GetPatientPDFList(string caseNumber);
        /// <summary>
        /// 获取患者病历PDF数据（Base64）
        /// </summary>
        /// <param name="nurseEMRFileListID">病历唯一ID号</param>
        /// <returns></returns>
        Task<object> GetPatientPDFData(string nurseEMRFileListID);
    }
}
