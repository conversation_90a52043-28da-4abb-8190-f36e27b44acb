﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DutyDoctorsRepository : IDutyDoctorsRepository
    {
        #region -- 字段
        public readonly MedicalDbContext _medicalDbContext;
        #endregion

        #region -- 构造函数
        public DutyDoctorsRepository(MedicalDbContext medicalDbContext)
        {
            this._medicalDbContext = medicalDbContext;
        }
        #endregion

        #region -- 实现接口方法
        /// <summary>
        /// 通过病区ID和值班日期获取值班数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="departmentID"></param>
        /// <param name="dutyDate"></param>
        /// <param name="shiftID"></param>
        /// <returns></returns>
        public async Task<List<DutyDoctorsInfo>> GetAsync(int stationID, int departmentID, DateTime dutyDate, int shiftID)
        {
            return await _medicalDbContext.DutyDoctors.Where(m => m.StationID == stationID && m.DepartmentID == departmentID && m.DutyDate == dutyDate && m.ShiftID == shiftID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 通过病区和值班日期获取值班数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="dutyDate"></param>
        /// <param name="shiftID"></param>
        /// <returns></returns>
        public async Task<List<DutyDoctorsInfo>> GetDutyByStationID(int stationID, DateTime dutyDate, int shiftID)
        {
            return await _medicalDbContext.DutyDoctors.Where(m => m.DutyDate != null && m.DutyDate.Value.Date == dutyDate.Date
                        && m.StationID == stationID && m.ShiftID == shiftID && m.DeleteFlag != "*").ToListAsync();
        }
        #endregion
    }
}
