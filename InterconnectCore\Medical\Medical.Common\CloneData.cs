﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Medical.Common
{
    public static class CloneData
    {
        public static List<T> Clone<T>(this List<T> list) where T : new()
        {
            var str = JsonConvert.SerializeObject(list);

            return JsonConvert.DeserializeObject<List<T>>(str);
        }

        public static T CloneObj<T>(this T t) where T : new()
        {
            var str = JsonConvert.SerializeObject(t);

            return JsonConvert.DeserializeObject<T>(str);
        }
    }
}
