﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class VirtualStationListRepository : IVirtualStationListRepository
    {

        private MedicalDbContext _MedicalDbContext = null;

        public VirtualStationListRepository(MedicalDbContext db)
        {
            _MedicalDbContext = db;
        }
        /// <summary>
        /// 获取所有病区
        /// </summary>
        /// <returns></returns>
        public async Task<List<VirtualStationListInfo>> GetAllVirtualStationAsync()
        {
            return await _MedicalDbContext.VirtualStationListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<VirtualStationListInfo> GetVirtualStationInfoByIdAsync(int virtualStationID)
        {
            return await _MedicalDbContext.VirtualStationListInfos.Where(m => m.DeleteFlag != "*" && m.VirtualStationID == virtualStationID).FirstAsync();
        }
        /// <summary>
        /// 获取虚拟病区的数量
        /// </summary>
        /// <returns></returns>
        public int GetCount()
        {
            return _MedicalDbContext.VirtualStationListInfos.Count();
        }
        /// <summary>
        /// 根据真实病区ID查询虚拟病区
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public List<VirtualStationListInfo> GetVirtualStationInfoByStationID(int stationID)
        {
            return _MedicalDbContext.VirtualStationListInfos.Where(m => m.DeleteFlag != "*" && m.StationID == stationID).ToList();
        }
        /// <summary>
        /// 根据HIsStationCode查询虚拟病区集合
        /// </summary>
        /// <param name="HISStationCode"></param>
        /// <returns></returns>
        public async Task<List<VirtualStationListInfo>> GetVirtualStationInfoByHISstationCodeAsync(string HISStationCode)
        {

            return await _MedicalDbContext.VirtualStationListInfos.Where(m => m.HISStationCode == HISStationCode).ToListAsync();
        }
    }
}
