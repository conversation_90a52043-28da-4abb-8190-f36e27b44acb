﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 调班申请
        /// </summary>
        public DbSet<ReSchduleApplyInfo> ReSchduleApplyInfos { get; set; }
        /// <summary>
        /// 输血log
        /// </summary>
        public DbSet<TransfusionLogInfo> TransfusionLogInfos { get; set; }
        /// <summary>
        /// 体温单护理常规
        /// </summary>
        public DbSet<TPRScheduleInfo> TPRScheduleInfos { get; set; }

        #region --新输血专项数据集注册
        /// <summary>
        /// 输血主表
        /// </summary>
        public DbSet<PatientTransfusionRecordInfo> PatientTransfusionRecordInfos { get; set; }

        /// <summary>
        /// 输血主表
        /// </summary>
        public DbSet<PatientTransfusionCareMainInfo> PatientTransfusionCareMainInfos { get; set; }

        /// <summary>
        ///  输血明细表(物理删除)
        /// </summary>
        public DbSet<PatientTransfusionCareDetailInfo> PatientTransfusionCareDetailInfos { get; set; }

        /// <summary>
        /// 输血血袋信息表
        /// </summary>
        public DbSet<PatientBloodBagMainInfo> PatientBloodBagMainInfos { get; set; }

        #endregion

        /// <summary>
        /// 措施取消勾选日志表
        /// </summary>
        public DbSet<PatientInterventionDeselectLogInfo> PatientInterventionDeselectLogInfos { get; set; }

        /// <summary>
        /// 关联执行配置表
        /// </summary>
        public DbSet<RelatedExecutionSettingInfo> RelatedExecutionSettingInfos { get; set; }
    }
}