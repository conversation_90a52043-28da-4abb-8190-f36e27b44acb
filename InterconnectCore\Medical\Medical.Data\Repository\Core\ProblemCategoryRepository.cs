﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ProblemCategoryRepository : IProblemCategoryRepository
    {
        private MedicalDbContext _dbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public ProblemCategoryRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ProblemCategoryInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _dbContext.ProblemCategories.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.ProblemToCategory.ToString();
        }
    }
}