﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class TubeListRepository : ITubeListRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public TubeListRepository(
            MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

        )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<TubeListInfo>> GetAsync()
        {
            var datas = await GetCacheAsync() as List<TubeListInfo>;
            if (datas != null)
            {
                return datas;
            }
            return null;
        }

        public async Task<TubeListInfo> GetByIDAsync(int tubeID)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Where(t => t.ID == tubeID).FirstOrDefault();
            }
            return null;
        }

        public async Task<List<TubeListInfo>> GetByTubeType(string tubeType)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Where(m => m.TubeType == tubeType).ToList();
            }
            return new List<TubeListInfo>();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<TubeListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.TubeListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.TubeList.GetKey(_sessionCommonServer);
        }

        public async Task<string> GetTubeShortNameByIDAsync(int tubeID)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                var data = datas.Where(m => m.ID == tubeID).FirstOrDefault();
                if (data != null)
                {
                    return data.TubeShortName;
                }
                return null;
            }
            return null;
        }
    }
}