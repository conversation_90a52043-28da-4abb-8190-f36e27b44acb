﻿using Medical.Models;

namespace InterconnectCore.ViewModels
{
    public class SyncBaseView
    {
        /// <summary>
        /// 患者列表图标信息
        /// </summary>
        public List<PatientListIconInfo> PatientListIconList { get; set; }
        /// <summary>
        /// 事件设置字典
        /// </summary>
        public List<EventSettingInfo> EventSettingList { get; set; }
        /// <summary>
        /// 人员字典
        /// </summary>
        public List<UserInfo> UserInfos { get; set; }
        /// <summary>
        /// 医院基础字典
        /// </summary>
        public HospitalBaseDictView HospitalBaseDictViews { get; set; }
        

    }
}