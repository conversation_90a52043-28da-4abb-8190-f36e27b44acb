﻿using Medical.ViewModels.View;

namespace InterconnectCore.Service.Interface
{
    public interface ISyncDataService
    {
        /// <summary>
        /// 回传HIS数据 启动定时任务 Medical调用
        /// </summary>
        /// <param name="syncData">回传数据</param>
        /// <param name="language">语言类别</param>
        /// <param name="hospitalID">医院类别</param>
        /// <returns></returns>
        bool SyncBackToHIS(SyncDataBackLog syncData, int language, string hospitalID);
        /// <summary>
        /// 回传HIS数据 定时任务调用
        /// </summary>
        /// <param name="syncData">回传数据</param>
        /// <param name="language">语言类别</param>
        /// <param name="hospitalID">医院类别</param>
        /// <returns></returns>
        Task<bool> SyncBackToHISAsyncJob(SyncDataBackLog syncData, int language, string hospitalID);
    }
}
