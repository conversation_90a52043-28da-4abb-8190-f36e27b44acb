﻿/*
 * 新增方法GetDetailByInpatientIDAndAssessListIDs，根据inpatient和assessListID集合获取最后一次明细数据 2022-4-27 苏军志
 */
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientThrombolysisCareDetailRepository : IPatientThrombolysisCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientThrombolysisCareDetailRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientThrombolysisCareDetailInfo>> GetDetailByMainIDAsync(string thronbolysisMainID)
        {
            return await _medicalDbContext.PatientThrombolysisCareDetailInfos.Where(t => t.PatientThrombolysisCareMainID == thronbolysisMainID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientThrombolysisCareDetailInfo>> GetDetailByRecordIDAsync(string recordID)
        {
            return await _medicalDbContext.PatientThrombolysisCareDetailInfos.Where(t => t.PatientThrombolysisCareMainID == recordID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<Dictionary<string, string>>> GetDetailByInpatientIDAndAssessListIDs(string inpatientID, int[] assessListIds)
        {
            var data = await _medicalDbContext.PatientThrombolysisCareDetailInfos.Where(t => t.InpatientID == inpatientID && assessListIds.Contains(t.AssessListID) && t.DeleteFlag != "*").ToListAsync();
            return data.OrderByDescending(t => t.ModifyDate).GroupBy(t => new { t.InpatientID, t.AssessListID })
                    .Select(t => new Dictionary<string, string>() {
                        { "key", t.First().AssessListID.ToString() } ,
                        { "value", t.First().AssessValue }
                    }).ToList();
        }
        /// <summary>
        /// 获取溶栓药物AssessListID
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="durgIDList"></param>
        /// <returns></returns>
        public async Task<int> GetDrugDetail(string recordID, List<int> durgIDList)
        {

            return await (from a in _medicalDbContext.PatientThrombolysisCareMainInfos
                          join b in _medicalDbContext.PatientThrombolysisCareDetailInfos
                          on a.PatientThrombolysisCareMainID equals b.PatientThrombolysisCareMainID
                          where a.PatientThrombolysisRecordID == recordID && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                           && a.RecordsCode == "ThrombolysisStart" && durgIDList.Contains(b.AssessListID)
                          select b.AssessListID).FirstOrDefaultAsync();
        }


    }
}
