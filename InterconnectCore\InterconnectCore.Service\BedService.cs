﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.Services.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.Extensions.Options;
using NLog;

namespace InterconnectCore.Service
{
    public class BedService : IBedService
    {
        #region 引用
        public readonly IBedListRepository _bedListRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private CommonHelper _commonHelper;
        private readonly IAppConfigSettingService _appConfigSettingService;
        #endregion

        #region 常量
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 配置码
        /// </summary>
        private string MODIFYPERSONID = "";
        /// <summary>
        /// 医疗院代码
        /// </summary>
        private string HOSPITALID = "";
        /// <summary>
        /// 更新缓存API
        /// </summary>
        private string CACHEUPDATEAPI = "";
        #endregion

        #region 构造函数
        public BedService(
               IBedListRepository bedListRepository
            , IOptions<SystemConfig> config
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IAppConfigSettingService appConfigSettingService
            , CommonHelper commonHelper
            )
        {
            _bedListRepository = bedListRepository;
            _config = config;
            _unitOfWork = unitOfWork;
            _appConfigSettingService = appConfigSettingService;
            _commonHelper = commonHelper;
            GetSettingConfig();
        }
        #endregion

        /// <summary>
        /// 获取配置
        /// </summary>
        private void GetSettingConfig()
        {
            HOSPITALID = _config.Value.HospitalID;
            MODIFYPERSONID = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID").Result;
            CACHEUPDATEAPI = _appConfigSettingService.GetConfigSetting(HOSPITALID, APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "CacheUpdateAPI").Result;
        }

        /// <summary>
        /// 新增床位
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="departmentID"></param>
        /// <param name="bedNumber"></param>
        /// <param name="bedSort"></param>
        /// <returns></returns>
        public async Task<bool> CreateOneBed(int stationID,int departmentID, string bedNumber, short bedSort)
        {
            //判断床位是否存在
            var bedListInfo = await _bedListRepository.GetByStationIDAndBedNumberNoCache(stationID, bedNumber);
            if (bedListInfo != null && bedListInfo.DeleteFlag != "*")
            {
                return true;
            }
            if (bedListInfo == null)
            {
                var maxID = _bedListRepository.GetMaxID();
                //床位不存在，增加床位
                var bedInfo = new BedListInfo
                {
                    ID = maxID,
                    BedNumber = bedNumber,
                    StationID = stationID,
                    DepartmentListID = departmentID,
                    ICUFlag = "",
                    DisableFlag = "",
                    Sort = bedSort,
                    ModifyDate = DateTime.Now,
                    ModifyPersonID = "TongBuNewBorn",
                    DeleteFlag = "",
                    NewBornFlag = "*",
                    HospitalID = _config.Value.HospitalID
                };
                _unitOfWork.GetRepository<BedListInfo>().Insert(bedInfo);
            }
            if (bedListInfo != null)
            {
                bedListInfo.DeleteFlag = "";
                bedListInfo.Modify("TongBu");
            }

            try
            {
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("Newborn新增床位失败" + ex.ToString());
                return false;
            }
            await _commonHelper.UpdateCache(CACHEUPDATEAPI + $"?keyName={CacheType.Bed}", null); 
            //增加更新床位缓存作业
            //BackgroundJob.Enqueue(() => _commonHelper.UpdateCache(query));
            return true;
        }
    }
}
