﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QCDictionaryDetailRepository : IQCDictionaryDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public QCDictionaryDetailRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<QCCheckDictionaryDetailInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.QCCheckDictionaryDetailInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.QCDictionaryDetail.ToString();
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<QCCheckDictionaryDetailInfo>> GetAllAsync()
        {
            return await _medicalDbContext.QCCheckDictionaryDetailInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取一个新的主键
        /// </summary>
        /// <param name="HospitalID"></param>
        /// <returns></returns>
        public async Task<int> GetNewID()
        {
            return await _medicalDbContext.QCCheckDictionaryDetailInfos.CountAsync();
        }
        /// <summary>
        /// 根据字典主表ID获取数据
        /// </summary>
        /// <param name="qCCheckMainID"></param>
        /// <returns></returns>
        public async Task<List<QCCheckDictionaryDetailInfo>> GetDataByMainID(int qCCheckMainID)
        {
            return await _medicalDbContext.QCCheckDictionaryDetailInfos.Where(m => m.QCCheckDictionaryMainID == qCCheckMainID && m.DeleteFlag != "*").ToListAsync();
        }

    }
}
