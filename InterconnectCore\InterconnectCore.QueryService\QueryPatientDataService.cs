﻿using InterconnectCore.QueryService.Interface;
using InterconnectCore.ViewModels.QueryPatientData;
using Medical.Data.Interface;


namespace InterconnectCore.QueryService
{
    public class QueryPatientDataService : IQueryPatientDataService
    {
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IPatientTubeRecordRepository _IPatientTubeRecordRepository;
        public QueryPatientDataService(
           IPatientEventRepository patientEventRepository,
            IInpatientDataRepository inpatientDataRepository,
            IPatientBasicDataRepository patientBasicDataRepository,
            IDepartmentListRepository departmentListRepository,
            IStationListRepository stationListRepository,
            IPatientTubeRecordRepository patientTubeRecordRepository
            )
        {
            _inpatientDataRepository = inpatientDataRepository;
            _patientEventRepository = patientEventRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _departmentListRepository = departmentListRepository;
            _stationListRepository = stationListRepository;
            _IPatientTubeRecordRepository = patientTubeRecordRepository;
        }
        /// <summary>
        /// 呼吸机辅助呼吸
        /// </summary>
        private int[] INTERVENTION_DETAILIDS = [60008906];
        /// <summary>
        ///气管插管ID
        /// </summary>
        private int[] TUBE_IDS = [48];


        public async Task<PatientDataView> GetPatientDeathData(string Casenumber)
        {
            var patientDataView = new PatientDataView();
            var inpatientView = await _inpatientDataRepository.GetByCaseNumberAsNoTracking(Casenumber);
            if (inpatientView == null)
            {
                return patientDataView;
            }         
            //获取患者基本信息
            var patientBasicDataView = await _patientBasicDataRepository.GetAsync(inpatientView.ChartNo);
            if (patientBasicDataView == null)
            {
                return patientDataView;
            }          
            //获取患者死亡事件
            var patientEventView = await _patientEventRepository.GetPatientEventViewByInpatientID(inpatientView.ID, 2876);
            if (patientEventView == null)
            {
                return patientDataView;
            }
            patientDataView= CreatePatientDataView();
            patientDataView.PatientName = patientBasicDataView.PatientName;
            patientDataView.CaseNumber = Casenumber;
            patientDataView.ChartNo = inpatientView.ChartNo;
            patientDataView.IdCard = patientBasicDataView.IdentityID;
            patientDataView.CreateDate = patientEventView.OccurDate.Add(patientEventView.OccurTime);
            patientDataView.Id = patientEventView.PatientEventID;
            patientDataView.PatientID= "01"+patientBasicDataView.IdentityID;
            //获取死亡事件科室信息
            var departmentInfo = await _departmentListRepository.GetAsync(patientEventView.DepartmentListID);
            if (departmentInfo != null)
            {
                patientDataView.DeptCode = departmentInfo.DepartmentCode;
                patientDataView.DeptName = departmentInfo.Department;
            }
            //获取患者死亡时是否在重症监护室
            var stationInfo = await _stationListRepository.GetAsync(patientEventView.StationID);
            if (stationInfo != null && (stationInfo.ICUFlag == "Y" || stationInfo.ICUFlag == "1"))
            {
                patientDataView.CriticalCareCode = "1";
                patientDataView.CriticalCareName = "1";
            }
            //确认患者是否使用呼吸机
            var patientTubes = await _IPatientTubeRecordRepository.GetPatientTubeRecordView(inpatientView.ID, TUBE_IDS, INTERVENTION_DETAILIDS);
            if (patientTubes.Count > 0)
            {
                patientDataView.VentilatorUsedCode = "1";
                patientDataView.VentilatorUsedName = "1";
            }
            return patientDataView;
        }

        /// <summary>
        /// 创建返回对象
        /// </summary>
        /// <returns></returns>
        private PatientDataView CreatePatientDataView()
        {
            return new PatientDataView
            {
                Id = "",
                DeptCode = "",
                DeptName = "",
                VentilatorUsedCode = "0",
                VentilatorUsedName = "",
                CriticalCareCode = "0",
                CriticalCareName = "",
                ActivityTypeCode = "6",
                ActivityTypeName = "住院",
                IdCardTypeCode = "01",
                IdCardTypeName = "居民身份证",
                OrgCode = "350206013",
                OrgName = "复旦大学附属中山医院厦门医院",
                QueryDateTime = DateTime.Now,
            };
        }
    }
}
