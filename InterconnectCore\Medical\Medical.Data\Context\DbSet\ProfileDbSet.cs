﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// Profile条件数据集
        /// </summary>
        public DbSet<PatientProfileConditionInfo> PatientProfileCondition { get; set; }
        /// <summary>
        /// PatientProfileInfo数据集
        /// </summary>
        public DbSet<PatientProfileInfo> PatientProfile { get; set; }
        /// <summary>
        /// PatientProfileLogInfo数据集
        /// </summary>
        public DbSet<PatientProfileLogInfo> PatientProfileLog { get; set; }

        /// <summary>
        /// PatientProfile病人注记清单
        /// </summary>
        public DbSet<PatientProfileMarkInfo> PatientProfileMark { get; set; }

        /// <summary>
        /// PatientFilesHeader数据集
        /// </summary>
        public DbSet<PatientFilesHeaderInfo> PatientFilesHeader { get; set; }

        /// <summary>
        /// 病人历史Profile
        /// </summary>
        public DbSet<PatientHistoryProfileInfo> PatientHistoryProfile { get; set; }
    }
}