﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Linq;
using System.Threading.Tasks;
//排除原因： NursingOrderToIntervention表不再使用
//操作人员：梁宝华 2020-03-17
namespace Medical.Data.Repository
{
    public class NursingOrderToInterventionRepository : INursingOrderToInterventionRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;

        public NursingOrderToInterventionRepository(MedicalDbContext db, IMemoryCache memoryCache)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
        }


        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var datas = await this._memoryCache.GetOrCreateAsync(key, async entry =>
            {
                entry.SetAbsoluteExpiration(TimeSpan.FromSeconds(6000));
                return await _medicalDbContext.NursingOrderToInterventionInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            });
            return datas;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingOrderToIntervention.ToString();
        }
    }
}
