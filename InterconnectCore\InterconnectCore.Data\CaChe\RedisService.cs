﻿using InterconnectCore.Common;
using InterconnectCore.Data.Interface;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using NLog;
using System.Text;

namespace InterconnectCore.Data
{
    public class RedisService : IRedisService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDistributedCache _cache;
        private readonly IMemoryCache _memoryCache;

        /// <summary>
        /// 设定内存过期时间为一周60*60*24*7
        /// </summary>
        private const int MEMORYCACHE_TIMEOUT = 604800;

        public RedisService(
            IDistributedCache cache,
             IMemoryCache memoryCache
            )
        {
            _cache = cache;
            _memoryCache = memoryCache;
        }

        public async Task<bool> Add<T>(string key, int expirationSecondTime, T value)
        {
            byte[] val = null;
            if (value.ToString() != "")
            {
                string data = JsonConvert.SerializeObject(value);

                val = Encoding.UTF8.GetBytes(data);
            }
            if (val == null)
            {
                return false;
            }

            //添加缓存
            await _cache.SetAsync(key, val);
            //刷新缓存
            await _cache.RefreshAsync(key);
            return true;
        }

        public async Task<bool> Exists(string key)
        {
            bool check = true;

            byte[] val = await _cache.GetAsync(key);

            if (val == null || val.Length == 0)
            {
                check = false;
            }
            return check;
        }

        public async Task<object> GetAsync(string key)
        {
            string str = null;
            object datas = null;
            if (string.IsNullOrEmpty(key))
            {
                return datas;
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                str = Encoding.UTF8.GetString(value);
                //datas = JsonConvert.DeserializeObject(str);
            }
            return str;
        }

        public async Task<T> GetAsync<T>(string key)
        {
            T datas = default(T);
            if (string.IsNullOrEmpty(key))
            {
                return datas;
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                string str = Encoding.UTF8.GetString(value);
                datas = JsonConvert.DeserializeObject<T>(str);
            }
            return datas;
        }


        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, Func<Task<T>> datas)
        {
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke();

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<object> GetOrCreateAsync(string key, int expirationSecondTime, int language, Func<int, Task<object>> datas)
        {
            var memoryData = await GetAsync(key);

            if (memoryData != null && !string.IsNullOrEmpty(memoryData.ToString()))
            {
                return memoryData;
            }

            var cacheDatas = await datas.Invoke(language);

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, Func<string, Task<T>> datas)
        {
            //获取内存缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                if (memoryData is List<object> temp && temp.Count() > 0)
                {
                    return memoryData;
                }
                if (memoryData is not List<object>)
                {
                    return memoryData;
                }
            }

            //查新数据库
            var cacheDatas = await datas.Invoke(hospitalID);

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }
        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, int language, Func<string, int, Task<T>> datas)
        {
            //获取内存缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke(hospitalID, language);
            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<bool> Update<T>(string key, int outSecondTime, T value)
        {
            bool check = false;
            if (value == null)
            {
                return check;
            }
            if (key != "" || key != null)
            {
                if (await Remove(key))
                {
                    check = await Add(key, outSecondTime, value.ToString());
                }
            }
            return check;
        }

        /// <summary>
        /// 根据传入的CacheMame 模糊比对删除缓存
        /// </summary>
        /// <param name="cacheName">传入all，清除所有缓存</param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<(bool, string)> RemoveCacheByNameAsync(string cacheName, string hospitalID, int language)
        {
            bool resultFlag;
            string messageStr;
            cacheName = cacheName.Trim();
            var dictionary = GetSystemAllCacheType();
            if (cacheName != "all")
            {
                dictionary = dictionary.Where(m => m.Key.Contains(cacheName)).ToDictionary(k => k.Key, v => v.Value);
            }
            foreach (var item in dictionary)
            {
                var key = item.Key.ToString();
                (resultFlag, messageStr) = await RemoveAllCacheByKey(key, hospitalID);

                if (!resultFlag)
                {
                    return (resultFlag, $"回收缓存{key}发生错误{messageStr},停止缓存回收");
                }
                (resultFlag, messageStr) = await CheckRemoveCacheByKey(key, hospitalID);
                if (!resultFlag)
                {
                    return (resultFlag, messageStr);
                }
            }
            messageStr = $"成功回收缓存数量{dictionary.Count}个,清单{ListToJson.ToJson(dictionary)}";
            return (true, messageStr);
        }

        public async Task<bool> Remove(string key)
        {
            bool check = false;
            if (key != "" || key != null)
            {
                await _cache.RemoveAsync(key);
                if (await Exists(key) == false)
                {
                    check = true;
                }
            }
            return check;
        }

        /// <summary>
        /// 获取系统所有字典缓存类型
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, int> GetSystemAllCacheType()
        {
            var dictionary = new Dictionary<string, int>();
            for (int i = 0; i < 300; i++)
            {
                var chacheName = Enum.GetName(typeof(CacheType), i);
                if (chacheName == null)
                {
                    continue;
                }
                dictionary.Add(chacheName, i);
            }
            return dictionary;
        }
        private async Task<(bool, string)> RemoveAllCacheByKey(string key, string hospitalID)
        {
            try
            {
                //CacheType
                await _cache.RemoveAsync(key);
                //CacheType+医院
                await _cache.RemoveAsync(key + "_" + hospitalID);
                //CacheType+中文
                await _cache.RemoveAsync(key + "_1");
                //CacheType+英文
                await _cache.RemoveAsync(key + "_2");
                //CacheType+医院+中文
                await _cache.RemoveAsync(key + "_" + hospitalID + "_1");
                //CacheType+医院+英文
                await _cache.RemoveAsync(key + "_" + hospitalID + "_2");
            }
            catch (Exception ex)
            {
                _logger.Error("清除缓存失败:" + ex.ToString());
                return (false, ex.ToString());
            }
            return (true, "");
        }

        /// <summary>
        /// 缓存清除验证
        /// </summary>
        /// <param name="key"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<(bool, string)> CheckRemoveCacheByKey(string key, string hospitalID)
        {
            var keys = key;
            var flag = await Exists(key);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }

            keys = key + "_" + hospitalID;
            flag = await Exists(keys);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }

            keys = (key + "_1");
            flag = await Exists(keys);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }

            keys = (key + "_2");
            flag = await Exists(keys);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }

            keys = key + "_" + hospitalID + "_1";
            flag = await Exists(keys);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }
            keys = key + "_" + hospitalID + "_2";
            flag = await Exists(keys);
            if (!flag)
            {
                return (false, $"缓存清理失败,key{keys}");
            }
            return (true, ""); ;
        }
    }
}
