﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientHistoryProfileRepository : IPatientHistoryProfileRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientHistoryProfileRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientHistoryProfileInfo>> GetPatientHistories(string chartNo)
        {
            return await _medicalDbContext.PatientHistoryProfile.Where(m => m.ChartNo == chartNo).ToListAsync();
        }

        public async Task<List<PatientHistoryProfileInfo>> GetPatientHistories(string chartNo, int assessListID)
        {
            return await _medicalDbContext.PatientHistoryProfile.Where(m => m.ChartNo == chartNo && m.AssessListID == assessListID).ToListAsync();
        }
    }
}
