﻿using System.ComponentModel.DataAnnotations.Schema;

namespace InterconnectCore.Models
{
    public class MutiModifyInfo : ModifyInfo
    {
        /// <summary>
        /// 新增人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }

        public MutiModifyInfo Add(string addEmployeeID)
        {
            AddDateTime = DateTime.Now;
            AddEmployeeID = addEmployeeID;
            return this;
        }
    }
}