﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 同步输血监测数据控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncBloodInspect")]
    [EnableCors("any")]
    public class SyncBloodInspectController : Controller
    {

        private readonly ISyncBloodInspectService _syncBloodInspectService;
        /// <summary>
        /// 构造注入
        /// </summary>
        /// <param name="syncBloodInspectService">同步输血监测数据服务</param>
        public SyncBloodInspectController(ISyncBloodInspectService syncBloodInspectService)
        {
            _syncBloodInspectService = syncBloodInspectService;
        }


        /// <summary>
        /// 同步输血监测数据到护理记录明细表
        /// </summary>
        /// <param name="inputViews">输血监测数据集合</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SyncBloodInspectDataToNursingRecordDetail([FromBody] List<BloodInspectInputView> inputViews)
        {
            var responseResult = new ResponseResult();
            if (inputViews == null)
            {
                 responseResult.Error("参数不能为空");
                return responseResult.ToJson();
            }
            (bool successFlag,string errorMessage) = await _syncBloodInspectService.SyncBloodInspectDataToNursingRecordDetail(inputViews);
            if (successFlag)
            {
                responseResult.Sucess();
            }
            else
            {
                responseResult.Error(errorMessage);
            }
            return responseResult.ToJson();
        }
    }
}
