﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientDeliriumCareMainRepository : IPatientDeliriumCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientDeliriumCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PatientDeliriumCareMainInfo> GetByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientDeliriumCareMainInfos
                .Where(m => m.PatientDeliriumCareMainID == careMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientDeliriumCareMainInfo>> GetByInpatientID(string inpatientID, DateTime? date = null)
        {
            if (date != null)
            {
                return await _medicalDbContext.PatientDeliriumCareMainInfos
                    .Where(m => m.InpatientID == inpatientID && m.AssessDate == date && m.DeleteFlag != "*").ToListAsync();
            }

            return await _medicalDbContext.PatientDeliriumCareMainInfos
                    .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientDeliriumCareMainInfo>> GetHandoverView(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var startDataTime = startDate.Add(startTime);

            var endDataTime = endDate.Add(endTime);

            var date = await _medicalDbContext.PatientDeliriumCareMainInfos.Where(m => m.InpatientID == inpatientID
                && m.BringToShift == true && m.DeleteFlag != "*"
                && m.AssessDate >= startDate && m.AssessDate <= endDate).ToListAsync();

            return date.Where(m => m.AssessDate.Add(m.AssessTime) >= startDataTime && m.AssessDate.Add(m.AssessTime) <= endDataTime).ToList();
        }

        public async Task<PatientDeliriumCareMainInfo> GetLatestByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientDeliriumCareMainInfos
                .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate)
                .ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<PatientDeliriumCareMainInfo> GetLastByInpatientID(string inpatientID, string careMainID)
        {
            return await _medicalDbContext.PatientDeliriumCareMainInfos
                .Where(m => m.InpatientID == inpatientID && m.PatientDeliriumCareMainID != careMainID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AssessDate)
                .ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<AssessTimeView> GetAssessTimeByID(string careMainID)
        {
            return await _medicalDbContext.PatientDeliriumCareMainInfos
                .Where(m => m.PatientDeliriumCareMainID == careMainID && m.DeleteFlag != "*")
                 .Select(m => new AssessTimeView { StartDate = m.AssessDate, StartTime = m.AssessTime })
               .FirstOrDefaultAsync();
        }

        public async Task<List<PatientDeliriumCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientDeliriumCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }
        /// <summary>
        /// 获取交班需要带入的内容 跟GetHandoverView方法不同的是1.删除了BringToshift字段的判断 2.只取所需字段
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientDeliriumCareMainInfo>> GetHandoverViewAsync(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var startDataTime = startDate.Add(startTime);
            var endDataTime = endDate.Add(endTime);

            var date = await _medicalDbContext.PatientDeliriumCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && m.AssessDate >= startDate && m.AssessDate <= endDate).Select(m => new PatientDeliriumCareMainInfo
                {
                    PatientDeliriumCareMainID = m.PatientDeliriumCareMainID,
                    InpatientID = m.InpatientID,
                    RiskResult = m.RiskResult,
                    AssessResult = m.AssessResult,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    BringToShift = m.BringToShift,
                    RecordsCode = m.RecordsCode,
                }).ToListAsync();

            return date.Where(m => m.AssessDate.Add(m.AssessTime) >= startDataTime && m.AssessDate.Add(m.AssessTime) <= endDataTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}