{
  "ConnectionStrings": {
    //咸阳测试环境
    //"DataInConnection": "server=10.10.13.150;database=medical;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"HangFireConnection": "server=10.10.13.150;database=hangfire;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"FilesConnection": "server=10.10.13.150;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"DataOutConnection": "server=10.10.13.150;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    //"MedicalStatistics": "server=10.10.13.150;database=medical;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"Cache": "127.0.0.1:6379"

    "DataInConnection": "server=www.honLivit.com;database=medical_Dev;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "HangFireConnection": "server=www.honLivit.com;database=hangfire;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "FilesConnection": "server=www.honlivit.com;database=Interconnect;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "DataOutConnection": "server=www.honLivit.com;database=Interconnect;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "MedicalStatistics": "server=60.205.113.132;database=medical_Dev;uid=zhongYunCCC;pwd=**`1q;TrustServerCertificate=true",
    "RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    "Cache": "127.0.0.1:6379"
  },

  "Cache": {
    "Dictionary": 3000
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Configs": {
    "HospitalID": "8",
    "Language": 1,
    "ServerType": 2,
    "UseCacheType": "Memory" //使用缓存类型：Redis、Memory
  },
  "AllowedHosts": "*"
}
