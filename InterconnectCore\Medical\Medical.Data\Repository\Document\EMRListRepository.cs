﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EMRListRepository : IEMRListRepository
    {

        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public EMRListRepository(
            MedicalDbContext medicalDb, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = medicalDb;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<EMRListInfo>> GetEMRList()
        {
            var data = await GetCacheAsync() as List<EMRListInfo>;

            return data.Where(m => m.EMRFlag != null && m.EMRFlag.Value).ToList();
        }

        public async Task<EMRListInfo> GetEmrByFileClass(int fileClassID)
        {
            var list = await GetEMRList();

            if (list != null && list.Count > 0)
            {
                return list.Where(m => m.FileClassID == fileClassID).FirstOrDefault();
            }
            return null;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EMRListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.EMRListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NurseEMR.GetKey(_sessionCommonServer);
        }

        public async Task<List<EMRListView>> GetEMRView()
        {
            var datas = await GetCacheAsync() as List<EMRListInfo>;

            return datas.Where(m => m.EMRFlag != null && m.EMRFlag.Value).Select(m => new EMRListView
            {
                EMRListID = m.ID,
                FileClassID = m.FileClassID,
                FileClassName = m.FileClassName,
                FileCode = m.FileCode,
                EMRCategory = m.EMRCategory,
                Sort = m.Sort,
                Style = m.Style,
                VerifyAPI = m.VerifyAPI,
                MergeFlag = m.MergeFlag
            }).ToList();
        }

        public async Task<string> GetEMRName(int fileClassID)
        {
            var datas = await GetCacheAsync() as List<EMRListInfo>;

            var data = datas.Find(m => m.FileClassID == fileClassID && m.EMRFlag != null && m.EMRFlag.Value);

            if (data != null)
            {
                return data.FileClassName;
            }

            return "";
        }

        public async Task<List<EMRListView>> GetCommonEMRListView(string eMRCategory)
        {
            var datas = await GetCacheAsync() as List<EMRListInfo>;

            return datas.Where(m => m.EMRCategory == eMRCategory
                          && m.EMRFlag != null && m.EMRFlag.Value
                          && m.VerifyAPI != null && m.VerifyAPI != "" && m.DeleteFlag != "*")
             .Select(m => new EMRListView
             {
                 EMRListID = m.ID,
                 FileClassID = m.FileClassID,
                 FileClassName = m.FileClassName,
                 FileCode = m.FileCode,
                 EMRCategory = m.EMRCategory,
                 Sort = m.Sort,
                 Style = 0,
                 VerifyAPI = m.VerifyAPI
             }).ToList();
        }
        public async Task<bool> GetMergeFlagByFileClass(int fileClassID)
        {
            var datas = await GetCacheAsync() as List<EMRListInfo>;
            var result = datas.Find(m => m.FileClassID == fileClassID && m.DeleteFlag != "*");
            if (result != null)
            {
                return result.MergeFlag;
            }
            else
            {
                return false;
            }
        }
    }
}
