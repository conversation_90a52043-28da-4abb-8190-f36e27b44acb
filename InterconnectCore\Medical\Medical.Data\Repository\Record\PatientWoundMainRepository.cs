﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientWoundMainRepository : IPatientWoundMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientWoundMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        ///  获取病人所有伤口记录
        /// </summary>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetAsync(string inPatientID)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.InpatientID == inPatientID &&
            m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        ///  获取病人评估信息
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetAssessInfoAsync(string AssessMainID, string inPatientID)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.AssessMainID.Trim() == AssessMainID.Trim() &&
            m.InpatientID == inPatientID &&
            m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        ///  获取病人未结束伤口记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetUnFinishedAsync(string inPatientID)
        {
            return await GetAsync(inPatientID).Result
                .Where(m => m.EndDate.ToString().Length == 0)
                .ToAsyncEnumerable().ToList();
        }

        /// <summary>
        ///  获取病人伤口记录
        /// </summary>
        /// <param name="PatientWoundMainID">ID</param>
        /// <returns></returns>
        public async Task<PatientWoundMainInfo> GetPatientWoundMainAsync(string PatientWoundMainID)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.PatientWoundMainID == PatientWoundMainID &&
            m.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        ///  获取病人所有伤口主ID记录
        /// </summary>
        /// <param name="WoundKind">种类</param>
        /// <param name="BodyPartID">部位</param>
        /// <param name="WoundCode">代号</param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetPatientWoundMainByParm(string WoundKind, string BodyPartID, string WoundCode)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.WoundKind == WoundKind && m.BodyPartID == int.Parse(BodyPartID) && m.WoundCode == WoundCode &&
            m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据排程主ID获取伤口记录
        /// </summary>
        /// <param name="ids">排程主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetByScheduleMainIDs(List<string> ids)
        {
            var patientWoundMainList = new List<PatientWoundMainInfo>();
            foreach (var item in ids)
            {
                var tempList = await _medicalDbContext.PatientWoundMainInfos.Where(m =>
                m.PatientScheduleMainID == item && m.DeleteFlag != "*").ToListAsync();
                patientWoundMainList = patientWoundMainList.Union(tempList).ToList();
            }
            return patientWoundMainList;
        }

        /// <summary>
        /// 获取时间点后为结束的伤口
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetAsync(string inpatientID, int stationID, DateTime dateTime)
        {
            //未结束伤口
            var list = await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.InpatientID == inpatientID && m.StationID == m.StationID
            && (m.EndDate.ToString().Length == 0 || m.EndDate == null)
            && m.DeleteFlag != "*").ToListAsync();
            //结束时间在传入时间点后的
            list.AddRange(await _medicalDbContext.PatientWoundMainInfos.Where(m =>
           m.InpatientID == inpatientID && m.StationID == m.StationID
           && m.EndDate != null && ((DateTime)m.EndDate).Add((TimeSpan)m.EndTime) > dateTime
           && m.DeleteFlag != "*").ToListAsync());

            return list;
        }

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="InpatientID"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundMainInfo>> GetPatientWoundMainByNumAsync(string InpatientID, string num)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.InpatientID == InpatientID &&
            m.AssessMainID == num && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundMainInfo>> GetAsync(int stationID, string inPatientID,
            string woundCode, int bodyPartID)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m =>
            m.StationID == stationID &&
            m.InpatientID == inPatientID &&
            m.WoundCode == woundCode &&
            m.BodyPartID == bodyPartID &&
            m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundMainInfo>> GetByMainID(string[] mainIDs)
        {
            var patientWoundMainList = new List<PatientWoundMainInfo>();
            for (int i = 0; i < mainIDs.Length; i++)
            {
                var tempList = await _medicalDbContext.PatientWoundMainInfos.Where(m => m.PatientWoundMainID == mainIDs[i]).ToListAsync();
                patientWoundMainList = patientWoundMainList.Union(tempList).ToList();
            }
            return patientWoundMainList;
        }

        public async Task<List<PatientWoundMainInfo>> GetByWoundKind(string inpatientID, string woundKind)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m => m.InpatientID == inpatientID
             && m.WoundKind == woundKind).ToListAsync();
        }

        public async Task<List<PatientWoundMainInfo>> GetByDataPumpDate(DateTime yesterday, DateTime today)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(
                                m => m.DataPumpDate >= yesterday && m.DataPumpDate < today && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundMainInfo>> GetPressureSoresByWoundKind(string woundKind)
        {
            return await _medicalDbContext.PatientWoundMainInfos.Where(m => m.WoundKind == woundKind && m.DeleteFlag != "*").ToListAsync();
        }
    }
}