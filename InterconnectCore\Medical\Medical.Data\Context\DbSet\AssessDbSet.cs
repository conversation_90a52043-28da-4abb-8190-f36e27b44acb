﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {

        /// <summary>
        /// 评估项目数据集
        /// </summary>
        public DbSet<AssessNormalCheckInfo> AssessNormalChecks { get; set; }
        public DbSet<AssessInteractionInfo> AssessInteractions { get; set; }
        public DbSet<AssessListIDToColorInfo> AssessListIDToColorInfos { get; set; }
        public DbSet<AssessScoreRangeInfo> AssessScoreRangeInfos { get; set; }
        public DbSet<DepartmentToAssessInfo> DepartmentToAssessInfos { get; set; }
        public DbSet<AllergyDrugInfo> AllergyDrugInfos { get; set; }
        public DbSet<AllergyBasicInfo> AllergyBasicInfos { get; set; }
        public DbSet<PatientAllergyInfo> PatientAllergyInfos { get; set; }
    }
}
