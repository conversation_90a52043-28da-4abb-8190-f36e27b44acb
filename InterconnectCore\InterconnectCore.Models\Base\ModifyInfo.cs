﻿using System.ComponentModel.DataAnnotations.Schema;

namespace InterconnectCore.Models
{
    public class ModifyInfo : BaseInfo
    {
        /// <summary>
        /// 修改人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ModifyPersonID { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDate { get; set; }

        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string DeleteFlag { get; set; } = "";

        public ModifyInfo Modify(string modifyEmployeeID)
        {
            ModifyDate = DateTime.Now;
            ModifyPersonID = modifyEmployeeID;
            return this;
        }

        /// <summary>
        /// 设置删除
        /// </summary>
        public void Delete(string modifyEmployeeID)
        {
            Modify(modifyEmployeeID);
            DeleteFlag = "*";
        }
        /// <summary>
        /// 抽档日期
        /// </summary>
        public DateTime? DataPumpDate { get; set; }
        /// <summary>
        /// 抽档标志   标志 *表示已经抽档
        /// </summary>
        public string DataPumpFlag { get; set; }

        /// <summary>
        /// 同步次数
        /// </summary>
        public int? Counts { get; set; }
    }
}