﻿using InterconnectCore.Services.Interface;
using Medical.Data.Interface;
using Medical.Models;

namespace InterconnectCore.Services
{
    public class AppConfigSettingService : IAppConfigSettingService
    {
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        public AppConfigSettingService(IAppConfigSettingRepository appConfigSettingRepository)
        {
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        public async Task<string> GetConfigSetting(string hospitalID, string settingType, string settingCode)
        {
            var datas = await _appConfigSettingRepository.GetAllAsync<AppConfigSettingInfo>();

            if (datas.Count == 0)
            {
                return "";
            }

            var returnData = datas.Find(m => m.HospitalID == hospitalID && m.SettingType == settingType && m.SettingCode == settingCode);

            if (returnData == null)
            {
                return "";
            }

            return returnData.SettingValue;
        }

        public async Task<List<AppConfigSettingInfo>> GetConfigSetting(string hospitalID, string settingType)
        {
            var datas = await _appConfigSettingRepository.GetAllAsync<AppConfigSettingInfo>();

            if (datas.Count == 0)
            {
                return datas;
            }

            return datas.Where(m => m.HospitalID == hospitalID && m.SettingType == settingType).ToList();
        }
    }
}