﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.CDADocument;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AssessMainRepository : IAssessMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;

        public AssessMainRepository(
              MedicalDbContext medicalDbContext,
              SessionCommonServer sessionCommonServer
            )
        {
            _medicalDbContext = medicalDbContext;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<PatientAssessMainInfo>> GetAsync(string inpatientID, bool? additional = null)
        {
            var list = await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderByDescending(m => m.Sort).ToListAsync();
            if (additional != null)
            {
                list = list.Where(t => t.Additional == additional.Value).OrderByDescending(m => m.Sort).ToList();
            }
            return list;
        }

        public async Task<PatientAssessMainInfo> GetMainIDAsync(string inpatientID, int count)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.NumberOfAssessment == count && m.Additional == false && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<PatientAssessMainInfo> GetLastAsync(string inpatientID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.Additional == false && m.DeleteFlag != "*")
                  .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<PatientAssessMainInfo> GetLastAsync(string inpatientID, string mainID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.ID != mainID
                    && m.Additional == false && m.DeleteFlag != "*")
                  .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<PatientAssessMainInfo> GetLastAsync(string inpatientID, int stationID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.Additional == false && m.DeleteFlag != "*")
                  .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<List<PatientAssessMainInfo>> GetAssessCarePlan(DateTime startDate, DateTime endDate)
        {
            var session = await _sessionCommonServer.GetSession();
            var hospitalID = session.HospitalID;
            var result = await (from assess in _medicalDbContext.AssessMains
                                join patient in _medicalDbContext.PatientBasicDatas on assess.ChartNo equals patient.ChartNo
                                join uses in _medicalDbContext.Users on assess.AddEmployeeID equals uses.UserID
                                join inpatient in _medicalDbContext.InpatientDatas on assess.InpatientID equals inpatient.ID
                                where assess.AssessDate >= startDate && assess.AssessDate <= endDate && assess.TempSaveMark != "T"
                                && assess.Additional == false && assess.DeleteFlag != "*" && uses.DeleteFlag != "*"
                                && uses.HospitalID == hospitalID
                                && inpatient.HospitalID == hospitalID && patient.HospitalID == hospitalID
                                select new PatientAssessMainInfo
                                {
                                    ID = assess.ID,
                                    AssessDate = assess.AssessDate,
                                    ChartNo = inpatient.LocalCaseNumber,// 病案号 统一改成LocalCaseNumber
                                    StationID = assess.StationID,
                                    NumberOfAssessment = assess.NumberOfAssessment,
                                    AddEmployeeID = uses.Name,
                                    SuggestProblem = assess.SuggestProblem,
                                    CheckProblem = assess.CheckProblem,
                                    NurseAddProblem = assess.NurseAddProblem,
                                    SuggestProblemCount = assess.SuggestProblemCount,
                                    CheckProblemCount = assess.CheckProblemCount,
                                    NurseAddProblemCount = assess.NurseAddProblemCount,
                                    EMRFlag = patient.PatientName,
                                    AddDate = inpatient.AdmissionDate,
                                    Diagnosis = assess.Diagnosis,
                                    NursingLevel = assess.NursingLevel,
                                    SurgaryDate = assess.SurgaryDate
                                }).ToListAsync();

            return result;
        }

        public async Task<List<PatientAssessMainInfo>> GetAssessByStationId(string inpatientID, int stationID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.Additional == false && m.DeleteFlag != "*").ToListAsync();
        }


        public async Task<PatientAssessMainInfo> GetPlanAssessmentByDateTime(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.Additional == false
                                    && m.AddDate <= dateTime && m.DeleteFlag != "*" && m.TempSaveMark != "T").OrderByDescending(m => m.AddDate).FirstOrDefaultAsync();
        }

        public async Task<PatientAssessMainInfo> GetByID(string assessMainID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.ID == assessMainID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 取得异动时间以后的数据到CDAView
        /// </summary>
        /// <param name="dateTime">日期时间</param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<CDR_AdmissionAssessmentInfo>> GetCDAViewByModifyDate(DateTime dateTime, string hospitalID)
        {

            var result = await (from assess in _medicalDbContext.AssessMains
                                join inpatient in _medicalDbContext.InpatientDatas on assess.InpatientID equals inpatient.ID
                                join bedList in _medicalDbContext.BedListInfos on assess.BedID equals bedList.ID
                                join patientBasic in _medicalDbContext.PatientBasicDatas on assess.ChartNo equals patientBasic.ChartNo
                                join station in _medicalDbContext.StationList on new { ID = assess.StationID, hospitalID } equals new { station.ID, hospitalID = station.HospitalID }
                                join departmnt in _medicalDbContext.DepartmentListInfos on assess.DepartmentListID equals departmnt.ID
                                join employee in _medicalDbContext.Users on assess.AddEmployeeID equals employee.UserID
                                where assess.ModifyDate >= dateTime && assess.NumberOfAssessment == 1 && assess.TempSaveMark != "T" &&
                                       departmnt.HospitalID == hospitalID && inpatient.HospitalID == hospitalID && bedList.HospitalID == hospitalID
                                       && patientBasic.HospitalID == hospitalID && employee.HospitalID == hospitalID
                                select new CDR_AdmissionAssessmentInfo
                                {
                                    PatientAssessMainID = assess.ID,
                                    InpatientID = assess.InpatientID,
                                    ChartNo = assess.ChartNo,
                                    CaseNumber = assess.CaseNumber,
                                    LocalChartNO = patientBasic.LocalChartNO,
                                    DeptCode = departmnt.DepartmentCode,
                                    WardAreaCode = station.StationCode,
                                    SickbedId = assess.BedNumber,
                                    Age = inpatient.Age.ToString(),
                                    WardRoomNo = bedList.RoomCode,
                                    VisitID = inpatient.NumberOfAdmissions.ToString(),
                                    Sex = patientBasic.Gender,
                                    Name = patientBasic.PatientName,
                                    HospizationId = assess.CaseNumber,
                                    MedicalRecordId = assess.ChartNo,
                                    IdCard = patientBasic.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(patientBasic.IdentityID) : patientBasic.IdentityID,
                                    IdentityCard = patientBasic.HospitalID == "5" ? EncryptionAndDecryption.DecryptStr(patientBasic.IdentityID) : patientBasic.IdentityID,
                                    BirthDay = patientBasic.DateOfBirth.Value,
                                    PatientType = "04",
                                    EffectiveFlag = "1",
                                    AdmissionDateTime = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                                    AssessmentDateTime = assess.AssessDate.Add(assess.AssessTime),
                                    DeptName = departmnt.Department,
                                    WardAreaName = station.StationName,
                                    ResponsibilityNurse = employee.Name,
                                    DeleteFlag = assess.DeleteFlag,
                                    AdmissionDiagCode = inpatient.ICDCode,
                                    AdmissionDiagName = inpatient.Diagnosis,
                                    DocumentAuthor = assess.ModifyPersonID,
                                    SignDateTime = assess.ModifyDate,
                                    TimeStamp = assess.ModifyDate.Value,
                                    //2022-06-13补充-主诉
                                    MasterSymptom = inpatient.ChiefComplaint,
                                }).ToListAsync();
            return result;
        }

        public async Task<DateTime> GetFistAssessModify()
        {
            var data = await _medicalDbContext.AssessMains
                .Where(m => m.NumberOfAssessment == 1).OrderBy(m => m.ModifyDate).FirstOrDefaultAsync();

            if (data == null)
            {
                return DateTime.Now;
            }

            if (data.ModifyDate == null)
            {
                return DateTime.Now;
            }
            return data.ModifyDate.Value;
        }
        //获取规定时间内的非暂存评估数据
        public async Task<List<PatientAssessMainInfo>> GetAdditionalAssess(string inpatientID, string TempSaveMark)
        {
            var list = await _medicalDbContext.AssessMains.
                Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.TempSaveMark == TempSaveMark)
                .OrderByDescending(m => m.Sort).ToListAsync();
            return list;
        }
        /// <summary>
        /// 根据病人住院号获取病人历次评估数据
        /// </summary>
        /// <param name="CaseNumber"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessMainInfo>> GetAssessByCaseNumberAsync(string CaseNumber)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.CaseNumber == CaseNumber && m.DeleteFlag != "*").OrderByDescending(m => m.AddDate).ToListAsync();
        }

        public async Task<List<AssessRecordTransferView>> GetAssess(string assessMainID)
        {
            var query = await (from m in _medicalDbContext.AssessMains
                               join n in _medicalDbContext.AssessDetails on m.ID equals n.PatientAssessMainID
                               where m.ID == assessMainID && m.DeleteFlag != "*" && n.DeleteFlag != "*"
                               select new AssessRecordTransferView
                               {
                                   AssessDate = m.AssessDate,
                                   AddDate = m.AddDate,
                                   RecordsCode = m.RecordsCode,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue,
                                   PatientID = m.PatientID,
                                   StationID = m.StationID,
                                   CaseNumber = m.CaseNumber,
                                   ChartNo = m.ChartNo,
                                   BedID = m.BedID,
                                   BedNumber = m.BedNumber,
                                   NumberOfAssessment = m.NumberOfAssessment,
                                   ModifyPersonID = m.ModifyPersonID,
                                   InpatientID = m.InpatientID
                               }).ToListAsync();
            if (query.Count <= 0)
            {
                return await _medicalDbContext.AssessMains.Where(m => m.ID == assessMainID && m.DeleteFlag != "*").Select(m =>
                    new AssessRecordTransferView
                    {
                        AssessDate = m.AssessDate,
                        AddDate = m.AddDate,
                        RecordsCode = m.RecordsCode,
                        PatientID = m.PatientID,
                        StationID = m.StationID,
                        CaseNumber = m.CaseNumber,
                        ChartNo = m.ChartNo,
                        BedID = m.BedID,
                        BedNumber = m.BedNumber,
                        NumberOfAssessment = m.NumberOfAssessment,
                        ModifyPersonID = m.ModifyPersonID,
                        InpatientID = m.InpatientID
                    }).ToListAsync();
            }
            return query;
        }

        public async Task<List<PatientAssessDetailView>> GetPatientAssessDetail(string inpatientID, int stationID, DateTime startDate, DateTime endDate)
        {
            var query = await (from m in _medicalDbContext.AssessMains
                               join n in _medicalDbContext.AssessDetails on m.ID equals n.PatientAssessMainID
                               where m.InpatientID == inpatientID && m.StationID == stationID
                               && m.AssessDate >= startDate && m.AssessDate <= endDate
                               && m.DeleteFlag != "*"
                               select new PatientAssessDetailView
                               {
                                   AssessDate = m.AssessDate,
                                   AssessTime = m.AssessTime,
                                   AssessListID = n.AssessListID,
                                   AssessValue = n.AssessValue
                               }).ToListAsync();
            return query;

        }

        public async Task<PatientAssessMainInfo> GetByCode(string inpatientID, string recordsCode)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.RecordsCode == recordsCode
            && m.DeleteFlag != "*").OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).FirstOrDefaultAsync();
        }

        public async Task<AssessTimeView> GetAssessTimeByID(string assessMainID)
        {
            var assessMain = await _medicalDbContext.AssessMains.Where(m => m.ID == assessMainID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.AssessDate, StartTime = m.AssessTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        public async Task<string> GetLastAssessID(string inpatientID, DateTime dateTime)
        {
            var endDate = dateTime.AddDays(1).Date;

            var datas = await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.AssessDate <= endDate
              && m.TempSaveMark != "T"
              && m.DeleteFlag != "*").Select(m => new
              {
                  m.ID,
                  m.AssessDate,
                  m.AssessTime
              }).ToListAsync();

            if (datas.Count == 0)
            {
                return "";
            }

            return datas.Where(m => m.AssessDate.Date.Add(m.AssessTime) <= dateTime).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                .Select(m => m.ID)
                .FirstOrDefault();
        }

        /// <summary>
        /// 根据ID获取RecordsCode
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<string> GetRecordsCodeByID(string mainID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.ID == mainID)
                .Select(m => m.RecordsCode).FirstOrDefaultAsync();
        }

        public async Task<string> GetMainID(string inpatientID, int numberOfAssess)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.NumberOfAssessment == numberOfAssess && m.DeleteFlag != "*")
                .Select(m => m.ID).FirstOrDefaultAsync();
        }

        public async Task<List<KeyValueString>> GetAssessRecordCode(List<string> IDs)
        {
            var data = await _medicalDbContext.AssessMains.Where(m => IDs.Contains(m.ID)
                       && m.TempSaveMark.Trim() != "T" && m.DeleteFlag != "*").Select(m => new KeyValueString
                       {
                           Key = m.ID,
                           Value = m.RecordsCode
                       }).ToListAsync();
            return data;
        }
        /// <summary>
        /// 获取病人第一次评估记录
        /// </summary>
        /// <param name="inpatientID">病人ID</param>
        /// <param name="numberOfAssess">评估次数</param>
        /// <returns></returns>
        public async Task<PatientAssessMainInfo> GetAssessRecordByNumberOfAssessAsync(string inpatientID, int numberOfAssess)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID
                    && m.NumberOfAssessment == numberOfAssess && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获得病人某时间点之后的评估记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessMainInfo>> GetAssessMainByDatetimeAsync(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && (
               (m.AssessDate == dateTime.Date && m.AssessTime > dateTime.TimeOfDay) || m.AssessDate > dateTime.Date
           ) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获得病人某时间点之后的评估记录模版
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<List<string>> GetAssessRecordsCodesByDatetimeAsync(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && (
               (m.AssessDate == dateTime.Date && m.AssessTime > dateTime.TimeOfDay) || m.AssessDate > dateTime.Date
           ) && m.DeleteFlag != "*").Select(m => m.RecordsCode).Distinct().ToListAsync();
        }

        /// <summary>
        /// 获得病人某时间点之后的第一次评估记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<PatientAssessMainInfo> GetFirstAssessMainAfterDateAsync(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && (
                (m.AssessDate == dateTime.Date && m.AssessTime > dateTime.TimeOfDay) || m.AssessDate > dateTime.Date
            ) && m.DeleteFlag != "*").OrderBy(m => m.AssessDate).OrderBy(m => m.AssessTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获得病人某时间点之后的评估记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<int> GetAssessMainByDatetimeLimitAsync(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").
                Select(m => new PatientAssessMain
                {
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime
                })
                .ToListAsync();
            if (list.Count == 0)
            {
                return 0;
            }
            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).Count();
        }
        /// <summary>
        /// 获取病人入院护理评估的ID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<string> GetAdmitAssessIDByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.NumberOfAssessment == 1)
                .Select(m => m.ID).FirstOrDefaultAsync();
        }
        public async Task<DateTime?> GetLastAssessTimeByInpatientID(string inpatientID)
        {
            var query = await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();

            if (query.Count == 0)
            {
                return null;
            }
            return query.OrderBy(m => m.AssessDate.Date.Add(m.AssessTime)).LastOrDefault().AssessDate.Date.Add(query.LastOrDefault().AssessTime);

        }
        /// <summary>
        /// 获取患者首次评估数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailView>> GetPatientFirstAssessDetail(string inpatientID)
        {

            var mainInfos = await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            var mainInfo = mainInfos.OrderBy(m => m.AssessDate.Add(m.AssessTime)).FirstOrDefault();
            return await _medicalDbContext.AssessDetails.Where(m => m.PatientAssessMainID == mainInfo.ID)
                .Select(m => new PatientAssessDetailView
                {
                    AssessDate = mainInfo.AssessDate,
                    AssessTime = mainInfo.AssessTime,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                })
                .ToListAsync();
        }
        /// <summary>
        /// 获取患者评估病区
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetAssessStationIDs(string inpatientID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.Additional == false && m.DeleteFlag != "*" && m.TempSaveMark == "S")
                  .Select(m => m.StationID).ToListAsync();
        }
        /// <summary>
        /// 获取最后一次评估记录(包括补录)
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<PatientAssessMainInfo> GetAllDataLastAsync(string inpatientID)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.TempSaveMark.Trim() != "T" && m.DeleteFlag != "*")
                  .OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime)
                  .Select(m => new PatientAssessMainInfo
                  {
                      ID = m.ID,
                      InpatientID = m.InpatientID,
                      AssessDate = m.AssessDate,
                      AssessTime = m.AssessTime,
                  })
                  .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取患者首次评估数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientAssessDetailView>> GetPatientFirstAssessDetailAsync(string inpatientID)
        {
            return await (from m in _medicalDbContext.AssessMains
                          join n in _medicalDbContext.AssessDetails
                          on m.ID
                          equals n.PatientAssessMainID
                          where m.InpatientID == inpatientID && m.NumberOfAssessment == 1 && m.DeleteFlag != "*"
                          select new PatientAssessDetailView
                          {
                              AssessDate = m.AssessDate,
                              AssessTime = m.AssessTime,
                              AssessListID = n.AssessListID,
                              AssessValue = n.AssessValue
                          }).ToListAsync();
        }
        /// <summary>
        /// 获得病人某时间点之后的第一次评估记录ID
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<string> GetFirstAssessMainIDAfterDateAsync(string inpatientID, DateTime dateTime)
        {
            return await _medicalDbContext.AssessMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                && ((m.AssessDate == dateTime.Date && m.AssessTime > dateTime.TimeOfDay) || m.AssessDate > dateTime.Date)
                ).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).Select(m => m.ID).FirstOrDefaultAsync();
        }
    }
}
