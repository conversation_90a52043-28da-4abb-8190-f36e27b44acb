﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DynamicTableListRepository : IDynamicTableListRepository
    {
        private MedicalDbContext _medicalDbContext;
        public DynamicTableListRepository(
             MedicalDbContext medicalDbContext
            )
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据表格分类和表格子类获取数据
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        public async Task<DynamicTableListInfo> GetDataByType(string tableType, string tableSubType)
        {
            return await _medicalDbContext.DynamicTableListInfos.FirstOrDefaultAsync(m => m.TableType == tableType && m.TableSubType == tableSubType && m.DeleteFlag != "*");
        }
    }
}
