﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientAttachedInterventionRepository : IPatientAttachedInterventionRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientAttachedInterventionRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        public async Task<PatientAttachedInterventionInfo> GetByPatientAttachedInterventionID(string patientAttachedInterventionID)
        {
            return await _dbContext.PatientAttachedInterventions.Where(m => m.PatientAttachedInterventionID == patientAttachedInterventionID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}