﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class TubeToBodyPartRepository : ITubeToBodyPartRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public TubeToBodyPartRepository(MedicalDbContext db, IMemoryCache memoryCache, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public Task<int> GetMaxID()
        {
            var ID = _medicalDbContext.TubeToBodyParts.MaxAsync(m => m.ID);
            return ID;
        }

        public async Task<List<TubeToBodyPartInfo>> GetAsync(int tubeID)
        {
            var datas = await GetCacheAsync();
            if (datas != null)
            {
                return (datas as List<TubeToBodyPartInfo>).Where(t => t.TubeListID == tubeID).ToList();
            }
            return new List<TubeToBodyPartInfo>();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<TubeToBodyPartInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.TubeToBodyParts.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.TubeToBodyPart.ToString();
        }
    }
}