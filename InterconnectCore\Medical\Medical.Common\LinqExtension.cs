﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Medical.Common
{
    public static class LinqExtension
    {
        /// <summary>
        /// 扩展DistinctBy方法
        /// 举例 var query = people.DistinctBy(p => new { p.Id, p.Name });
        /// 举例 var query = people.DistinctBy(p => p.Id);
        /// </summary>
        /// <typeparam name="TSource"></typeparam>
        /// <typeparam name="TKey"></typeparam>
        /// <param name="source"></param>
        /// <param name="keySelector"></param>
        /// <returns></returns>
        //public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
        //{
        //    HashSet<TKey> seenKeys = new HashSet<TKey>();
        //    foreach (TSource element in source)
        //    {
        //        if (seenKeys.Add(keySelector(element)))
        //        {
        //            yield return element;
        //        }
        //    }
        //}

        /// <summary>
        /// Linq-Where扩展，第一个参数为True时，才会拼接第二个条件
        /// Example：list.Where(m => !string.IsNullOrEmpty(AssessListGroupID), m => m.AssessListGroupID == assessListGroupID)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="condition">拼接条件</param>
        /// <param name="predicate">查询条件</param>
        /// <returns></returns>
        public static IEnumerable<T> Where<T>(this IEnumerable<T> query,
            bool condition, Func<T, bool> predicate)
        {
            return condition ? Enumerable.Where(query, predicate) : query;
        }
        public static IQueryable<T> Where<T>(this IQueryable<T> query,
            bool condition, Expression<Func<T, bool>> predicate)
        {
            return condition ? query.Where(predicate) : query;
        }
        /// <summary>
        /// 按给定参数拆分集合
        /// </summary>
        /// <example>Enumerable.Range(0,100).Split(3).ToList()</example>
        /// <typeparam name="T"></typeparam>
        /// <param name="enumerable">给定集合</param>
        /// <param name="groupSize">拆分后每个集合的元素个数</param>
        /// <returns></returns>
        /// <remarks>待升级到.net6后，应废弃，改为使用Chunk方法</remarks>
        /// <see cref="https://stackoverflow.com/questions/419019/split-list-into-sublists-with-linq"/>
        public static IEnumerable<IEnumerable<T>> Split<T>(this IEnumerable<T> enumerable, int groupSize)
        {
            // The list to return. 
            List<T> list = new List<T>(groupSize);

            // Cycle through all of the items.
            foreach (T item in enumerable)
            {
                // Add the item.
                list.Add(item);

                // If the list has the number of elements, return that.
                if (list.Count == groupSize)
                {
                    // Return the list.
                    yield return list;

                    // Set the list to a new list.
                    list = new List<T>(groupSize);
                }
            }

            // Return the remainder if there is any,
            if (list.Count != 0)
            {
                // Return the list.
                yield return list;
            }
        }
    }
}
