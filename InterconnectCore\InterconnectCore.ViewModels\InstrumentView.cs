﻿namespace InterconnectCore.ViewModels
{
    public class InstrumentView
    {
        /// <summary>
        /// 病人ID
        /// </summary>
        public string pid { get; set; }
        /// <summary>
        /// 访问ID
        /// </summary>
        public string vid { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public int? gender { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int? age { get; set; }
        /// <summary>
        /// 出生日期
        /// </summary>
        public string dob { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string bed { get; set; }
        /// <summary>
        /// 房间号
        /// </summary>
        public string room { get; set; }
        /// <summary>
        /// 科室
        /// </summary>
        public string department { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        public string facility { get; set; }
        /// <summary>
        /// 身高
        /// </summary>
        public int? height { get; set; }
        /// <summary>
        /// 体重
        /// </summary>
        public int? weight { get; set; }
        /// <summary>
        /// 远端内部系统所使用的设备ID
        /// </summary>
        public string deviceid { get; set; }
    }
}
