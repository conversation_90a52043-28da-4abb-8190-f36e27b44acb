﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientBabyFeedingCareDetailRepository : IPatientBabyFeedingCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientBabyFeedingCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        #region
        /// <summary>
        /// 根据婴儿喂养主记录获取照护明细
        /// </summary>
        /// <param name="mainID">婴儿喂养主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareDetailInfo>> GetByMainID(string mainID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareDetailInfos.Where(t => t.BabyFeedingCareMainID == mainID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据患者住院ID获取明细
        /// </summary>
        /// <param name="inpaitentID">患者住院ID</param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareDetailInfo>> GetByInpatientID(string inpaitentID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareDetailInfos.Where(t => t.InpatientID == inpaitentID && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取部分字段值
        /// </summary>
        /// <param name="inpaitentID"></param>
        /// <returns></returns>
        public async Task<List<PatientBabyFeedingCareDetailInfo>> GetSomeDataByInpatientID(string inpaitentID)
        {
            return await _medicalDbContext.PatientBabyFeedingCareDetailInfos.Where(t => t.InpatientID == inpaitentID && t.DeleteFlag != "*").Select(m => new PatientBabyFeedingCareDetailInfo
            {
                BabyFeedingCareMainID = m.BabyFeedingCareMainID,
                AssessListID = m.AssessListID,
                AssessValue = m.AssessValue,
            }).ToListAsync();
        }
        #endregion
    }
}
