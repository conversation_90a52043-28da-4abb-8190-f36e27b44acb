﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data
{
    public class PatientDiagnosisRepository : IPatientDiagnosisRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientDiagnosisRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public Task<List<PatientDiagnosisInfo>> GetAsync(string inPatientID)
        {
            return _dbContext.PatientDiagnosisInfos.Where(t => t.InpatientID == inPatientID && t.DeleteFlag != "*").ToListAsync();
        }

        public Task<PatientDiagnosisInfo> GetAsyncByPatientICDCode(string inPatientID, string iCDCode)
        {
            return _dbContext.PatientDiagnosisInfos.Where(t => t.InpatientID == inPatientID && t.ICDCode == iCDCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public Task<List<PatientDiagnosisInfo>> GetAsync(DateTime startTime, DateTime endTime)
        {
            return _dbContext.PatientDiagnosisInfos.Where(t => t.OutTime >= startTime
            && t.OutTime <= endTime && t.DeleteFlag != "*").ToListAsync();
        }

        public Task<List<PatientDiagnosisInfo>> GetAsync(string[] inPatientID, bool mainFlag = false)
        {
            var query = _dbContext.PatientDiagnosisInfos.Where(t => inPatientID.Contains(t.InpatientID) && t.DeleteFlag != "*");
            if (mainFlag)
            {
                query = query.Where(t => t.MainFlag == "*");
            }
            return query.ToListAsync();
        }

        public async Task<bool> SaveAsync(List<PatientDiagnosisInfo> models)
        {
            _dbContext.PatientDiagnosisInfos.AddRange(models);
            return await _dbContext.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 获取患者诊断数据
        /// </summary>
        /// <param name="caseNumbers">住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientDiagnosisInfo>> GetPatientDiagnosisByCaseNumber(List<string> caseNumbers)
        {
            return await _dbContext.PatientDiagnosisInfos.Where(t => caseNumbers.Contains(t.CaseNumber) && t.DeleteFlag != "*").ToListAsync();
        }
    }
}