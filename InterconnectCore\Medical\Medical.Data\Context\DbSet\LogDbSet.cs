﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        public DbSet<SynchronizeLogInfo> SynchronizeLogInfos { get; set; }
        public DbSet<ScanErrorLogInfo> ScanErrorLogInfos { get; set; }
        /// <summary>
        /// 排程延迟执行日志
        /// </summary>
        public DbSet<DelayPerformLogInfo> DelayPerformLogInfos { get; set; }
        /// <summary>
        /// 检验闭环扫描日志
        /// </summary>
        public DbSet<ClosingControlLogsInfo> ClosingControlLogsInfos { get; set; }

        public DbSet<OperationLogInfo> OperationLogInfos { get; set; }
    }

}
