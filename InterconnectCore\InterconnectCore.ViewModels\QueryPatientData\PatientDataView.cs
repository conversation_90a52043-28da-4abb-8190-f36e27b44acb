﻿using Newtonsoft.Json;

namespace InterconnectCore.ViewModels.QueryPatientData
{
    /// <summary>
    /// 
    /// </summary>
    public class PatientDataView
    {
        /// <summary>
        /// 死亡事件ID
        /// </summary>
        [JsonProperty("ID")]
        public string Id { get; set; }
        /// <summary>
        /// 就诊记录类型代码
        /// </summary>
        [JsonProperty("ActivityTypeCode")]
        public string ActivityTypeCode { get; set; }
        /// <summary>
        /// 就诊记录类型名称
        /// </summary>
        [JsonProperty("ActivityTypeName")]
        public string ActivityTypeName { get; set; }
        /// <summary>
        /// 机构内患者基本信息唯一标识,按照对方格式
        /// </summary>
        [JsonProperty("PatientID")]
        public string PatientID { get; set; }

        /// <summary>
        /// 患者基本信息ID，机构内患者基本信息唯一标识(ChartNO)
        /// </summary>
        [JsonProperty("ChartNO")]
        public string ChartNo { get; set; }
        /// <summary>
        /// 就诊流水号，本次住院唯一号
        /// </summary>
        [JsonProperty("CaseNumber")]
        public string CaseNumber { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        [JsonProperty("PatientName")]
        public string PatientName { get; set; }
        /// <summary>
        /// 身份证件类别代码，固定：01
        /// </summary>
        [JsonProperty("IDCardTypeCode")]
        public string IdCardTypeCode { get; set; }

        /// <summary>
        /// 身份证件类别名称，固定：居民身份证
        /// </summary>
        [JsonProperty("IDCardTypeName")]
        public string IdCardTypeName { get; set; }

        /// <summary>
        /// 身份证件号码
        /// </summary>
        [JsonProperty("IDCard")]
        public string IdCard { get; set; }
        /// <summary>
        /// 死亡记录日期时间，timestamp
        /// </summary>
        [JsonProperty("CreateDate")]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 是否使用呼吸机代码，0否 1是 varchar(2)
        /// </summary>
        [JsonProperty("VentilatorUsedCode")]
        public string VentilatorUsedCode { get; set; }

        /// <summary>
        /// 是否使用呼吸机名称，varchar(2)
        /// </summary>
        [JsonProperty("VentilatorUsedName")]
        public string VentilatorUsedName { get; set; }
        /// <summary>
        /// 是否重症监护代码，0否 1是 varchar(2)
        /// </summary>
        [JsonProperty("CriticalCareCode")]
        public string CriticalCareCode { get; set; }
        /// <summary>
        /// 是否重症监护代码，varchar(2)
        /// </summary>
        [JsonProperty("CriticalCareName")]
        public string CriticalCareName { get; set; }   
        /// <summary>
        /// 医疗机构代码，固定：350206013
        /// </summary>
        [JsonProperty("ORGCode")]
        public string OrgCode { get; set; }

        /// <summary>
        /// 医疗机构名称，varchar(100)
        /// </summary>
        [JsonProperty("ORGName")]
        public string OrgName { get; set; }
        /// <summary>
        /// 科室代码，varchar(20)
        /// </summary>
        [JsonProperty("DeptCode")]
        public string DeptCode { get; set; }

        /// <summary>
        /// 科室名称，varchar(50)
        /// </summary>
        [JsonProperty("DeptName")]
        public string DeptName { get; set; }

        /// <summary>
        /// 操作时间，查询数据的时间
        /// </summary>
        [JsonProperty("QueryDateTime")]
        public DateTime QueryDateTime { get; set; }
      
    }
}
