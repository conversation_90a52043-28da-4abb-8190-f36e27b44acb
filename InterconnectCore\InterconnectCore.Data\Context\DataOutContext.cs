﻿using InterconnectCore.Models;
using Microsoft.EntityFrameworkCore;

namespace InterconnectCore.Data.Context
{
    public partial class DataOutContext : DbContext
    {
        public DataOutContext(DbContextOptions<DataOutContext> options)
           : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<SettingDescriptionInfo>().<PERSON><PERSON><PERSON>(t => new { t.SettingType });
            builder.Entity<LogInfo>().<PERSON><PERSON><PERSON>(t => new { t.Guid });
            base.OnModelCreating(builder);
        }
        public DbSet<SettingDescriptionInfo> SettingDescriptionInfos { get; set; }
        public DbSet<PatientBasicInfo> PatientBasicInfos { get; set; }
        /// <summary>
        ///  日志记录
        /// </summary>
        public DbSet<LogInfo> LogInfos { get; set; }
        public DbSet<SyncLogInfo> SyncLogInfos { get; set; }

    }
}
