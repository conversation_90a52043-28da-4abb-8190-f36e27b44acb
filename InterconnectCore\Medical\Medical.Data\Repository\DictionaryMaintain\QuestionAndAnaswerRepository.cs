﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QuestionAndAnaswerRepository : IQuestionAndAnaswerRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public QuestionAndAnaswerRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        //查询常见问题表所有信息
        public async Task<List<QuestionAndAnaswerInfo>> GetQuestionAndAnaswers()
        {
            return await _medicalDbContext.QuestionAndAnaswers.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        //通过ID获取指定的错误信息记录
        public async Task<QuestionAndAnaswerInfo> GetQuestionAndAnaswerInfoById(int QuestionAndAnaswerID)
        {
            return await _medicalDbContext.QuestionAndAnaswers.Where(m => m.DeleteFlag != "*" && m.QuestionAndAnaswerID == QuestionAndAnaswerID).SingleOrDefaultAsync();
        }
    }
}
