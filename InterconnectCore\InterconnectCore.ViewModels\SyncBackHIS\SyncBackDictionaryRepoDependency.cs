﻿using Medical.Data.Interface;

namespace InterconnectCore.ViewModels
{
    public class SyncBackDictionaryRepoDependencyService
    {
        public Lazy<IHospitalListRepository> HospitalListRepo { get; }
        public Lazy<IEmployeelDataRepository> EmployeeRepo { get; }
        public Lazy<IPatientBasicDataRepository> PatientBasicRepo { get; }
        public Lazy<IInpatientDataRepository> InpatientDataRepo { get; }
        public Lazy<IDepartmentListRepository> DeptRepo { get; }

        public SyncBackDictionaryRepoDependencyService(
            Lazy<IHospitalListRepository> hospitalListRepo,
            Lazy<IEmployeelDataRepository> employeeRepo,
            Lazy<IPatientBasicDataRepository> patientBasicRepo,
            Lazy<IInpatientDataRepository> inpatientDataRepo,
            Lazy<IDepartmentListRepository> deptRepo)
        {
            HospitalListRepo = hospitalListRepo;
            EmployeeRepo = employeeRepo;
            PatientBasicRepo = patientBasicRepo;
            InpatientDataRepo = inpatientDataRepo;
            DeptRepo = deptRepo;
        }
    }

}
