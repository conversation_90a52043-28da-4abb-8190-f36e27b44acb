﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class QCDictionaryRepository : IQCDictionaryRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;


        public QCDictionaryRepository(
            MedicalDbContext db,
            IMemoryCache memoryCache,
            SessionCommonServer sessionCommonServer,
            GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 查询指定范围的记录
        /// </summary>
        /// <param name="applyDomain">适用范围</param>
        /// <returns>指定适用范围的字典档集合</returns>
        public async Task<List<QCCheckDictionaryMainInfo>> GetByApplyDomain(string applyDomain)
        {
            var list = await this.GetAllAsync<QCCheckDictionaryMainInfo>();
            return list.Where(m => m.ApplyDomain == applyDomain).ToList();
        }

        /// <summary>
        /// 根据id和医疗院id查找指定的质控字典档
        /// </summary>
        /// <param name="QCDictionaryID">质控字典档ID</param>
        /// <returns>返回查询的指定质控字典档记录</returns>
        public async Task<QCCheckDictionaryMainInfo> GetQCDictionary(int QCDictionaryID)
        {
            var list = await this.GetAllAsync<QCCheckDictionaryMainInfo>();
            return list.Where(m => m.QCCheckDictionaryMainID == QCDictionaryID).SingleOrDefault();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<QCCheckDictionaryMainInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.QCDictionarys.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.QCDictionary.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 获取一个新的主键
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetNewID()
        {
            var datas = await this.GetAllAsync<QCCheckDictionaryMainInfo>();
            var index = datas.Max(m => m.QCCheckDictionaryMainID);
            return index + 1;
        }
    }
}
