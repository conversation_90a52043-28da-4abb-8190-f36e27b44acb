﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class OrganizationDepartmentRepository : IOrganizationDepartmentRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;


        public OrganizationDepartmentRepository(MedicalDbContext db
             , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<OrganizationDepartmentInfo> GetByDepartmentIDAsync(string departmentID)
        {
            var results = await GetCacheAsync() as List<OrganizationDepartmentInfo>;
            return results.Where(m => m.DepartmentID == departmentID).FirstOrDefault();
        }

        public async Task<List<OrganizationDepartmentInfo>> GetByDepartmentLevelIDAsync(int departmentLevelID)
        {
            var results = await GetCacheAsync() as List<OrganizationDepartmentInfo>;
            return results.Where(m => m.DepartmentLevelID == departmentLevelID && m.DeleteFlag != "*").ToList();
        }
        /// <summary>
        /// 根据部门ID数组获取数据
        /// </summary>
        /// <param name="departmentIDArr"></param>
        /// <returns></returns>
        public async Task<List<OrganizationDepartmentInfo>> GetDataByDepartmentIDArr(List<string> departmentIDArr)
        {
            var results = await GetCacheAsync() as List<OrganizationDepartmentInfo>;
            return results.Where(m => departmentIDArr.Contains(m.DepartmentID) && m.DeleteFlag != "*").ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<OrganizationDepartmentInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            var data = await _medicalDbContext.OrganizationDepartmentInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
            return data;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.OrganizationDepartment.GetKey(_sessionCommonServer);
        }

        public async Task<List<OrganizationDepartmentInfo>> GetAllAsync()
        {
            return await GetCacheAsync() as List<OrganizationDepartmentInfo>;
        }
    }
}