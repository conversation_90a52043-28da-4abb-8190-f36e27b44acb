﻿
using InterconnectCore.Data.Context;
using InterconnectCore.Data.Interface;
using InterconnectCore.Models;

namespace InterconnectCore.Data.Repository
{
    public class SettingDescRepository : ISettingDescRepository
    {
        private DataOutContext _DataOutConnection = null;

        public SettingDescRepository(DataOutContext db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 获取所有没有抽取的数据
        /// </summary>
        /// <returns></returns>
        public List<SettingDescriptionInfo> GetAsync(int settingType, string typeCode)
        {
            return _DataOutConnection.SettingDescriptionInfos.Where
                (m => m.SettingType == settingType && m.TypeCode == typeCode && m.DeleteFlag != "*").ToList();
        }
    }
}
