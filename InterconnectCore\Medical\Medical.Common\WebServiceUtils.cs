﻿using NLog;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Xml;

namespace Medical.Common
{
    public class WebServiceUtils
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public static string GetResult(string requestUrl, string requestBody)
        {
            if (string.IsNullOrEmpty(requestUrl))
            {
                return "";
            }
            if (string.IsNullOrEmpty(requestBody))
            {
                return "";
            }

            byte[] dataArray = Encoding.UTF8.GetBytes(requestBody);

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(requestUrl);
            request.Method = "POST";
            request.ContentType = "application/xml";
            request.ContentLength = dataArray.Length;

            Stream dataStream = null;
            try
            {
                dataStream = request.GetRequestStream();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil请求异常" + ex.ToString());
                return "";
            }
            dataStream.Write(dataArray, 0, dataArray.Length);
            dataStream.Close();

            string result = "";
            try
            {
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                result = reader.ReadToEnd();
                reader.Close();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil响应异常" + ex.ToString());
                return "";
            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="url">请求路径</param>
        /// <param name="xmlStr">请求体</param>
        /// <returns></returns>
        public static XmlDocument GetXmlResult(string url, string xmlStr, string soapAction = null)
        {
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error("WebServiceUtil.GetXmlResult方法的参数url为空");
                return null;
            }

            byte[] dataArray = Encoding.UTF8.GetBytes(xmlStr);

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.ContentLength = dataArray.Length;
            if (!string.IsNullOrEmpty(soapAction))
            {
                request.Headers["SOAPAction"] = soapAction;
            }
            Stream dataStream = null;
            try
            {
                dataStream = request.GetRequestStream();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil请求异常" + ex.ToString());
                return null;
            }

            dataStream.Write(dataArray, 0, dataArray.Length);
            dataStream.Close();

            string res = "";
            try
            {
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                res = reader.ReadToEnd();
                reader.Close();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil响应异常" + ex.ToString());
                return null;
            }
            XmlDocument doc = new XmlDocument();
            XmlDocument docResult = new XmlDocument();
            try
            {
                doc.LoadXml(res);
                if (string.IsNullOrEmpty(doc.InnerText))
                {
                    _logger.Error("WebServiceUtil响应内容为空");
                    return null;
                }
                docResult.LoadXml(doc.InnerText.Trim());
                if (string.IsNullOrEmpty(docResult.InnerXml))
                {
                    _logger.Error("WebServiceUtil响应内容中数据为空");
                    return null;
                }
            }
            catch (Exception)
            {
                _logger.Error("WebServiceUtil响应内容xml格式异常");
                return null;
            }

            return docResult;
        }
    }
}