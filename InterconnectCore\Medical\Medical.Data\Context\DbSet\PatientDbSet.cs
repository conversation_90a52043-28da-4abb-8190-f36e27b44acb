﻿using Medical.Models;
using Medical.Models.Interconnect;
using Medical.Models.Patient;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext
    {
        /// <summary>
        /// 病人护理问题数据集
        /// </summary>
        public DbSet<PatientProblemInfo> PatientProblems { get; set; }
        /// <summary>
        /// 病人护理措施数据集
        /// </summary>
        public DbSet<PatientInterventionInfo> PatientInterventions { get; set; }
        /// <summary>
        /// 护理排程数据集
        /// </summary>
        public DbSet<PatientScheduleMainInfo> PatientScheduleMain { get; set; }
        /// <summary>
        /// 护理排程明细数据集
        /// </summary>
        public DbSet<PatientScheduleDetailInfo> PatientScheduleDetail { get; set; }
        /// <summary>
        /// 病人医嘱主数据集
        /// </summary>
        public DbSet<PatientOrderMainInfo> PatientOrderMains { get; set; }
        /// <summary>
        /// 病人医嘱名细数据集
        /// </summary>
        public DbSet<PatientOrderDetailInfo> PatientOrderDetails { get; set; }
        /// <summary>
        /// 病人医护理目标数据集
        /// </summary>
        public DbSet<PatientGoalInfo> PatientGoals { get; set; }
        /// <summary>
        /// 病人先关因素
        /// </summary>
        public DbSet<PatientRelatedFactorInfo> PatientRelatedFactors { get; set; }
        /// <summary>
        /// 病人数据集
        /// </summary>
        public DbSet<PatientBasicDataInfo> PatientBasicDatas { get; set; }

        /// <summary>
        /// 交接班记录
        /// </summary>
        public DbSet<HandoverInfo> HandoverInfos { get; set; }

        /// <summary>
        /// 交接班人体图记录
        /// </summary>
        public DbSet<PatientHandoverImageInfo> PatientHandoverImageInfos { get; set; }

        /// <summary>
        /// 交接班记录档
        /// </summary>
        public DbSet<PatientHandoverContentsInfo> PatientHandoverContentsInfos { get; set; }

        /// <summary>
        /// 交班记录明细
        /// </summary>
        public DbSet<HandoverDetailInfo> HandoverDetailInfos { get; set; }

        /// <summary>
        /// 伤口图片
        /// </summary>
        public DbSet<PatientSpecialListImageInfo> PatientSpecialListImageInfos { get; set; }
        /// <summary>
        /// 胰岛素注射记录
        /// </summary>
        public DbSet<InsulinRecordInfo> InsulinRecordInfos { get; set; }
        /// <summary>
        /// 病人出入量
        /// </summary>
        public DbSet<PatientIntakeOutputInfo> PatientIntakeOutputs { get; set; }
        /// <summary>
        /// 病人护理实际目标
        /// </summary>
        public DbSet<PatientEvaluationInfo> PatientEvaluations { get; set; }
        /// <summary>
        /// 病人护理记录
        /// </summary>
        public DbSet<PatientNursingRecordInfo> PatientNursingRecordInfos { get; set; }
        /// <summary>
        /// 病人检验结果
        /// </summary>
        public DbSet<PatientTestResultInfo> PatientTestResultInfos { get; set; }
        /// <summary>
        /// 病人附加措施数据集
        /// </summary>
        public DbSet<PatientAttachedInterventionInfo> PatientAttachedInterventions { get; set; }
        /// <summary>
        /// 评估记录历史
        /// </summary>
        public DbSet<PatientAssessMainHistoryInfo> PatientAssessMainHistoryInfos { get; set; }
        /// <summary>
        /// 病人在院信息异动日志
        /// </summary>
        public DbSet<InpatientLogInfo> InpatientLogInfos { get; set; }
        /// <summary>
        /// 病人抢救用药记录
        /// </summary>
        public DbSet<PatientRescueMedicationInfo> PatientRescueMedicationInfos { get; set; }
        /// <summary>
        /// 病人给药排程
        /// </summary>
        public DbSet<PatientMedicineScheduleInfo> PatientMedicineScheduleInfos { get; set; }
        /// <summary>
        /// 病人观察措施
        /// </summary>
        public DbSet<PatientScheduleMeasuresInfo> PatientScheduleMeasuresInfos { get; set; }
        /// <summary>
        /// 病人观察措施明细
        /// </summary>
        public DbSet<PatientScheduleMeasuresDetailInfo> PatientScheduleMeasuresDetailInfos { get; set; }
        /// <summary>
        /// 患者事件
        /// </summary>
        public DbSet<PatientEventInfo> PatientEventInfos { get; set; }
        /// <summary>
        /// 患者事件明细表
        /// </summary>
        public DbSet<PatientEventDetailInfo> PatientEventDetailInfos { get; set; }
        /// <summary>
        /// 手术记录
        /// </summary>
        public DbSet<PatientOperationInfo> PatientOperationInfos { get; set; }
        /// <summary>
        /// 住院病人出院诊断
        /// </summary>
        public DbSet<PatientDiagnosisInfo> PatientDiagnosisInfos { get; set; }
        //2019-9-21
        /// <summary>
        /// 病人排程Json
        /// </summary>
        public DbSet<PatientScheduleJsonInfo> PatientScheduleJsonInfos { get; set; }
        /// <summary>
        /// 闭环检验标签数据表
        /// </summary>
        //public DbSet<TestClosingControlInfo> TestClosingControls { get; set; }
        /// <summary>
        /// 闭环检验字点表
        /// </summary>
        public DbSet<ClosingControlStatusInfo> ClosingControlStatusInfos { get; set; }
        /// <summary>
        /// 闭环检验日志
        /// </summary>
        public DbSet<ClosingControlLogInfo> ClosingControlLogInfoInfos { get; set; }
        /// <summary>
        /// 会诊统计
        /// </summary>
        public DbSet<PatientConsultInfo> PatientConsultInfos { get; set; }
        /// <summary>
        /// 伤口记录表
        /// </summary>
        public DbSet<PatientWoundRecordInfo> PatientWoundRecordInfos { get; set; }
        /// <summary>
        /// 伤口评估主表
        /// </summary>
        public DbSet<PatientWoundCareMainInfo> PatientWoundCareMainInfos { get; set; }
        /// <summary>
        /// 伤口评估明细表
        /// </summary>
        public DbSet<PatientWoundCareDetailInfo> PatientWoundCareDetailInfos { get; set; }

        /// <summary>
        /// 导管记录表
        /// </summary>
        public DbSet<PatientTubeRecordInfo> PatientTubeRecordInfos { get; set; }
        /// <summary>
        /// 导管评估主表
        /// </summary>
        public DbSet<PatientTubeCareMainInfo> PatientTubeCareMainInfos { get; set; }
        /// <summary>
        /// 导管评估明细表
        /// </summary>
        public DbSet<PatientTubeCareDetailInfo> PatientTubeCareDetailInfos { get; set; }
        /// <summary>
        /// 抢救明细
        /// </summary>
        public DbSet<PatientRescueCareDetailInfo> PatientRescueCareDetailInfos { get; set; }
        /// <summary>
        /// 抢救主表
        /// </summary>
        public DbSet<PatientRescueCareMainInfo> PatientRescueCareMainInfos { get; set; }
        /// <summary>
        /// 抢救记录
        /// </summary>
        public DbSet<PatientRescueRecordInfo> PatientRescueRecordInfos { get; set; }
        /// <summary>
        /// 约束记录
        /// </summary>
        public DbSet<PatientRestraintRecordInfo> PatientRestraintRecordInfos { get; set; }
        /// <summary>
        /// 约束评估主表
        /// </summary>
        public DbSet<PatientRestraintCareMainInfo> PatientRestraintCareMainInfos { get; set; }
        /// <summary>
        /// 约束评估明细表
        /// </summary>
        public DbSet<PatientRestraintCareDetailInfo> PatientRestraintCareDetailInfos { get; set; }
        /// <summary>
        /// 疼痛照顾主表
        /// </summary>
        public DbSet<PatientPainCareMainInfo> PatientPainCareMainInfos { get; set; }
        /// <summary>
        /// 疼痛照顾明细表
        /// </summary>
        public DbSet<PatientPainCareDetailInfo> PatientPainCareDetailInfos { get; set; }
        /// <summary>
        /// 疼痛主表
        /// </summary>
        public DbSet<PatientPainRecordInfo> PatientPainRecordInfos { get; set; }
        /// <summary>
        /// Apache2主表
        /// </summary>
        public DbSet<Apache2Info> Apache2Infos { get; set; }
        /// <summary>
        /// 末梢血运记录表
        /// </summary>
        public DbSet<PatientPCRecordInfo> PatientPCRecordInfos { get; set; }
        /// <summary>
        /// 末梢血运评估表
        /// </summary>
        public DbSet<PatientPCCareMainInfo> PatientPCCareMainInfos { get; set; }
        /// <summary>
        /// 末梢血运评估明细表
        /// </summary>
        public DbSet<PatientPCCareDetailInfo> PatientPCCareDetailInfos { get; set; }
        /// <summary>
        /// 手术记录主表
        /// </summary>
        public DbSet<PatientOperationRecordMainInfo> PatientOperationRecordMainInfos { get; set; }
        /// <summary>
        /// 手术记录明细表
        /// </summary>
        public DbSet<PatientOperationRecordDetailInfo> PatientOperationRecordDetailInfos { get; set; }
        /// 造口记录表
        /// </summary>
        public DbSet<PatientStomaRecordInfo> PatientStomaRecordInfos { get; set; }
        /// <summary>
        /// 造口维护主表
        /// </summary>
        public DbSet<PatientStomaCareMainInfo> PatientStomaCareMainInfos { get; set; }
        /// <summary>
        /// 造口维护明细表
        /// </summary>
        public DbSet<PatientStomaCareDetailInfo> PatientStomaCareDetailInfos { get; set; }
        /// <summary>
        /// 观察措施模板表
        /// </summary>
        public DbSet<ObserveTemplateInfo> ObserveTemplateInfos { get; set; }

        /// <summary>
        /// 新生儿出生记录
        /// </summary>
        public DbSet<NewBornRecordInfo> NewBornRecordInfos { get; set; }

        /// <summary>
        /// 分娩记录
        /// </summary>
        public DbSet<PatientDeliveryRecordInfo> PatientDeliveryRecordInfos { get; set; }
        /// <summary>
        /// 分娩照护主记录
        /// </summary>
        public DbSet<PatientDeliveryCareMainInfo> PatientDeliveryCareMainInfos { get; set; }
        /// <summary>
        /// 分娩照护明细记录
        /// </summary>
        public DbSet<PatientDeliveryCareDetailInfo> PatientDeliveryCareDetailInfos { get; set; }

        /// <summary>
        /// 婴儿喂养记录
        /// </summary>
        public DbSet<PatientBabyFeedingRecordInfo> PatientBabyFeedingRecordInfos { get; set; }
        /// <summary>
        /// 婴儿喂养照护主记录
        /// </summary>
        public DbSet<PatientBabyFeedingCareMainInfo> PatientBabyFeedingCareMainInfos { get; set; }
        /// <summary>
        /// 婴儿喂养照护明细记录
        /// </summary>
        public DbSet<PatientBabyFeedingCareDetailInfo> PatientBabyFeedingCareDetailInfos { get; set; }
        /// <summary>
        /// 溶栓主档
        /// </summary>
        public DbSet<PatientThrombolysisRecordInfo> PatientThrombolysisRecordInfos { get; set; }

        /// <summary>
        /// 溶栓维护主表
        /// </summary>
        public DbSet<PatientThrombolysisCareMainInfo> PatientThrombolysisCareMainInfos { get; set; }

        /// <summary>
        /// 溶栓维护明细
        /// </summary>
        public DbSet<PatientThrombolysisCareDetailInfo> PatientThrombolysisCareDetailInfos { get; set; }
        /// <summary>
        /// 饮食记录维护主表
        /// </summary>
        public DbSet<PatientDietIntakeCareMainInfo> PatientDietIntakeCareMainInfos { get; set; }
        /// <summary>
        /// 饮食记录维护明细
        /// </summary>
        public DbSet<PatientDietIntakeCareDetailInfo> PatientDietIntakeCareDetailInfos { get; set; }
        /// <summary>
        /// 巡视记录
        /// </summary>
        public DbSet<PatientPatrolRecordInfo> PatrolRecordInfos { get; set; }
        /// <summary>
        /// 泵入
        /// </summary>
        public DbSet<PatientPumpingRecordInfo> PatientPumpingRecordInfos { get; set; }
        public DbSet<PatientPumpingCareMainInfo> PatientPumpingCareMainInfos { get; set; }
        public DbSet<PatientPumpingCareDetailInfo> PatientPumpingCareDetailInfos { get; set; }
        public DbSet<PumpingDrugListInfo> PumpingDrugListInfos { get; set; }
        /// <summary>
        /// 谵妄评估主表
        /// </summary>
        public DbSet<PatientDeliriumCareMainInfo> PatientDeliriumCareMainInfos { get; set; }
        /// <summary>
        /// 谵妄评估明细表
        /// </summary>
        public DbSet<PatientDeliriumCareDetailInfo> PatientDeliriumCareDetailInfos { get; set; }
        /// <summary>
        /// 检验闭环核对表
        /// </summary>
        public DbSet<PatientClosingControlInfo> PatientClosingControlInfos { get; set; }
        /// <summary>
        /// 皮试记录
        /// </summary>
        public DbSet<PatientSkinTestInfo> PatientSkinTestInfos { get; set; }
        /// <summary>
        /// 会诊评价明细表
        /// </summary>
        public DbSet<PatientConsultDetailInfo> PatientConsultDetailInfos { get; set; }
        /// <summary>
        /// 神经血管评估
        /// </summary>
        public DbSet<PatientNeurovascularCareMainInfo> PatientNeurovascularCareMainInfos { get; set; }
        public DbSet<PatientNeurovascularCareDetailInfo> PatientNeurovascularCareDetailInfos { get; set; }

        /// <summary>
        /// 评估记录主表
        /// </summary>
        public DbSet<PatientFormRecordInfo> PatientFormRecordInfos { get; set; }

        /// <summary>
        /// 评估记录明细表
        /// </summary>
        public DbSet<PatientFormDetailInfo> PatientFormDetailInfos { get; set; }
        /// <summary>
        /// 危急值
        /// </summary>
        public DbSet<PatientCriticalInfo> PatientCriticalInfos { get; set; }

        /// <summary>
        /// 血液净化评估主记录
        /// </summary>
        public DbSet<PatientCRRTRecordInfo> PatientCRRTRecordInfos { get; set; }
        /// <summary>
        /// 血液净化评估主表
        /// </summary>
        public DbSet<PatientCRRTCareMainInfo> PatientCRRTCareMainInfos { get; set; }
        /// <summary>
        /// 血液净化评估明细表
        /// </summary>
        public DbSet<PatientCRRTCareDetailInfo> PatientCRRTCareDetailInfos { get; set; }
        /// 出入量数据映射表
        /// </summary>
        public DbSet<PatientIntakeOutputMappingInfo> PatientIntakeOutputMappingInfos { get; set; }
        /// <summary>
        /// 动静脉内瘘评估主记录
        /// </summary>
        public DbSet<PatientArteriovenousFistulaRecordInfo> PatientArteriovenousFistulaRecordInfos { get; set; }
        /// <summary>
        /// 动静脉内瘘评估主表
        /// </summary>
        public DbSet<PatientArteriovenousFistulaCareMainInfo> PatientArteriovenousFistulaCareMainInfos { get; set; }
        /// <summary>
        /// 动静脉内瘘评估明细表
        /// </summary>
        public DbSet<PatientArteriovenousFistulaCareDetailInfo> PatientArteriovenousFistulaCareDetailInfos { get; set; }

        /// <summary>
        /// 产时维护记录副表,待产产程观察记录
        /// </summary>
        public DbSet<PatientPreDeliveryCareMainInfo> PatientPreDeliveryCareMainInfos { get; set; }
        /// <summary>
        /// 产时维护记录副表,产后记录
        /// </summary>
        public DbSet<PatientPostDeliveryCareMainInfo> PatientPostDeliveryCareMainInfos { get; set; }
        /// <summary>
        /// 产时维护记录副表,新生儿记录
        /// </summary>
        public DbSet<NewBornNursingRecordCareMainInfo> NewBornNursingRecordCareMainInfos { get; set; }
        /// <summary>
        /// 患者皮瓣护理记录
        /// </summary>
        public DbSet<PatientFlapRecordInfo> PatientFlapRecordInfos { get; set; }
        /// <summary>
        /// 患者皮瓣护理主表
        /// </summary>
        public DbSet<PatientFlapCareMainInfo> PatientFlapCareMainInfos { get; set; }
        /// <summary>
        /// 患者皮瓣护理明细表
        /// </summary>
        public DbSet<PatientFlapCareDetailInfo> PatientFlapCareDetailInfos { get; set; }
        /// <summary>
        /// 镇静照顾主表
        /// </summary>
        public DbSet<PatientSedationCareMainInfo> PatientSedationCareMainInfos { get; set; }
        /// <summary>
        /// 镇静照顾明细表
        /// </summary>
        public DbSet<PatientSedationCareDetailInfo> PatientSedationCareDetailInfos { get; set; }

        /// <summary>
        /// 无呕专项主表
        /// </summary>
        public DbSet<PatientCINVCareMainInfo> PatientCINVCareMainInfos { get; set; }
        /// <summary>
        /// 无呕专项明细表
        /// </summary>
        public DbSet<PatientCINVCareDetailInfo> PatientCINVCareDetailInfos { get; set; }
        /// <summary>
        /// 交班图示坐标表
        /// </summary>
        public DbSet<HandOverIconCoordinateInfo> HandOverIconCoordinateInfos { get; set; }
        /// <summary>
        /// 患者风险干预措施
        /// </summary>
        public DbSet<PatientScoreInterventionInfo> PatientScoreInterventionInfos { get; set; }
        /// <summary>
        /// 危重访视记录表
        /// </summary>
        public DbSet<PatientCriticallyVisitsRecordInfo> PatientCriticallyVisitsRecordInfos { get; set; }
    }
}