﻿/*
 * 2022-1-12 通过ScheduleMainID获取一条疼痛评估记录 En
 */
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPainCareMainRepository : IPatientPainCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPainCareMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        //通过careMainID获取指定记录
        public async Task<PatientPainCareMainInfo> GetByID(string careMainID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(t => t.PatientPainCareMainID == careMainID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        //通过patientPainRecordID获取记录
        public async Task<List<PatientPainCareMainInfo>> GetByRecordID(string patientPainRecordID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(t => t.PatientPainRecordID == patientPainRecordID && t.DeleteFlag != "*").ToListAsync();
        }

        //获取指定疼痛评估记录
        public async Task<PatientPainCareMainInfo> GetByRecordIDAndRecordCode(string patientPainRecordID, string recordsCode)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(t => t.PatientPainRecordID == patientPainRecordID && t.RecordsCode == recordsCode && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        //获取最后一次评估
        public async Task<PatientPainCareMainInfo> GetLastByTimeAsync(string recordID)
        {
            var list = await _medicalDbContext.PatientPainCareMainInfos.Where(t => t.PatientPainRecordID == recordID && t.DeleteFlag != "*")
                                .OrderByDescending(t => t.NumberOfAssessment).ToListAsync();
            if (list != null && list.Count > 0)
            {
                return list[0];
            }
            return null;
        }

        //获取首次评估记录
        public async Task<PatientPainCareMainInfo> GetByNumberOfAssessment(int numberOfAssessment, string patientRecordID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(t => t.NumberOfAssessment == numberOfAssessment
            && t.PatientPainRecordID == patientRecordID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        //获取ScheduleMainID对应的所有疼痛记录
        public async Task<List<PatientPainCareMainInfo>> GetByScheduleMainID(string inpatientID, string scheduleMainID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.PatientScheduleMainID == scheduleMainID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<string> GetCareMainIDByScheduleMainID(string inpatientID, string scheduleMainID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.PatientScheduleMainID == scheduleMainID && m.DeleteFlag != "*")
                        .OrderByDescending(m => m.AssessDate).OrderByDescending(m => m.AssessTime).Select(m => m.PatientPainCareMainID).FirstOrDefaultAsync();
        }

        public async Task<List<PatientPainCareMainInfo>> GetNoAssessMainIDData(string inpatientID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && (m.PatientAssessMainID == null || m.PatientAssessMainID == "") && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientPainView>> GetPatientPainCareIntervention(string inpatientID, int stationID, DateTime startTime, DateTime endTime)
        {
            var data = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                                  && m.BringToShift == true && m.DeleteFlag != "*"
                                  && m.AssessDate >= startTime.Date && m.AssessDate <= endTime.Date)
                .Select(m => new PatientPainView
                {
                    PatientPainCareMainID = m.PatientPainCareMainID,
                    AssessDate = m.AssessDate,
                    AssessTime = m.AssessTime,
                    ActiveStatus = m.ActiveStatus,
                    FASScore = m.FASScore,
                    AssessTool = m.AssessTool,
                    PainPoint = m.PainPoint,
                    BodyPartName = m.BodyPartName,
                    CareIntervention = m.CareIntervention
                }).ToListAsync();

            return data.Where(m => m.AssessDate.Add(m.AssessTime) >= startTime && m.AssessDate.Add(m.AssessTime) <= endTime).ToList();
        }

        public async Task<PatientPainCareMainInfo> GetLastByAssessMainID(string inpatientID, string assessMainID)
        {
            var result = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID
                            && m.PatientAssessMainID == assessMainID && m.DeleteFlag != "*")
                            .OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToListAsync();
            if (result.Count > 0)
            {
                return result.Last();
            }
            return null;
        }

        //透过评估序号取得内容
        public async Task<List<PatientPainCareMainInfo>> GetByAssessMainID(string inpatientID, string assessMainID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.PatientAssessMainID == assessMainID
           && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientPainCareMainInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID
                    && m.DeleteFlag != "*").ToListAsync();
        }
        //TODO,需要修复前端传递InpatientId,当做索引
        public async Task<List<PatientPainCareMainInfo>> GetRecordsBySourceID(string sourceID, string sourceType)
        {
            var data = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
            if (!string.IsNullOrEmpty(sourceType))
            {
                data = data.Where(m => !string.IsNullOrEmpty(m.SourceType) && m.SourceType.Trim() == sourceType.Trim()).ToList();
            }
            return data;
        }

        public async Task<PatientPainView> GetFirstByAssessMainID(string inpatientID, string assessMainID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.PatientAssessMainID == assessMainID
                        && m.DeleteFlag != "*").OrderBy(m => m.NumberOfAssessment)
                        .Select(m => new PatientPainView
                        {
                            PatientPainCareMainID = m.PatientPainCareMainID,
                            AssessDate = m.AssessDate,
                            AssessTime = m.AssessTime,
                            ActiveStatus = m.ActiveStatus,
                            FASScore = m.FASScore,
                            AssessTool = m.AssessTool,
                            PainPoint = m.PainPoint,
                            BodyPartName = m.BodyPartName,
                            CareIntervention = m.CareIntervention
                        }).FirstOrDefaultAsync();
        }

        public async Task<List<PatientPainCareMainInfo>> GetAllPainsByInpatientID(string inpaentID)
        {
            return await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpaentID &&
            m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<KeyValueString>> GetPainToolName(List<string> careMainIDs)
        {
            var data = await (from m in _medicalDbContext.PatientPainCareMainInfos.Where(m => careMainIDs.Contains(m.PatientPainCareMainID) && m.DeleteFlag != "*")
                              select new KeyValueString
                              {
                                  Key = m.PatientPainCareMainID,
                                  Value = m.AssessTool + m.BodyPartName
                              }).ToListAsync();

            return data;
        }
        /// <summary>
        /// 获取某段时间内最后一条维护记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<PatientPainCareMainInfo> GetPatientLastPainCareByDateTime(string inpatientID, DateTime startTime, DateTime endTime)
        {
            var data = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
            && m.AssessDate >= startTime.Date && m.AssessDate <= endTime.Date).ToListAsync();

            return data.Where(m => m.AssessDate.Add(m.AssessTime) >= startTime && m.AssessDate.Add(m.AssessTime) <= endTime)
                .OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).LastOrDefault();
        }

        public async Task<List<PatientPainView>> GetPainHandoverViewByDateTime(string inpatientID, DateTime startTime, DateTime endTime)
        {
            var data = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
            && m.AssessDate >= startTime.Date && m.AssessDate <= endTime.Date).Select(m => new PatientPainView
            {
                PatientPainRecordID = m.PatientPainRecordID,
                PatientPainCareMainID = m.PatientPainCareMainID,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                ActiveStatus = m.ActiveStatus,
                FASScore = m.FASScore,
                AssessTool = m.AssessTool,
                PainPoint = m.PainPoint,
                BodyPartName = m.BodyPartName,
                CareIntervention = m.CareIntervention,
                BringToShift = m.BringToShift ?? false,
            }).ToListAsync();

            return data.Where(m => m.AssessDate.Add(m.AssessTime) >= startTime && m.AssessDate.Add(m.AssessTime) <= endTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
        /// <summary>
        /// 根据来源获取疼痛分数
        /// </summary>
        /// <param name="sourceID">来源序号</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<int?> GetPainPointBySourceID(string sourceID, string sourceType)
        {
            return await _medicalDbContext.PatientPainCareMainInfos
                .Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => m.PainPoint).FirstOrDefaultAsync();
        }

        public async Task<List<KeyValueString>> GetPainScheduleIDs(List<string> careMainIDs)
        {
            var data = await (from m in _medicalDbContext.PatientPainCareMainInfos.Where(m => careMainIDs.Contains(m.PatientPainCareMainID) && m.DeleteFlag != "*")
                              select new KeyValueString
                              {
                                  Key = m.PatientPainCareMainID,
                                  Value = m.PatientScheduleMainID
                              }).ToListAsync();

            return data;
        }
        /// <summary>
        /// 获取本病区每个患者最近一次的疼痛评估分数
        /// </summary>
        /// <param name="inpatientIDs">病人住院序号集合</param>
        /// <returns></returns>
        public async Task<List<PatientPainCareMainInfo>> GetPatientLastPainViewByInpatientIDs(List<string> inpatientIDs)
        {
            // TODO：最后支持.net3.1的EFCore版本不支持直接在数据库层面执行GroupBy后，对每个 group进行 orderBy操作。
            // 所以这里先取出数据再进行GroupBy操作，升级到.net6.0后可以优化此处代码
            var data = await _medicalDbContext.PatientPainCareMainInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*")
                .Select(m => new PatientPainCareMainInfo
                {
                    InpatientID = m.InpatientID,
                    BedNumber = m.BedNumber,
                    PainPoint = m.PainPoint,
                }).ToListAsync();
            var result = data.GroupBy(m => m.InpatientID)
                .Select(m => m.OrderByDescending(n => n.AssessDate).ThenByDescending(n => n.AssessTime).First())
                .ToList();
            return result;
        }
    }
}