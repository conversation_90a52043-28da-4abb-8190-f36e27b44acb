﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientFlapCareMainRepository : IPatientFlapCareMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientFlapCareMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        /// <summary>
        /// 根据主表ID获取维护记录
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<PatientFlapCareMainInfo> GetByCareMainID(string mainID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.FirstOrDefaultAsync(m =>
            m.PatientFlapCareMainID == mainID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据主表ID获取维护记录View
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<FlapCareMainView> GetViewByRecordID(string mainID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.Where(m =>
            m.PatientFlapCareMainID == mainID && m.DeleteFlag != "*").Select(m => new FlapCareMainView
            {
                PatientFlapRecordID = m.PatientFlapRecordID,
                PatientFlapCareMainID = m.PatientFlapCareMainID,
                RecordsCode = m.RecordsCode,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                StationID = m.StationID,
                DepartmentListID = m.DepartmentListID,
                PatientScheduleMainID = m.PatientScheduleMainID,
                NumberOfAssessment = m.NumberOfAssessment,
                FlapTemperature = m.FlapTemperature,
                FlapColor = m.FlapColor,
                SkinTexture = m.SkinTexture,
                Swelling = m.Swelling,
                FlapAssess = m.FlapAssess,
                FlapTreatment = m.FlapTreatment,
                UserID = m.AddEmployeeID,
                BringToShift = m.BringToShift,
                BringToNursingRecord = m.BringToNursingRecord,
                InformPhysician = m.InformPhysician
            }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据RecordID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<PatientFlapCareMainInfo>> GetInfosByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.Where(m =>
            m.PatientFlapRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据RecordID获取开始评估记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<PatientFlapCareMainInfo> GetStartCareMainByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.FirstOrDefaultAsync(t =>
            t.PatientFlapRecordID == recordID && t.DeleteFlag != "*" && t.RecordsCode.Contains("Start"));
        }
        /// <summary>
        /// 根据主记录ID获取维护记录
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<FlapCareMainView>> GetCareMainViewsByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.Where(m =>
            m.PatientFlapRecordID == recordID && m.DeleteFlag != "*").Select(m => new FlapCareMainView
            {
                PatientFlapRecordID = m.PatientFlapRecordID,
                PatientFlapCareMainID = m.PatientFlapCareMainID,
                RecordsCode = m.RecordsCode,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                StationID = m.StationID,
                DepartmentListID = m.DepartmentListID,
                PatientScheduleMainID = m.PatientScheduleMainID,
                NumberOfAssessment = m.NumberOfAssessment,
                FlapTemperature = m.FlapTemperature,
                FlapColor = m.FlapColor,
                SkinTexture = m.SkinTexture,
                Swelling = m.Swelling,
                FlapAssess = m.FlapAssess,
                FlapTreatment = m.FlapTreatment,
                UserID = m.AddEmployeeID,
                BringToShift = m.BringToShift,
                BringToNursingRecord = m.BringToNursingRecord,
                InformPhysician = m.InformPhysician
            }).ToListAsync();
        }
        /// <summary>
        /// 获取当前病人所有主记录的选框勾选状态
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientBringView>> GetRecordsBringViewsByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.Where(m =>
            m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.RecordsCode.Contains("Start"))
                .Select(m => new PatientBringView
                {
                    RecordID = m.PatientFlapRecordID,
                    CareMainID = m.PatientFlapCareMainID,
                    BringToShift = m.BringToShift,
                    BringToNursingRecord = m.BringToNursingRecord,
                    InformPhysician = m.InformPhysician
                }).ToListAsync();
        }
        /// <summary>
        /// 获取最新的评估次数序号
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<int> GetNewCareMainNumberOfAssessment(string recordID)
        {
            var number = await _medicalDbContext.PatientFlapCareMainInfos.CountAsync(m =>
            m.PatientFlapRecordID == recordID && m.DeleteFlag != "*");
            return number + 1;
        }
        /// <summary>
        /// 获取班内有护理措施的评估照护数据
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <param name="shift">班别</param>
        /// <param name="shiftDate">班别日期</param>
        /// <returns></returns>
        public async Task<List<SpecificHandoverView>> GetHandoverViewsByShift(string inpatientID, string shift, DateTime shiftDate)
        {
            return await _medicalDbContext.PatientFlapCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" &&
            m.Shift == shift && m.ShiftDate == shiftDate && !string.IsNullOrEmpty(m.CareIntervention)).Select(m => new SpecificHandoverView
            {
                RecordID = m.PatientFlapRecordID,
                CareMainID = m.PatientFlapCareMainID,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                CareIntervention = m.CareIntervention,
                BringToShift = m.BringToShift ?? false,
            }).ToListAsync();
        }
        public async Task<List<SpecificHandoverView>> GetHandoverViewsByTimeRange(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var data = await _medicalDbContext.PatientFlapCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date).ToListAsync();
            return data.Where(m => m.AddDate.Add(m.AssessTime) >= startDateTime && m.AddDate.Add(m.AssessTime) <= endDateTime).Select(m => new SpecificHandoverView
            {
                RecordID = m.PatientFlapRecordID,
                CareMainID = m.PatientFlapCareMainID,
                AssessDate = m.AssessDate,
                AssessTime = m.AssessTime,
                CareIntervention = m.CareIntervention,
                BringToShift = m.BringToShift ?? false,
            }).ToList();
        }

        public async Task<List<SpecificHandoverView>> GetHandoverView(string inpatientID, DateTime startDateTime, DateTime endDateTime)
        {
            var list = await (from m in _medicalDbContext.PatientFlapCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
                  && m.AssessDate >= startDateTime.Date && m.AssessDate <= endDateTime.Date)
                              join n in _medicalDbContext.PatientFlapRecordInfos.Where(n => n.InpatientID == inpatientID && n.DeleteFlag != "*")
                              on m.PatientFlapRecordID equals n.PatientFlapRecordID
                              select new SpecificHandoverView
                              {
                                  RecordID = m.PatientFlapRecordID,
                                  CareMainID = m.PatientFlapCareMainID,
                                  AssessDate = m.AssessDate,
                                  AssessTime = m.AssessTime,
                                  BringToShift = m.BringToShift ?? false,
                                  RecordsCode = m.RecordsCode,
                                  BodyPartName = n.BodyPartName,
                                  CareIntervention = m.CareIntervention,
                              }).ToListAsync();

            return list.Where(m => m.AssessDate.Add(m.AssessTime) >= startDateTime && m.AssessDate.Add(m.AssessTime) <= endDateTime).OrderBy(m => m.AssessDate).ThenBy(m => m.AssessTime).ToList();
        }
    }
}