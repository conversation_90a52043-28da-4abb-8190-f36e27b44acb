﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
//排除原因:由于改版，该版本已经不再使用
//操作人员：梁宝华 2020-03-17
namespace Medical.Data.Repository
{
    public class PatientPainAssessDetailRepository : IPatientPainAssessDetailRepository
    {
        private MedicalDbContext _medicalDbContext;

        public PatientPainAssessDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据PatientPainMainID获取明细表所有数据
        /// </summary>
        /// <param name="PatientPainMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientPainAssessDetailInfo>> GetByID(string PatientPainMainID)
        {
            var list = await _medicalDbContext.PatientPainDetailInfos.Where(t => t.PatientPainMainID == PatientPainMainID && t.DeleteFlag != "*").ToListAsync();
            return list;
        }
    }
}