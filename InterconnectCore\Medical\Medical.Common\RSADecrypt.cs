﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Medical.Common
{
    public class EncryptDecryptHelper
    {
        #region 默认密钥向量
        //默认密钥向量 
        private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        #endregion

        #region rsa加密
        /// <summary>
        /// rsa加密
        /// </summary>
        /// <param name="s">要加密的字符串</param>
        /// <param name="key">加密key</param>
        /// <returns></returns>
        public static string RSAEncrypt(string s, string key)
        {
            if (string.IsNullOrEmpty(s)) throw new ArgumentException("An empty string value cannot be encrypted.");
            if (string.IsNullOrEmpty(key)) throw new ArgumentException("Cannot encrypt using an empty key. Please supply an encryption key.");
            CspParameters cspp = new CspParameters();
            cspp.KeyContainerName = key;
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(cspp);
            rsa.PersistKeyInCsp = true;
            byte[] bytes = rsa.Encrypt(System.Text.UTF8Encoding.UTF8.GetBytes(s), true);
            return BitConverter.ToString(bytes);
        }
        #endregion

        #region rsa解密
        /// <summary>
                /// rsa解密
                /// </summary>
                /// <param name="s">加密后字符串字符串</param>
                /// <param name="key">加密key</param>
                /// <returns></returns>
        public static string RSADecrypt(string s, string key)
        {
            string result = null;
            if (string.IsNullOrEmpty(s)) throw new ArgumentException("An empty string value cannot be encrypted.");
            if (string.IsNullOrEmpty(key)) throw new ArgumentException("Cannot decrypt using an empty key. Please supply a decryption key.");
            CspParameters cspp = new CspParameters();
            cspp.KeyContainerName = key;
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(cspp);
            rsa.PersistKeyInCsp = true;
            string[] decryptArray = s.Split(new string[] { "-" }, StringSplitOptions.None);
            byte[] decryptByteArray = Array.ConvertAll<string, byte>(decryptArray, (a => Convert.ToByte(byte.Parse(a, System.Globalization.NumberStyles.HexNumber))));
            byte[] bytes = rsa.Decrypt(decryptByteArray, true);
            result = System.Text.UTF8Encoding.UTF8.GetString(bytes);
            return result;
        }
        #endregion

        #region DES加密字符串
        ///   <summary> 
        ///   DES加密字符串 
        ///   </summary> 
        ///   <param   name= "encryptString "> 待加密的字符串 </param> 
        ///   <param   name= "encryptKey "> 加密密钥,要求为8位 </param> 
        ///   <returns> 加密成功返回加密后的字符串，失败返回源串 </returns> 
        public static string EncryptDES(string encryptString, string encryptKey)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Encoding.UTF8.GetBytes(encryptString);
                DESCryptoServiceProvider dCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, dCSP.CreateEncryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Convert.ToBase64String(mStream.ToArray());
            }
            catch
            {
                return encryptString;
            }
        }
        #endregion

        #region DES解密字符串
        ///   <summary> 
                ///   DES解密字符串 
                ///   </summary> 
                ///   <param   name= "decryptString "> 待解密的字符串 </param> 
                ///   <param   name= "decryptKey "> 解密密钥,要求为8位,和加密密钥相同 </param> 
                ///   <returns> 解密成功返回解密后的字符串，失败返源串 </returns> 
        public static string DecryptDES(string decryptString, string decryptKey)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(decryptKey);
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Convert.FromBase64String(decryptString);
                DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(mStream.ToArray());
            }
            catch
            {
                return decryptString;
            }
        }
        #endregion

        #region 3DES加密
        /// <summary>
                /// 3DES加密
                /// </summary>
                /// <param name="strString">需要加密的字符串</param>
                /// <param name="strKey">加密key</param>
                /// <returns></returns>
        public static string DES3Encrypt(string strString, string strKey)
        {
            TripleDESCryptoServiceProvider DES = new TripleDESCryptoServiceProvider();
            MD5CryptoServiceProvider hashMD5 = new MD5CryptoServiceProvider();


            DES.Key = hashMD5.ComputeHash(Encoding.Default.GetBytes(strKey));
            DES.Mode = CipherMode.ECB;


            ICryptoTransform DESEncrypt = DES.CreateEncryptor();


            byte[] Buffer = Encoding.Default.GetBytes(strString);
            return Convert.ToBase64String(DESEncrypt.TransformFinalBlock(Buffer, 0, Buffer.Length));
        }
        #endregion

        #region 3DES解密
        /// <summary>
        /// 3DES解密
        /// </summary>
        /// <param name="strString">解密字符串</param>
        /// <param name="strKey">解密key</param>
        /// <returns></returns>
        public static string DES3Decrypt(string strString, string strKey)
        {
            TripleDESCryptoServiceProvider DES = new TripleDESCryptoServiceProvider();
            MD5CryptoServiceProvider hashMD5 = new MD5CryptoServiceProvider();


            DES.Key = hashMD5.ComputeHash(Encoding.Default.GetBytes(strKey)); DES.Mode = CipherMode.ECB;
            ICryptoTransform DESDecrypt = DES.CreateDecryptor();
            string result = "";
            try
            {
                byte[] Buffer = Convert.FromBase64String(strString);
                result = Encoding.Default.GetString(DESDecrypt.TransformFinalBlock(Buffer, 0, Buffer.Length));
            }
            catch (System.Exception e)
            {
                throw (new System.Exception("null", e));
            }
            return result;
        }
        #endregion

        #region RC2加密
        /// <summary>
                /// RC2加密
                /// </summary>
                /// <param name="encryptString">待加密的密文</param>
                /// <param name="encryptKey">密匙(必须为5-16位)</param>
                /// <returns></returns>
        public static string RC2Encrypt(string encryptString, string encryptKey)
        {
            string returnValue;
            try
            {
                byte[] temp = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
                RC2CryptoServiceProvider rC2 = new RC2CryptoServiceProvider();
                byte[] byteEncryptString = Encoding.Default.GetBytes(encryptString);
                MemoryStream memorystream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memorystream, rC2.CreateEncryptor(Encoding.Default.GetBytes(encryptKey), temp), CryptoStreamMode.Write);
                cryptoStream.Write(byteEncryptString, 0, byteEncryptString.Length);
                cryptoStream.FlushFinalBlock();
                returnValue = Convert.ToBase64String(memorystream.ToArray());


            }
            catch (Exception ex)
            {
                throw ex;
            }
            return returnValue;


        }
        #endregion

        #region RC2解密
        /// <summary>
        /// RC2解密
        /// </summary>
        /// <param name="decryptString">密文</param>
        /// <param name="decryptKey">密匙(必须为5-16位)</param>
        /// <returns></returns>
        public static string RC2Decrypt(string decryptString, string decryptKey)
        {
            string returnValue;
            try
            {
                byte[] temp = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
                RC2CryptoServiceProvider rC2 = new RC2CryptoServiceProvider();
                byte[] byteDecrytString = Convert.FromBase64String(decryptString);
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, rC2.CreateDecryptor(Encoding.Default.GetBytes(decryptKey), temp), CryptoStreamMode.Write);
                cryptoStream.Write(byteDecrytString, 0, byteDecrytString.Length);
                cryptoStream.FlushFinalBlock();
                returnValue = Encoding.Default.GetString(memoryStream.ToArray());
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return returnValue;
        }
        #endregion

        #region 3DES 加密
        /// <summary>
        /// 3DES 加密
        /// </summary>
        /// <param name="encryptString">待加密密文</param>
        /// <param name="encryptKey1">密匙1(长度必须为8位)</param>
        /// <param name="encryptKey2">密匙2(长度必须为8位)</param>
        /// <param name="encryptKey3">密匙3(长度必须为8位)</param>
        /// <returns></returns>
        public static string DES3Encrypt(string encryptString, string encryptKey1, string encryptKey2, string encryptKey3)
        {


            string returnValue;
            returnValue = EncryptDES(encryptString, encryptKey3);
            returnValue = EncryptDES(returnValue, encryptKey2);
            returnValue = EncryptDES(returnValue, encryptKey1);

            return returnValue;


        }
        #endregion

        #region 3DES 解密
        /// <summary>
        /// 3DES 解密
        /// </summary>
        /// <param name="decryptString">待解密密文</param>
        /// <param name="decryptKey1">密匙1(长度必须为8位)</param>
        /// <param name="decryptKey2">密匙2(长度必须为8位)</param>
        /// <param name="decryptKey3">密匙3(长度必须为8位)</param>
        /// <returns></returns>
        public static string DES3Decrypt(string decryptString, string decryptKey1, string decryptKey2, string decryptKey3)
        {


            string returnValue;
            try
            {
                returnValue = DecryptDES(decryptString, decryptKey1);
                returnValue = DecryptDES(returnValue, decryptKey2);
                returnValue = DecryptDES(returnValue, decryptKey3);


            }
            catch (Exception ex)
            {
                throw ex;
            }
            return returnValue;
        }
        #endregion

        #region AES加密
        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="encryptString">待加密的密文</param>
        /// <param name="encryptKey">加密密匙</param>
        /// <returns></returns>
        public static string AESEncrypt(string encryptString, string encryptKey)
        {
            string returnValue;
            byte[] temp = Convert.FromBase64String("Rkb4jvUy/ye7Cd7k89QQgQ==");
            Rijndael AESProvider = Rijndael.Create();
            try
            {
                byte[] byteEncryptString = Encoding.Default.GetBytes(encryptString);
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, AESProvider.CreateEncryptor(Encoding.Default.GetBytes(encryptKey), temp), CryptoStreamMode.Write);
                cryptoStream.Write(byteEncryptString, 0, byteEncryptString.Length);
                cryptoStream.FlushFinalBlock();
                returnValue = Convert.ToBase64String(memoryStream.ToArray());
            }


            catch (Exception ex)
            {
                throw ex;
            }


            return returnValue;


        }
        #endregion

        #region AES解密
        /// <summary>
        ///AES 解密
        /// </summary>
        /// <param name="decryptString">待解密密文</param>
        /// <param name="decryptKey">解密密钥</param>
        /// <returns></returns>
        public static string AESDecrypt(string decryptString, string decryptKey)
        {
            string returnValue = "";
            byte[] temp = Convert.FromBase64String("Rkb4jvUy/ye7Cd7k89QQgQ==");
            Rijndael AESProvider = Rijndael.Create();
            try
            {
                byte[] byteDecryptString = Convert.FromBase64String(decryptString);
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, AESProvider.CreateDecryptor(Encoding.Default.GetBytes(decryptKey), temp), CryptoStreamMode.Write);
                cryptoStream.Write(byteDecryptString, 0, byteDecryptString.Length);
                cryptoStream.FlushFinalBlock();
                returnValue = Encoding.Default.GetString(memoryStream.ToArray());
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return returnValue;
        }
        #endregion

        #region ZI加密
        /// <summary>
        ///ZI 加密
        /// </summary>
        /// <param name="decryptString">待加密密文</param>
        /// <returns></returns>
        //public static string ZIEncrypt(string decryptString, string encryptKey)
        //{
        //    string returnValue = "";
        //    try
        //    {
        //        byte[] temp = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        //        byte[] byteEncryptString = Encoding.Default.GetBytes(decryptString);
        //        MemoryStream memorystream = new MemoryStream();
        //        RC2CryptoServiceProvider rC2 = new RC2CryptoServiceProvider();
        //        List<byte> byteList = new List<byte>(byteEncryptString);
        //        byteList.Insert(0, 7);
        //        var length = byteList.Count;
        //        byteList.Add(7);
        //        CryptoStream cryptoStream = new CryptoStream(memorystream, rC2.CreateEncryptor(Encoding.Default.GetBytes(encryptKey), temp), CryptoStreamMode.Write);
        //        cryptoStream.Write(byteList.ToArray(), 0, byteList.ToArray().Length);
        //        cryptoStream.FlushFinalBlock();
        //        returnValue = Convert.ToBase64String(memorystream.ToArray());
        //        return returnValue;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //    return "";
        //}
        #endregion

        #region ZI解密
        /// <summary>
        ///ZI 解密
        /// </summary>
        /// <param name="decryptString">待解密密文</param>
        /// <returns></returns>
        //public static string ZIDecrypt(string decryptString, string decryptKey)
        //{
        //    string returnValue = "";
        //    Rijndael AESProvider = Rijndael.Create();
        //    try
        //    {
        //        byte[] temp = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        //        RC2CryptoServiceProvider rC2 = new RC2CryptoServiceProvider();
        //        byte[] byteDecrytString = Convert.FromBase64String(decryptString);
        //        List<byte> byteList = new List<byte>(byteDecrytString);
        //        byteList.RemoveAt(0);
        //        var length = byteList.Count;
        //        byteList.RemoveAt(length-1);
        //        MemoryStream memoryStream = new MemoryStream();
        //        CryptoStream cryptoStream = new CryptoStream(memoryStream, rC2.CreateDecryptor(Encoding.Default.GetBytes(decryptKey), temp), CryptoStreamMode.Write);
        //        cryptoStream.Write(byteList.ToArray(), 0, byteList.ToArray().Length);
        //        cryptoStream.FlushFinalBlock();
        //        returnValue = Encoding.Default.GetString(memoryStream.ToArray());
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //    return returnValue;
        //}
        #endregion
    }
}
