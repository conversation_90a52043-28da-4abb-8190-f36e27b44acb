﻿using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Medical.Data.Interface.Log;
using Medical.ViewModels.View;
using NLog;

namespace InterconnectCore.Service
{
    public class SyncBackMedicationClosingControlLogService
    {

        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IClosingControlLogsRepository _closingControlLogsRepository;
        public readonly IHospitalListRepository _hospitalListRepository;
        public readonly IEmployeelDataRepository _employeelDataRepository;
        public readonly IPatientBasicDataRepository _patientBasicDataRepository;
        public readonly IInpatientDataRepository _inpatientDataRepository;
        public readonly IDepartmentListRepository _departmentListRepository;
        private readonly IPatientMedicineScheduleRepository _patientMedicineScheduleRepository;
        private readonly IRequestApiService _requestApiService;

        public SyncBackMedicationClosingControlLogService(
            IClosingControlLogsRepository closingControlLogsRepository,
            IHospitalListRepository hospitalListRepository,
            IEmployeelDataRepository employeelDataRepository,
            IPatientBasicDataRepository patientBasicDataRepository,
            IInpatientDataRepository inpatientDataRepository,
            IDepartmentListRepository departmentListRepository,
            IPatientMedicineScheduleRepository patientMedicineScheduleRepository,
            IRequestApiService requestApiService)
        {
            _closingControlLogsRepository = closingControlLogsRepository;
            _hospitalListRepository = hospitalListRepository;
            _employeelDataRepository = employeelDataRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _departmentListRepository = departmentListRepository;
            _patientMedicineScheduleRepository = patientMedicineScheduleRepository;
            _requestApiService = requestApiService;
        }

        private const string API_SETTING_CODE = "SyncBackMedicationOrderExecute";

        /// <summary>
        /// 同步皮试相关数据给HIS
        /// </summary>
        /// <param name="syncData">回传调用参数</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        public async Task<bool> SyncDataAsync(SyncDataBackLog syncData, string hospitalID)
        {
            var dataKeys = syncData.DataKey.Split("||").Select(key => int.TryParse(key, out var intKey) ? intKey : 0).Where(key => key != 0).ToList();
            if (dataKeys.Count == 0)
            {
                _logger.Error($"同步回传给药闭环日志记录失败,dataKey为空，参数={ListToJson.ToJson(syncData)}");
                return false;
            }
            var closingControlLogsInfos = await _closingControlLogsRepository.GetLogsByIdAsync(dataKeys);
            if (closingControlLogsInfos.Count == 0)
            {
                return true;
            }
            var medicineGroupIDs = closingControlLogsInfos.Select(m => m.SerialNumber).ToArray();
            var medicineSchedules = await _patientMedicineScheduleRepository.GetByGroupAsync(medicineGroupIDs);
            var employeeIDs = closingControlLogsInfos.Select(m => m.AddEmployeeID).ToList();
            var hospital = await _hospitalListRepository.GetHospital(hospitalID);
            var employees = await _employeelDataRepository.GetEmployeeByEmployeeIDs(employeeIDs, hospitalID);
            var departmentList = await _departmentListRepository.GetDepartmentListAsync();
            var numberOfAdmissions = await _inpatientDataRepository.GetNumberOfAdmissionsByInpatientID(closingControlLogsInfos[0].InpatientID);
            // 循环处理数据
            var requestParamList = new List<MedicationOrderExecuteView>();
            foreach (var dataKey in dataKeys)
            {
                var oneLog = closingControlLogsInfos.Find(m => m.LogID == dataKey);
                if (oneLog == null)
                {
                    continue;
                }
                if (oneLog.AddDateTime == null)
                {
                    _logger.Error($"闭环日志中没有新增时间LogID={oneLog.LogID}");
                    continue;
                }
                var innerMedicineSchedules = medicineSchedules.Where(m => m.GroupID == oneLog.SerialNumber).ToList();
                if (innerMedicineSchedules.Count == 0)
                {
                    continue;
                }
                var dept = departmentList.Find(m => m.ID == oneLog.DepartmentListID);
                if (dept == null)
                {
                    _logger.Error($"获取科室信息失败，ID={oneLog.DepartmentListID}");
                    continue;
                }
                foreach (var medicineSchedule in innerMedicineSchedules)
                {
                    var view = new MedicationOrderExecuteView
                    {
                        InpatientID = oneLog.InpatientID,
                        CaseNumber = oneLog.CaseNumber,
                        HospitalCode = hospital?.HospitalCode,
                        HospitalName = hospital?.HospitalName,
                        ChartNo = oneLog.ChartNo,
                        ID = dataKey.ToString(),
                        NumberOfAdmissions = numberOfAdmissions,
                        DepartmentCode = dept?.DepartmentCode,
                        DepartmentName = dept?.Department,
                        StatusCode = oneLog.StatusCode,
                        AddEmployeeID = oneLog.AddEmployeeID,
                        AddDateTime = oneLog.AddDateTime.Value,
                        OrderCode = medicineSchedule.OrderCode,
                        PatientOrderMainID = medicineSchedule.PatientOrderMainID,
                        PatientOrderDetailID = medicineSchedule.PatientOrderDetailID,
                        PrepareDate = medicineSchedule.PrepareDate,
                        PrepareTime = medicineSchedule.PrepareTime,
                        PrepareEmployeeID = medicineSchedule.PrepareEmployeeID,
                        PerformDate = medicineSchedule.PerformDate,
                        PerformTime = medicineSchedule.PerformTime,
                        PerformEmployeeID = medicineSchedule.PerformEmployeeID,
                        CancelReason = medicineSchedule.CancelReason,
                    };
                    requestParamList.Add(view);
                }
            }
            // 有数据才发送
            if (requestParamList.Count > 0)
            {
                return await _requestApiService.RequestInterconnect(API_SETTING_CODE, requestParamList, requestParamList[0].InpatientID, requestParamList[0].CaseNumber);
            }
            return false;
        }
    }
}