﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Handover;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientHandoverContentsRepository : IPatientHandoverContentsRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        public PatientHandoverContentsRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 根据handoverID,HandoverClass获取交接内容
        /// </summary>
        /// <param name="handoverID"></param>
        /// <param name="handoverClass"></param>
        /// <returns></returns>
        public async Task<PatientHandoverContentsInfo> GetPatientHandoverSBARByHandoverID(string handoverID, string handoverClass)
        {
            return await _dbContext.PatientHandoverContentsInfos.Where(m => m.HandoverID == handoverID
                  && m.HandoverClass == handoverClass && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取交接班明细数据
        /// </summary>
        /// <param name="handoverID"></param>
        /// <returns></returns>
        public async Task<List<PatientHandoverContentsInfo>> GetByHandoverID(string handoverID)
        {
            return await _dbContext.PatientHandoverContentsInfos.Where(m => m.HandoverID == handoverID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根基HandoverID集合获取交班SBAR
        /// </summary>
        /// <param name="handoverIDList"></param>
        /// <returns></returns>
        public async Task<List<PatientHandoverContentsInfo>> GetPatientHandoverSBARByHandoverIDList(List<string> handoverIDList)
        {
            return await _dbContext.PatientHandoverContentsInfos.Where(m => handoverIDList.Contains(m.HandoverID)
                   && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientHandoverContentsView>> GetPatientHandOffViewByHandoverIDs(List<string> handoverIDList)
        {
            return await _dbContext.PatientHandoverContentsInfos.Where(m => handoverIDList.Contains(m.HandoverID)
                   && m.DeleteFlag != "*" && m.HandoverClass == "HandOff")
                .Select(m => new PatientHandoverContentsView
                {
                    HandoverID = m.HandoverID,
                    HandoverClass = m.HandoverClass,
                    InpatientID = m.InpatientID,
                    Situation = m.Situation,
                    Background = m.Background,
                    Assement = m.Assement,
                    Recommendation = m.Recommendation
                }).ToListAsync();

        }
        /// <summary>
        /// 根基HandoverID集合获取交班SBAR
        /// </summary>
        /// <param name="handoverIDList"></param>
        /// <returns></returns>
        public async Task<List<PatientHandoverContentsInfo>> GetPatientHandoverSBARByHandoverIDListAndHandoverClass(List<string> handoverIDList, string handoverClass)
        {
            return await _dbContext.PatientHandoverContentsInfos.Where(m => handoverIDList.Contains(m.HandoverID) && m.HandoverClass == handoverClass
                   && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据handoverID 获取数据 优先获取接班数据 
        /// </summary>
        /// <param name="handoverID"></param>
        /// <returns></returns>
        public async Task<PatientHandoverContentsInfo> GetSBARByHandoverID(string handoverID)
        {
            var list = await _dbContext.PatientHandoverContentsInfos.Where(m => m.HandoverID == handoverID && m.DeleteFlag != "*").ToListAsync();
            var handonContent = list.Find(m => m.HandoverClass == "HandOn");
            if (handonContent != null)
            {
                return handonContent;
            }
            return list.Find(m => m.HandoverClass == "HandOff");
        }
    }
}