﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Medical.Data.Repository
{
    public class NurseEMRFileListRepository : INurseEMRFileListRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public NurseEMRFileListRepository(MedicalDbContext medicalDb)
        {
            _medicalDbContext = medicalDb;
        }
        public async Task<List<NurseEMRFileListInfo>> GetAllNurseEMRFileList(string inpateintID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.Inpatientid == inpateintID && m.DeleteFlag != "*")
                .Select(m=>new NurseEMRFileListInfo
                {
                    ID = m.ID,
                    Inpatientid = m.Inpatientid,
                    FileClass = m.FileClass,
                    ShowName = m.ShowName,
                    ModifyDate = m.ModifyDate,
                    Stationid = m.Stationid,
                    RecordListID = m.RecordListID,
                    SerialNumber = m.SerialNumber,
                }).ToListAsync();
        }
        public async Task<NurseEMRFileListInfo> GetFileIDByIDView(int ID)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => m.ID == ID && m.DeleteFlag != "*").Select(m => new NurseEMRFileListInfo
            {
                FileId = m.FileId,
                FileName = m.FileName,
                Inpatientid = m.Inpatientid
            }
           ).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取多病人的电子病历
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <returns></returns>
        public async Task<List<NurseEMRFileListInfo>> GetDataByInpatientIDs(List<string> inpatientIDs)
        {
            return await _medicalDbContext.NurseEMRFileListInfos.Where(m => inpatientIDs.Contains(m.Inpatientid) && m.DeleteFlag != "*")
                    .Select(m => new NurseEMRFileListInfo
                    {
                        ID = m.ID,
                        Inpatientid = m.Inpatientid,
                        FileClass = m.FileClass,
                        ShowName = m.ShowName,
                        ModifyDate = m.ModifyDate,
                        Stationid = m.Stationid,
                        RecordListID = m.RecordListID,
                        SerialNumber = m.SerialNumber,
                    }).ToListAsync();
        }
    }
}
