﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPumpingRecordRepository : IPatientPumpingRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPumpingRecordRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据inpatientID获取未停止或停止数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="endFlag"></param>
        /// <returns></returns>
        public async Task<List<PatientPumpingRecordInfo>> GetDataByInpatientID(string inpatientID, bool? endFlag)
        {
            var list = await _medicalDbContext.PatientPumpingRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            if (endFlag.HasValue)
            {
                if (endFlag.Value)
                {
                    list = list.Where(m => m.EndDate.HasValue && m.EndTime.HasValue).ToList();
                }
                else
                {
                    list = list.Where(m => !m.EndDate.HasValue && !m.EndTime.HasValue).ToList();
                }
            }
            return list.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientPumpingRecordInfo> GetDataByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientPumpingRecordInfos.Where(m => m.PatientPumpingRecordID == recordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取最大sort
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<int> GetMaxSort(string inpatientID)
        {
            var list = await _medicalDbContext.PatientPumpingRecordInfos.Where(m => m.InpatientID == inpatientID).Select(m => m.Sort).ToListAsync();
            if (list.Count == 0)
            {
                return 0;
            }
            else
            {
                return list.Max();
            }
        }
        public async Task<List<PatientPumpingRecordInfo>> GetDataByDate(string inpatientID, DateTime startTime, DateTime endTime)
        {
            var result = new List<PatientPumpingRecordInfo>();

            var list = await _medicalDbContext.PatientPumpingRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();

            var noEndList = list.Where(m => m.EndDate == null).ToList();

            var endListNoFilter = list.Where(m => m.EndDate.HasValue).ToList();

            var endList = endListNoFilter.Where(m => m.StartDate.Date.Add(m.StartTime) >= startTime && m.EndDate.Value.Date.Add(m.EndTime.Value) < endTime);

            result.AddRange(noEndList);
            result.AddRange(endList);

            return result;
        }
    }
}
