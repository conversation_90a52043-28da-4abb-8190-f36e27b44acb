﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DepartmentListRepository : IDepartmentListRepository
    {
        private readonly MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public DepartmentListRepository(
            MedicalDbContext medicalDbContext
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDbContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }
        public async Task<List<DepartmentListInfo>> GetDepartmentListAsync()
        {
            return (List<DepartmentListInfo>)await GetCacheAsync();

        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<DepartmentListInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            var data = await _medicalDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Department.GetKey(_sessionCommonServer);
        }

        public async Task<DepartmentListInfo> GetAsync(int id)
        {
            var data = await this.GetAllAsync<DepartmentListInfo>();
            return data.Find(t => t.ID == id);
        }
        /// <summary>
        /// 根据科室ID获取科室名称
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<DepartmentListInfo> GetDepartmentNameByID(int departmentID)
        {
            var data = (List<DepartmentListInfo>)await GetCacheAsync();
            return data.FirstOrDefault(m => departmentID == m.ID);
        }
    }
}