﻿using Medical.Models;

namespace InterconnectCore.ViewModels
{
    public class MarkView
    {
        public InpatientDataInfo InpatientData { get; set; }
        /// <summary>
        /// 呼叫Mark的Url
        /// </summary>
        public string Url { get; set; }
        /// <summary>
        /// 组装后的Mark数据
        /// </summary>
        public string MarkDatas { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
    }
}