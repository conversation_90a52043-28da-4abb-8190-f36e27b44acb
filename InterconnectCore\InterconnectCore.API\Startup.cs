﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using InterconnectCore.API.Extensions;
using InterconnectCore.API.Infrastructure.Filters;
using InterconnectCore.Data.Context;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Repository;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using System.Reflection;


namespace InterconnectCore.API
{

    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <summary>
        ///
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            //初始化配置(SystemConfig必须为Medical.ViewModels,保证与Repository层使用的对象一致)
            services.Configure<ViewModels.SystemConfig>(Configuration.GetSection("Configs"));
            services.Configure<Medical.ViewModels.SystemConfig>(Configuration.GetSection("Configs"));
            services.Configure<CommonSystemConfig>(Configuration.GetSection("Configs"));
            var dataOut = Configuration.GetConnectionString("DataOutConnection");
            var dataIn = Configuration.GetConnectionString("DataInConnection");
            var medicalStatistics = Configuration.GetConnectionString("MedicalStatistics");
            var Files = Configuration.GetConnectionString("FilesConnection");
            #region "数据库连线设定"
            //连接Redis数据库
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = Configuration.GetConnectionString("RedisConnection");
                options.InstanceName = "";
            });
            services.AddDbContext<DataOutContext>(
                options => options.UseSqlServer(dataOut, m => m.UseCompatibilityLevel(100))
                .AddInterceptors(new QueryWithNoLockDbCommandInterceptor())
                );
            services.AddScoped<DataOutContext>();


            services.AddDbContext<MedicalDbContext>(
                options =>
                options.UseSqlServer(dataIn, m => m.UseCompatibilityLevel(100))
               .AddInterceptors(new QueryWithNoLockDbCommandInterceptor())
            );
            services.AddScoped<MedicalDbContext>();


            services.AddDbContext<MedicalStatisticsDbContext>(
               options =>
               options.UseSqlServer(medicalStatistics, m => m.UseCompatibilityLevel(100))
              .AddInterceptors(new QueryWithNoLockDbCommandInterceptor())
             );
            services.AddScoped<MedicalStatisticsDbContext>();


            services.AddDbContext<FilesDBContext>(
                       options =>
                       options.UseSqlServer(Files, m => m.UseCompatibilityLevel(100))
                      .AddInterceptors(new QueryWithNoLockDbCommandInterceptor())
                    );
            services.AddScoped<FilesDBContext>();


            //设定批次保存数据库连接
            services.AddUnitOfWork<DataOutContext>();
            services.AddUnitOfWork<MedicalDbContext>();
            services.AddUnitOfWork<FilesDBContext>();

            //数据交换
            services.AddDbContext<CDADBContext>(options =>
            {
                //options.EnableSensitiveDataLogging(true);
                options.UseSqlServer(Configuration.GetConnectionString("DataOutConnection"), m => m.UseCompatibilityLevel(100));
            });
            services.AddScoped<CDADBContext>();

            //CA签章
            services.AddDbContext<CADBContext>(options => options.UseSqlServer(Configuration.GetConnectionString("CAConnection"), m => m.UseCompatibilityLevel(100)));
            services.AddScoped<CADBContext>();
            services.AddUnitOfWork<CADBContext>();

            #endregion

            #region Hangfire
            services.AddHangfire(x => x.UseSqlServerStorage(Configuration.GetConnectionString("HangFireConnection")));
            services.AddHangfireServer();
            #endregion
            services.AddHttpContextAccessor();
            services.AddEndpointsApiExplorer();
            services.AddSingleton<ConnectionMultiplexer>(_ => ConnectionMultiplexer.Connect(Configuration.GetConnectionString("RedisConnection")));
            services.AddKeyedSingleton<IDistributedLock, RedisDistributedLock>(LockType.Redis.GetDescription());
            services.AddKeyedSingleton<IDistributedLock, LocalLock>(LockType.Local.GetDescription());
            //增加服务端缓存
            services.AddMemoryCache();

            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_3_0);

            services.AddMvcCore(options =>
            {
                options.Filters.Add(typeof(AuthorizationAttribute));
                options.Filters.Add(typeof(CommonFilterAttribute));
                options.Filters.Add<GlobalExceptionsFilter>();
            }).AddNewtonsoftJson(options =>
            {
                ////设置时间格式
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.MicrosoftDateFormat;
                options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
            });


            services.AddCors(options =>
            {
                options.AddPolicy("any", builder =>
                {
                    //builder.AllowAnyOrigin() //允许任何来源的主机访问
                    builder.SetIsOriginAllowed(m => true)
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .SetPreflightMaxAge(TimeSpan.FromSeconds(36000));//指定可以缓存预检请求的响应的时间
                });
            });

            #region "添加WebAPI接口文档"
            //添加Swagger (提供WebAPI接口)
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "InterconnectCore.API", Version = "v1" });
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                options.IncludeXmlComments(xmlPath, true);
            });
            #endregion

            services.AddControllers();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            //添加Swagger及默认UI
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.DocumentTitle = "InterconnectCore API Doc";
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "InterconnectCore.API V1");
                c.DisplayRequestDuration();
                c.RoutePrefix = string.Empty;
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            app.UseMiddleware<ReqResLogMiddle>();
            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthorization();

            app.UseCors("any");

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}