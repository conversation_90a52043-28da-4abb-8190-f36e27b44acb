﻿using Medical.Models;

namespace InterconnectCore.ViewModels
{
    public class StationAndDepartmentView
    {
        /// <summary>
        /// 病区
        /// </summary>
        public List<StationListInfo> StationList { get; set; }
        /// <summary>
        /// 科室
        /// </summary>
        public List<DepartmentListInfo> DepartmentList { get; set; }
        /// <summary>
        /// 病区科室对照
        /// </summary>
        public List<StationToDeptInfo> StationToDepartmentList { get; set; }
    }
}
