﻿using NLog;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text.RegularExpressions;

namespace InterconnectCore.Common
{
    public class ImageHelper
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        private static char[] base64CodeArray = new char[]
         {
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            '0', '1', '2', '3', '4',  '5', '6', '7', '8', '9', '+', '/', '='
         };

        public static string ImageToBase64Str(string path)
        {
            Image img = Image.FromFile(path);
            return ImageToBase64Str(img);
        }

        public static string ImageToBase64Str(Image img)
        {
            MemoryStream ms = new MemoryStream();
            img.Save(ms, ImageFormat.Png);
            byte[] arr = new byte[ms.Length];
            ms.Position = 0;
            ms.Read(arr, 0, (int)ms.Length);
            ms.Close();
            return Convert.ToBase64String(arr);
        }

        /// <summary>
        /// 将Base64字符串转换为Image对象
        /// </summary>
        /// <param name="base64Str">base64字符串</param>
        /// <returns></returns>
        public static Bitmap Base64StrToImage(string base64Str)
        {
            if (!IsBase64(base64Str))
            {
                return null;
            }

            Bitmap bitmap = null;

            try
            {
                byte[] arr = Convert.FromBase64String(base64Str);
                MemoryStream ms = new MemoryStream(arr);
                Bitmap bmp = new Bitmap(ms);
                ms.Close();
                bitmap = bmp;
            }
            catch (Exception ex)
            {
                _logger.Warn("Base64字符串转换为Image对象失败:" + base64Str + "错误:" + ex.ToString());
                return null;
            }

            return bitmap;
        }

        /// <summary>
        /// 将Base64字符串转换为图片并保存到本地
        /// </summary>
        /// <param name="base64Str">base64字符串</param>
        /// <param name="savePath">图片保存地址，如：/Content/Images/10000.png</param>
        /// <returns></returns>
        public static bool Base64StrToImage(string base64Str, string savePath)
        {
            var ret = true;

            try
            {
                var bitmap = Base64StrToImage(base64Str);

                if (bitmap != null)
                {
                    //创建文件夹
                    var folderPath = savePath.Substring(0, savePath.LastIndexOf('/'));

                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }

                    //图片后缀格式
                    var suffix = savePath.Substring(savePath.LastIndexOf('.') + 1, savePath.Length - savePath.LastIndexOf('.') - 1).ToLower();
                    var suffixName = suffix == "png" ? ImageFormat.Png :
                        suffix == "jpg" || suffix == "jpeg" ? ImageFormat.Jpeg :
                        suffix == "bmp" ? ImageFormat.Bmp :
                        suffix == "gif" ? ImageFormat.Gif : ImageFormat.Jpeg;

                    //这里复制一份对图像进行保存，否则会出现“GDI+ 中发生一般性错误”的错误提示
                    var bmpNew = new Bitmap(bitmap);
                    bmpNew.Save(savePath, suffixName);
                    bmpNew.Dispose();

                    bitmap.Dispose();
                }
                else
                {
                    ret = false;
                }
            }
            catch (Exception ex)
            {
                _logger.Warn("Base64字符串转换为Image对象失败:" + base64Str + "错误:" + ex.ToString());
                return false;
            }

            return ret;
        }

        public static bool IsBase64(string base64Str)
        {
            string strRegex = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";

            if (string.IsNullOrEmpty(base64Str))
                return false;
            if (DateTime.TryParse(base64Str, out _))
                return false;
            if (base64Str.Length % 4 != 0)
                return false;

            return Regex.IsMatch(base64Str, strRegex);
        }
    }
}