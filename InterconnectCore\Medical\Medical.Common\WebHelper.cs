﻿using Microsoft.AspNetCore.Http;

namespace Medical.Common
{
    public class WebHelper
    {
        /// <summary>
        /// 获取用户IP地址
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static string GetWebClientIP(HttpContext context)
        {
            string userip = "未获取用户ip";

            try
            {
                if (context == null || context.Request == null)
                    return "";

                string customerip = context.Connection.RemoteIpAddress.ToString();

                return customerip;
            }
            catch { }

            return userip;
        }

    }
}
