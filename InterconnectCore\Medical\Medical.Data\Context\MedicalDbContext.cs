﻿using Medical.Models;
using Medical.Models.Patient;
using Medical.Models.RecordSupplement;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Medical.Data.Context
{
    public partial class MedicalDbContext : DbContext
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public MedicalDbContext(DbContextOptions<MedicalDbContext> options)
           : base(options)
        {
            this.SaveChangesFailed += SaveChangesFailedHandler;
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<BedListInfo>().<PERSON><PERSON><PERSON>(t => new { t.ID, t.HospitalID });
            builder.Entity<ICDToAssessInfo>().<PERSON><PERSON><PERSON>(t => new { t.ICDCode, t.HospitalID });
            builder.Entity<FrequencyInfo>().<PERSON><PERSON>ey(t => (new { t.ID, t.Language, t.HospitalID, t.StationID }));
            builder.Entity<BodyPartListInfo>().<PERSON><PERSON>ey(t => new { t.ID, t.Language });
            builder.Entity<AssessListInfo>().<PERSON><PERSON><PERSON>(t => new { t.ID, t.Language });
            builder.Entity<NursingProblemInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<RelatedFactorInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<NursingInterventionMainInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<NursingOutComeInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<TubeListInfo>().HasKey(t => (new { t.ID, t.Language, t.HospitalID }));
            builder.Entity<NursingGoalInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<SettingDescriptionInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<IntakeOutputSettingInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<NursingOutComeInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<NursingInterventionDetailInfo>().HasKey(t => (new { t.InterventionDetailID, t.Language }));
            builder.Entity<AssessContentInfo>().HasKey(t => (new { t.ID, t.HospitalID, t.RecordsCode }));
            builder.Entity<PatientBasicDataInfo>().HasKey(t => (new { t.PatientID, t.HospitalID }));
            builder.Entity<InstructionInfo>().HasKey(t => (new { t.ID, t.HospitalID, t.Language }));
            builder.Entity<AssessInteractionInfo>().HasKey(t => (new { t.ID, t.HospitalID }));
            builder.Entity<AssessNormalCheckInfo>().HasKey(t => (new { t.ID, t.HospitalID, }));
            builder.Entity<RecordsListInfo>().HasKey(t => new { t.ID, t.HospitalID, t.Language });
            builder.Entity<StationListInfo>().HasKey(t => new { t.ID, t.HospitalID });
            builder.Entity<PhysicianOrderInfo>().HasKey(t => new { t.ID, t.HospitalID });
            builder.Entity<PatientTestResultInfo>().HasKey(t => new { t.CaseNumber, t.TestNo, t.TestCode, t.TestDate });
            builder.Entity<ProblemClassdictInfo>().HasKey(t => new { t.Classnum, t.Subnum });
            builder.Entity<AssessScoreRangeInfo>().HasKey(t => new { t.AssessScoreRangeID, t.Language, t.HospitalID });
            builder.Entity<RecordsFormatInfo>().HasKey(t => new { t.ID, t.HospitalID, t.Language });
            builder.Entity<AdministrationSettingInfo>().HasKey(t => new { t.AdministrationSettingID, t.Language, t.HospitalID });
            builder.Entity<StatisticsReportInfo>().HasKey(t => (new { t.ID, t.Language, t.HospitalID }));
            builder.Entity<AssessListIDToColorInfo>().HasKey(t => (new { t.ID, t.Language }));
            builder.Entity<SwitchRecordFormatInfo>().HasKey(t => (new { t.ID, t.HospitalID, t.Language }));
            builder.Entity<NursingRecordInfo>().HasKey(t => (new { t.NursingRecordID }));
            builder.Entity<MedicationRouteInfo>().HasKey(t => (new { t.MedicationRouteID}));
            builder.Entity<OrderToAssessListInfo>().HasKey(t => (new { t.OrderToAssessListID, t.HospitalID }));
            builder.Entity<PhysicianOrderInfo>().HasKey(t => (new { t.ID, t.HospitalID }));
            builder.Entity<TempData>().HasKey(t => (new { t.DataId, t.DataClass }));
            builder.Entity<SendMessageSettingInfo>().HasKey(t => (new { t.SendMessageSettingID }));
            builder.Entity<PatientSpecialListImageInfo>().HasKey(t => (new { t.PatientSpecialListImageID }));
            builder.Entity<MobileVersionInfo>().HasKey(t => (new { t.MobileVersionID }));
            builder.Entity<ClinicalSettingInfo>().HasKey(t => (new { t.ClinicSettingID, t.HospitalID, t.Language }));
            builder.Entity<DepartmentToAssessInfo>().HasKey(t => (new { t.ID, t.SystemID, t.HospitalID, t.DepartmentListID }));
            builder.Entity<EventSettingInfo>().HasKey(t => (new { t.EventSettingID, t.HospitalID, t.Language }));
            builder.Entity<QCCheckAppraiseContentInfo>().HasKey(t => (new { t.QCCheckAppraiseContentID, t.HospitalID, t.RecordsCode }));
            builder.Entity<QCCheckSubjectInfo>().HasKey(t => (new { t.QCCheckSubjectID, t.HospitalID }));
            builder.Entity<QCCheckDictionaryMainInfo>().HasKey(t => new { t.QCCheckDictionaryMainID, t.HospitalID });
            builder.Entity<QCCheckDictionaryDetailInfo>().HasKey(t => new { t.QCCheckDictionaryDetailID });
            builder.Entity<ScanErrorLogInfo>().HasKey(t => (new { t.ScanErrorLogID }));
            builder.Entity<QuestionAndAnaswerInfo>().HasKey(t => (new { t.QuestionAndAnaswerID }));
            builder.Entity<TubeToBodyPartInfo>().HasKey(t => (new { t.ID }));
            builder.Entity<QCCheckRecordInfo>().HasKey(t => (new { t.QCCheckRecordID }));
            builder.Entity<QCCheckMainInfo>().HasKey(t => (new { t.QCCheckMainID }));
            builder.Entity<QCCheckDetailInfo>().HasKey(t => (new { t.QCCheckDetailID }));
            builder.Entity<HandoverSupplementInfo>().HasKey(t => (new { t.HandoverRecordID, t.ID }));
            builder.Entity<DepartmentListInfo>().HasKey(t => (new { t.ID, t.HospitalID }));
            builder.Entity<AuthorityListInfo>().HasKey(t => (new { t.ID, t.FunctionListID, t.Language }));
            builder.Entity<PatientConsultInfo>().HasKey(t => (new { t.ID }));
            builder.Entity<VirtualStationListInfo>().HasKey(t => (new { t.VirtualStationID }));
            builder.Entity<VirtualBedListInfo>().HasKey(t => (new { t.HisBedNumber, t.VirtualStationID }));

            builder.Entity<AllergyDrugInfo>().HasKey(t => (new { t.AllergyDrugID }));
            builder.Entity<AllergyBasicInfo>().HasKey(t => (new { t.AllergyBasicID, t.AllergyType, t.Language }));
            builder.Entity<HandoverSettingInfo>().HasKey(t => (new { t.HandoverSettingID, t.HospitalID }));
            builder.Entity<HandoverKeySignSettingInfo>().HasKey(t => (new { t.HandoverKeySignSettingID }));
            builder.Entity<AttendanceCrossInfo>().HasKey(t => (new { t.AttendanceCrossID, t.HospitalID }));

            builder.Entity<InpatientNurseOperationStatisticsInfo>().HasKey(m => new { m.Year, m.Month, m.StationID, m.DepartmentListID });

            //检核审批相关
            builder.Entity<DocumentListInfo>().HasKey(t => (new { t.DocumentListID }));
            builder.Entity<SignOffDetailInfo>().HasKey(t => (new { t.SignOffDetailID }));
            builder.Entity<SignOffMainInfo>().HasKey(t => (new { t.SignOffMainID }));
            //排出物属性字典
            builder.Entity<OutputAttributeInfo>().HasKey(t => (new { t.OutputAttributeID, t.Language, t.AttributeKind }));
            //排出物属性字典
            builder.Entity<OutputAttributeInfo>().HasKey(t => (new { t.OutputAttributeID, t.Language, t.AttributeKind }));
            //会诊目的
            builder.Entity<ConsultGoalInfo>().HasKey(t => (new { t.ConsultGoalID, t.HospitalID }));
            //AppConfig配置表
            builder.Entity<AppConfigSettingInfo>().HasKey(t => (new { t.AppConfigSettingID, t.HospitalID }));
            //措施费用表
            builder.Entity<InterventionBillingInfo>().HasKey(t => (new { t.InterventionBillingID }));
            builder.Entity<Apache2Info>().HasKey(t => (new { t.Apache2ID, t.CaseNumber }));
            // 汉字拼音首字母
            builder.Entity<PinyinIndexInfo>().HasKey(t => (new { t.PinyinIndexID }));
            //告知书
            builder.Entity<EMRDocumentInfo>().HasKey(t => (new { t.EMRDocumentID }));
            // 菜单列表
            builder.Entity<MenuListInfo>().HasKey(t => new { t.MenuListID, t.HospitalID, t.Language });
            builder.Entity<TPRScheduleInfo>().HasKey(t => (new { t.TPRScheduleID }));
            //造口记录表
            builder.Entity<PatientStomaRecordInfo>().HasKey(t => (new { t.PatientStomaRecordID }));
            //造口维护主表
            builder.Entity<PatientStomaCareMainInfo>().HasKey(t => (new { t.PatientStomaCareMainID }));

            //观察措施模板表
            builder.Entity<ObserveTemplateInfo>().HasKey(t => (new { t.ObserveTemplateID, t.HospitalID }));
            //数据交换配置
            builder.Entity<CDADocumentSettingInfo>().HasKey(t => (new { t.CDADocumentSettingID, t.HospitalID }));
            builder.Entity<MappingDictionaryInfo>().HasKey(t => (new { t.MappingDictionaryID, t.HospitalID }));
            builder.Entity<ICDListInfo>().HasKey(t => (new { t.ICDCode, t.Language, t.Version, t.HospitalID }));
            //年度计划上传文件表
            builder.Entity<NursingManagementFileinfo>().HasKey(t => (new { t.NursingManagementFileID }));

            //门诊编号对照
            builder.Entity<MRNMappingInfo>().HasKey(t => (new { t.MainMRN, t.AncillaryMRN }));

            builder.Entity<ICDToAssessInfo>().HasKey(t => new { t.ICDToAssessID });

            builder.Entity<RecordsToBodyPartInfo>().HasKey(t => new { t.ID, t.Language });

            ///补录日志表
            builder.Entity<SupplementRecordLogInfo>().HasAlternateKey(t => new { t.SupplementRecordLogID });
            //巡视记录表
            builder.Entity<PatientPatrolRecordInfo>().HasAlternateKey(t => new { t.PatientPatrolRecordID });

            builder.Entity<PumpingDrugListInfo>().HasAlternateKey(t => new { t.PumpingDrugListID });

            builder.Entity<PatientOrderDetailInfo>().HasAlternateKey(m => new { m.PatientOrderDetailID });

            builder.Entity<PatientOrderMainInfo>().HasAlternateKey(m => new { m.PatientOrderMainID });

            builder.Entity<PatientOrdersMergeInfo>().HasAlternateKey(m => new { m.ID });
            ///输血信息表
            builder.Entity<PatientBloodBagMainInfo>().HasKey(m => new { m.BloodDonorBagsCode, m.BloodNumber, m.OuterBloodCode });

            builder.Entity<StationShiftInfo>().HasAlternateKey(m => new { m.ID, m.HospitalID, m.Language });

            builder.Entity<LanguagePackageInfo>().HasKey(m => new { m.HospitalID, m.Language, m.FunctionID, m.Code });

            builder.Entity<EMRFieldInfo>().HasKey(m => new { m.ID, m.Language });

            builder.Entity<CascadeSettingInfo>().HasKey(m => new { m.ID });

            builder.Entity<EMRSourceInfo>().HasKey(m => new { m.ID, m.Language, m.HospitalID });

            //评估记录表
            builder.Entity<PatientFormRecordInfo>().HasKey(m => new { m.PatientFormRecordID });
            builder.Entity<PatientFormDetailInfo>().HasKey(m => new { m.PatientFormDetailID });
            builder.Entity<NursingBoardSettingInfo>().HasKey(m => new { m.NursingBoardSettingID });
            builder.Entity<LoginLogInfo>().HasKey(m => new { m.LoginLogID, m.EmployeeID });
            builder.Entity<ButtonListInfo>().HasKey(m => new { m.ButtonListID, m.Language });
            builder.Entity<RouterListInfo>().HasKey(m => new { m.RouterListID });
            builder.Entity<RouterUseButtonInfo>().HasKey(m => new { m.RouterUseButtonID });
            builder.Entity<AuthorityRouterUseButtonInfo>().HasKey(m => new { m.AuthorityRouterUseButtonID });
            builder.Entity<PatientHandoverImageInfo>().HasKey(m => new { m.PatientHandoverImageID });
            builder.Entity<PatientHandoverContentsInfo>().HasKey(m => new { m.PatientHandoverContentsID });
            builder.Entity<FormulaListInfo>().HasKey(t => (new { t.ID, t.Language, t.HospitalID, t.RecordsCode }));
            //病区工作提醒
            builder.Entity<StationToJobTipInfo>().HasKey(t => (new { t.StationJobTipID, t.HospitalID }));
            //日带教表
            builder.Entity<DailyTeachingInfo>().HasKey(t => (new { t.DailyTeachingID }));

            builder.Entity<PatientDataStorageRecordInfo>().HasKey(t => (new { t.InpatientID, t.DataType }));

            builder.Entity<PatientPatrolRecordInfo>().Property(b => b.GroupID).IsUnicode(false);
            builder.Entity<PatientMedicineScheduleInfo>().Property(b => b.GroupID).IsUnicode(false);
            builder.Entity<PatientMedicineScheduleInfo>().Property(b => b.PatientOrderMainID).IsUnicode(false);
            builder.Entity<DailyTeachingInfo>().HasKey(t => (new { t.DailyTeachingID }));
            builder.Entity<EventChangeSettingInfo>().HasKey(t => (new { t.EventChangeSettingID }));
            builder.Entity<BedLendRecordInfo>().HasKey(t => (new { t.ID }));
            builder.Entity<PatientProfileInfo>().ToTable(tb => tb.HasTrigger("T_DeletePatientProfile"));
            builder.Entity<PerpetualCalendarInfo>().HasKey(t => new { t.Date, t.HospitalID });

            base.OnModelCreating(builder);
        }
        /// <summary>
        /// 监听保存失败，保留截断字符串报错日志
        /// </summary>
        /// <param name="sender">当前上下文</param>
        /// <param name="e">保存失败的相关参数</param>
        private void SaveChangesFailedHandler(object sender, SaveChangesFailedEventArgs e)
        {
            var context = (DbContext)sender;
            var ex = e.Exception;
            if (ex.HResult != -**********)
            {
                return;
            }
            var entries = context.ChangeTracker.Entries()
                .Where(x => x.State == EntityState.Added || x.State == EntityState.Modified)
                .ToList();
            // 打印被跟踪的实体
            foreach (var entry in entries)
            {
                // 打印每个属性的值
                foreach (var prop in entry.Properties)
                {
                    // 检查字符串长度是否超过数据库定义的最大长度
                    if (prop.Metadata.ClrType == typeof(string) && prop.CurrentValue is string value)
                    {
                        if (string.IsNullOrEmpty(value))
                        {
                            continue;
                        }
                        var typeName = prop.Metadata.GetColumnType(); // 获取字段最大长度
                        var maxLength = GetMaxLengthFromTypeName(typeName);
                        // 字节长度
                        var byteLength = Encoding.UTF8.GetByteCount(value);
                        if (maxLength.HasValue && byteLength > maxLength.Value)
                        {
                            _logger.Error($"截断字符串：表【{entry.Entity.GetType().Name}】字段【{prop.Metadata.Name}】值【{value}】,长度【{byteLength}】-【{maxLength}】");
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 获取字符串的字节长度
        /// </summary>
        /// <param name="typeName"></param>
        /// <returns></returns>
        public static int? GetMaxLengthFromTypeName(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return null;
            }
            var match = Regex.Match(typeName, @"varchar\((\d+)\)", RegexOptions.IgnoreCase);
            if (match.Success && match.Groups.Count > 1)
            {
                if (int.TryParse(match.Groups[1].Value, out var length))
                {
                    return length;
                }
            }
            return null;
        }
    }
}