﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleMainRepository : IPatientScheduleMainRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        public PatientScheduleMainRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 透过排程序号取得排程数据
        /// </summary>
        /// <param name="patientScheduleMainID"></param>
        /// <returns></returns>
        public async Task<PatientScheduleMainInfo> GetByID(string patientScheduleMainID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.PatientScheduleMainID == patientScheduleMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<List<PatientScheduleMainInfo>> GetByCaseNumberAndPatientInterventionID(string casenumber, string patientInterventionID)
        {
            return await _medicalDbContext.PatientScheduleMain.Where(m => m.CaseNumber == casenumber
          && m.PatientInterventionID == patientInterventionID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}