﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientThrombolysisRecordRepository : IPatientThrombolysisRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientThrombolysisRecordRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<PatientThrombolysisRecordInfo> GetRecordByReordIDAsync(string thrombolysisRecordID)
        {
            return await _medicalDbContext.PatientThrombolysisRecordInfos.Where(t => t.PatientThrombolysisRecordID == thrombolysisRecordID && t.DeleteFlag != "*").FirstOrDefaultAsync();

        }
        /// <summary>
        /// 根据病人inpatientID获取病人数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientThrombolysisRecordInfo>> GetRecordByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientThrombolysisRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderBy(m => m.AddDate).ToListAsync();
        }
        /// <summary>
        /// 获取交班使用数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="startDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endDate"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<HandoverThrombolysisCareIntervention>> GetPatientThrombolysisIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientThrombolysisCareMainInfos
                               join b in _medicalDbContext.PatientThrombolysisRecordInfos on a.PatientThrombolysisRecordID equals b.PatientThrombolysisRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverThrombolysisCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   PatientThrombolysisRecordID = a.PatientThrombolysisRecordID,
                                   Observe = a.Observe
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .ToList();

            return datas;
        }

        /// <summary>
        /// 获取单病人溶栓主记录列表信息
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<PatientThrombolysisRecordTableView>> GetRecordViewListByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientThrombolysisRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").OrderBy(m => m.AddDate)
                .Select(item => new PatientThrombolysisRecordTableView
                {
                    PatientThrombolysisRecordID = item.PatientThrombolysisRecordID,
                    InpatientID = item.InpatientID,
                    AddDate = item.AddDate,
                    OccuredDepartmentID = item.OccuredDepartmentID,
                    OccuredStationID = item.OccuredStationID,
                    StartDate = item.StartDate,
                    StartTime = item.StartTime,
                    EndDate = item.EndDate,
                    EndTime = item.EndTime,
                    EntryTime = item.EntryTime,
                    ThrombolysisKind = item.ThrombolysisKind,
                    AdministrationStart = item.AdministrationStart,
                    AddEmployeeID = item.AddEmployeeID,
                    //默认先写死
                    Content = null,
                    SourceContent = null
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据主记录获取RecordView
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientThrombolysisRecordTableView> GetRecordViewByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientThrombolysisRecordInfos.Where(m => m.PatientThrombolysisRecordID == recordID && m.DeleteFlag != "*").OrderBy(m => m.AddDate)
                .Select(item => new PatientThrombolysisRecordTableView
                {
                    PatientThrombolysisRecordID = item.PatientThrombolysisRecordID,
                    InpatientID = item.InpatientID,
                    AddDate = item.AddDate,
                    OccuredDepartmentID = item.OccuredDepartmentID,
                    OccuredStationID = item.OccuredStationID,
                    StartDate = item.StartDate,
                    StartTime = item.StartTime,
                    EndDate = item.EndDate,
                    EndTime = item.EndTime,
                    EntryTime = item.EntryTime,
                    ThrombolysisKind = item.ThrombolysisKind,
                    AdministrationStart = item.AdministrationStart,
                    AddEmployeeID = item.AddEmployeeID,
                    //默认先写死
                    Content = null,
                    SourceContent = null
                })
                .FirstOrDefaultAsync();
        }
    }
}
