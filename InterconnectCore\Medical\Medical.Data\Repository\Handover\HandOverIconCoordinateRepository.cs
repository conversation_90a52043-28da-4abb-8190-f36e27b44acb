﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class HandOverIconCoordinateRepository : IHandOverIconCoordinateRepository
    {

        private readonly MedicalDbContext _dbContext = null;
        public HandOverIconCoordinateRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 根据handoverID,HandoverClass获取交接内容
        /// </summary>
        /// <param name="handoverID"></param>
        /// <param name="handoverClass"></param>
        /// <returns></returns>
        public async Task<List<HandOverIconCoordinateInfo>> GetAsync(string handoverID, string handoverClass)
        {
            return await _dbContext.HandOverIconCoordinateInfos.Where(m => m.HandOverID == handoverID
                  && m.HandOverClass == handoverClass && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandOverIconCoordinateInfo>> GetByHandOverIDAsync(string handoverID)
        {
            return await _dbContext.HandOverIconCoordinateInfos.Where(m => m.HandOverID == handoverID
                  && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandOverIconCoordinateInfo>> GetAsync(List<string> handoverIDs, string handOverClass)
        {
            return await _dbContext.HandOverIconCoordinateInfos.Where(m => handoverIDs.Contains(m.HandOverID)
                  && m.HandOverClass == handOverClass && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandOverIconCoordinateInfo>> GetAsync(List<string> handoverIDs)
        {
            return await _dbContext.HandOverIconCoordinateInfos.Where(m => handoverIDs.Contains(m.HandOverID)
                  && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
