﻿using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using NLog;

namespace InterconnectCore.Service
{
    public class SyncBackPatientSkinTestService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IEmployeelDataRepository _employeelDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IPatientSkinTestRepository _patientSkinTestRepository;
        private readonly IPatientScheduleMainRepository _patientScheduleMainRepository;
        private readonly IPatientAttachedInterventionRepository _patientAttachedInterventionRepository;
        private readonly IPatientMedicineScheduleRepository _patientMedicineScheduleRepository;

        public SyncBackPatientSkinTestService(IHospitalListRepository hospitalListRepository
            , IStationListRepository stationListRepository
            , IInpatientDataRepository inpatientDataRepository
            , IPatientBasicDataRepository patientBasicDataRepository
            , IEmployeelDataRepository employeelDataRepository
            , IDepartmentListRepository departmentListRepository
            , IRequestApiService requestApiService
            , IPatientSkinTestRepository patientSkinTestRepository
            , IPatientScheduleMainRepository patientScheduleMainRepository
            , IPatientAttachedInterventionRepository patientAttachedInterventionRepository
            , IPatientMedicineScheduleRepository patientMedicineScheduleRepository)
        {
            _hospitalListRepository = hospitalListRepository;
            _stationListRepository = stationListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _employeelDataRepository = employeelDataRepository;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _patientSkinTestRepository = patientSkinTestRepository;
            _patientScheduleMainRepository = patientScheduleMainRepository;
            _patientAttachedInterventionRepository = patientAttachedInterventionRepository;
            _patientMedicineScheduleRepository = patientMedicineScheduleRepository;
        }
        /// <summary>
        /// 同步皮试相关数据给HIS
        /// </summary>
        /// <param name="syncData">回传数据</param>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="fromMedicineSchedule">是否来源给药排程</param>
        /// <returns></returns>
        public async Task<bool> SyncDataAsync(SyncDataBackLog syncData, string hospitalID,bool fromMedicineSchedule=true)
        {
            if (fromMedicineSchedule)
            {
                return await SyncPatientMedicineScheduleSkinTest(syncData, hospitalID);
            }
            return false;
        }

        #region 给药触发排程带出的皮试记录回传
        /// <summary>
        /// 回传给药触发排程带出的皮试记录
        /// </summary>
        /// <param name="syncData">回传数据信息</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        private async Task<bool> SyncPatientMedicineScheduleSkinTest(SyncDataBackLog syncData, string hospitalID)
        {
            var dataView = await _patientSkinTestRepository.GetByID(syncData.DataKey);
            if (dataView == null)
            {
                _logger.Warn($"药品触发皮试记录回传失败，未找到对应皮试记录，PatientSkinTestID：{syncData.DataKey}");
                return false;
            }
            var patientScheduleMain = await _patientScheduleMainRepository.GetByID(dataView.PatientScheduleMainID);
            if (patientScheduleMain == null || patientScheduleMain.SourceFlag != "T")
            {
                _logger.Warn($"药品触发皮试记录回传失败，未找到对应排程记录，PatientScheduleMainID：{dataView.PatientScheduleMainID}");
                return false;
            }
            var patientAttachedIntervention = await _patientAttachedInterventionRepository.GetByPatientAttachedInterventionID(patientScheduleMain.PatientInterventionID);
            if (patientAttachedIntervention == null)
            {
                _logger.Warn($"药品触发皮试记录回传失败，未找到对应排程触发记录，PatientInterventionID:{patientScheduleMain.PatientInterventionID}");
                return false;
            }
            var patientMedicineSchedule = await _patientMedicineScheduleRepository.GetByID(patientAttachedIntervention.PatientScheduleMainID);
            if (patientAttachedIntervention == null)
            {
                _logger.Warn($"药品触发皮试记录回传失败，未找到对应PatientMedicineSchedule药品记录，PatientMedicineScheduleID:{patientAttachedIntervention.PatientScheduleMainID}");
                return false;
            }
            var syncPatientMedicineScheduleSkinTestView = await CreateSyncPatientMedicineScheduleSkinTestView(syncData, hospitalID, dataView, patientMedicineSchedule);
            await _requestApiService.RequestInterconnect("SyncBackPatientMedicineScheduleSkinTest", syncPatientMedicineScheduleSkinTestView, syncPatientMedicineScheduleSkinTestView.InpatientID, syncPatientMedicineScheduleSkinTestView.CaseNumber);
            return true;
        }
        /// <summary>
        /// 创建View
        /// </summary>
        /// <param name="syncData">回传数据信息</param>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="dataView">皮试记录</param>
        /// <param name="patientMedicineSchedule">给药记录</param>
        /// <returns></returns>
        private async Task<SyncPatientMedicineScheduleSkinTestView> CreateSyncPatientMedicineScheduleSkinTestView(SyncDataBackLog syncData, string hospitalID, PatientSkinTestInfo dataView, PatientMedicineScheduleInfo patientMedicineSchedule)
        {
            var hospital = await _hospitalListRepository.GetHospital(hospitalID);
            var station = await _stationListRepository.GetAsync(dataView.StationID);
            var patientName = await _patientBasicDataRepository.GetPatientNameAsync(dataView.ChartNo);
            var employee = await _employeelDataRepository.GetEmployeeByEmployeeIDs([dataView.ModifyPersonID, dataView.AddEmployeeID], hospitalID);
            var dept = await _departmentListRepository.GetDepartmentNameByID(dataView.DepartmentListID);
            var numberOfAdmission = await _inpatientDataRepository.GetNumberOfAdmissionsByInpatientID(dataView.InpatientID);
            var allergyResultName = "";
            if (string.IsNullOrEmpty(dataView.AllergyResultName) && !string.IsNullOrEmpty(dataView.AllergyResultCode))
            {
                allergyResultName = dataView.AllergyResultCode == "0" ? "阴性" : "阳性";
            }
            var syncPatientMedicineScheduleSkinTestView = new SyncPatientMedicineScheduleSkinTestView
            {
                HospitalCode = hospital.HospitalCode,
                HospitalName = hospital.HospitalName,
                HospitalID = hospital.HospitalID,
                InpatientID = dataView.InpatientID,
                ChartNo = dataView.ChartNo,
                CaseNumber = dataView.CaseNumber,
                PatientName = patientName,
                BedNumber = dataView.BedNumber,
                NumberOfAdmissions = numberOfAdmission,
                StationCode = station?.StationCode ?? "",
                StationName = station?.StationName ?? "",
                DepartmentCode = dept?.DepartmentCode ?? "",
                DepartmentName = dept?.Department ?? "",
                PatientSkinTestID = dataView.PatientSkinTestID,
                ComboNo = patientMedicineSchedule.PatientOrderMainID,
                ExecSqnDrug = patientMedicineSchedule.PatientOrderDetailID,
                ExecTimes = syncData.EventType == EventType.Add ? "1" : "2",
                ItemType = "1",
                PerformDateTime = dataView.PerformDateTime.Value,
                AllergyResultCode = dataView.AllergyResultCode,
                AllergyResultName = string.IsNullOrEmpty(dataView.AllergyResultName) ? allergyResultName : dataView.AllergyResultName,
                AddEmployeeID = dataView.AddEmployeeID,
                AddEmployeeName = employee.Find(m => m.EmployeeID == dataView.AddEmployeeID)?.EmployeeName ?? "",
                ModifyEmployeeID = dataView.ModifyPersonID,
                ModifyEmployeeName = employee.Find(m => m.EmployeeID == dataView.ModifyPersonID)?.EmployeeName ?? "",
                DeleteFlag = dataView.DeleteFlag,
            };
            return syncPatientMedicineScheduleSkinTestView;
        }
        #endregion

    }
}
