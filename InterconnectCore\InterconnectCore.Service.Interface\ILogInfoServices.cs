﻿using InterconnectCore.Models;


namespace InterconnectCore.Service.Interface
{
    public interface ILogInfoServices
    {
        /// <summary>
        ///  写日志
        /// </summary>
        /// <param name="logInfos"></param>
        /// <returns></returns>
        LogInfo InsertLogAsync(string tablenames, string logs);

        /// <summary>
        /// 删除日志
        /// </summary>
        /// <returns></returns>
        bool DelLog();

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        List<LogInfo> GetLog(string guid);
    }
}
