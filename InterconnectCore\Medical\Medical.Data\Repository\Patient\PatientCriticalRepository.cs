﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Medical.Data.Repository
{
    public class PatientCriticalRepository : IPatientCriticalRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientCriticalRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }

        public async Task<List<PatientCriticalInfo>> GetPatientCriticalByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }


        public async Task<List<PatientCriticalInfo>> GetPatientCriticalByCategoryAsync(string category, string inpatientID = null)
        {
            if (inpatientID != null)
            {
                return await _medicalDbContext.PatientCriticalInfos.Where(m => m.InpatientID == inpatientID && m.Category == category && m.DeleteFlag != "*").ToListAsync();
            }

            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.Category == category && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人的危急值
        /// </summary>
        /// <param name="caseNumber">住院流水号</param>
        /// <param name="barCode">条码</param>
        /// <param name="itemName">项目名称</param>c
        /// <param name="reportConfirmDate">报告确认时间</param>
        /// <returns></returns>
        public async Task<PatientCriticalInfo> GetOneSyncCriticalValueAsync(string inpatientID, string barCode, string itemName, DateTime reportConfirmDate)
        {
            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.InpatientID == inpatientID &&
                m.BarCode == barCode && m.ItemName == itemName && m.ReportConfirmDate == reportConfirmDate && m.DeleteFlag != "*").FirstOrDefaultAsync();

        }

        public async Task<List<PatientCriticalInfo>> GetCriticalValuesByDateTimeSpan(DateTime start, DateTime end, string caseNumber)
        {
            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*"
                && m.ReportConfirmDate.Value > start && m.ReportConfirmDate.Value < end).OrderBy(m => m.ReportConfirmDate.Value)
                .Select(m => new PatientCriticalInfo
                {
                    ItemName = m.ItemName,
                    ItemResult = m.ItemResult,
                    ReportConfirmDate = m.ReportConfirmDate
                }).ToListAsync();
        }

        public async Task<List<PatientCriticalInfo>> GetCriticalByInpatientIDs(List<string> inpatientIDs)
        {
            return await _medicalDbContext.PatientCriticalInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取护士没有确认的危急值数据 
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="startDateTime"></param>
        /// <returns></returns>
        public async Task<List<PatientCriticalInfo>> GetUnConfirmedCriticalByCaseNumber(string caseNumber, DateTime? startDateTime = null)
        {
            if (startDateTime.HasValue)
            {
                return await _medicalDbContext.PatientCriticalInfos.Where(m => m.CaseNumber == caseNumber
                    && !m.NurseConfirmDate.HasValue && m.DeleteFlag != "*" && m.AddDate > startDateTime.Value).ToListAsync();
            }
            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.CaseNumber == caseNumber
                && !m.NurseConfirmDate.HasValue && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据barcode批量获取数据
        /// </summary>
        /// <param name="barcode"></param>
        /// <returns></returns>
        public async Task<List<string>> GetCriticalByBarCode(List<string> barcode)
        {
            return await _medicalDbContext.PatientCriticalInfos.Where(m => m.DeleteFlag != "*" && barcode.Contains(m.BarCode))
                .Select(m => m.BarCode).ToListAsync();
        }
        /// <summary>
        /// 获取危急值结果（日期段范围内-包含首尾）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="inpatientIDs">病人CCC住院ID集合</param>
        /// <returns></returns>
        public async Task<List<PatientCriticalInfo>> GetCriticalValuesByDateRangeAsync(DateTime startDate, DateTime endDate, List<string> inpatientIDs)
        {
            var end = endDate.AddDays(1).Date;

            return await _medicalDbContext.PatientCriticalInfos.Where(m =>
                inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*" && m.ReportConfirmDate.HasValue
                && m.ReportConfirmDate.Value >= startDate.Date && m.ReportConfirmDate.Value < end
                ).OrderBy(m => m.ReportConfirmDate.Value)
                .Select(m => new PatientCriticalInfo
                {
                    CaseNumber = m.CaseNumber,
                    ItemName = m.ItemName,
                    ItemResult = m.ItemResult,
                    ReportConfirmDate = m.ReportConfirmDate,
                    Category = m.Category,
                }).ToListAsync();
        }
    }
}
