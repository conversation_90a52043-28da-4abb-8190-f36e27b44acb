﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingInterventionDetailRepository : INursingInterventionDetailRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly INursingInterventionMainRepository _nursingInterventionMainRepository;
        private readonly GetCacheService _getCacheService;

        /// <summary>
        /// 健康教育措施公共补充明细措施编号
        /// </summary>
        private const int EDUCATION_INTERVENTIONMAINID_30041 = 30041;
        /// <summary>
        /// 健康教育措施类型
        /// </summary>
        private const string EDUCATION_ACTIONTYPE_3 = "3";

        public NursingInterventionDetailRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , INursingInterventionMainRepository nursingInterventionMainRepository
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _nursingInterventionMainRepository = nursingInterventionMainRepository;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingInterventionDetailInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var details = await _medicalDbContext.NursingInterventionDetail.Where(m => (m.HospitalID == "999999" || m.HospitalID == hospitalID.ToString()) && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
            return await AddRangeEducationDetail(details, (Int32)language);
        }
        /// <summary>
        /// 为所有ActionType=3的措施（健康教育）补充公共明细
        /// </summary>
        /// <param name="datas"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        private async Task<List<NursingInterventionDetailInfo>> AddRangeEducationDetail(List<NursingInterventionDetailInfo> datas, int language)
        {
            if (datas.Count == 0)
            {
                return datas;
            }

            var educationDetail = datas.Where(m => m.NursingInterventionMainID == EDUCATION_INTERVENTIONMAINID_30041).ToList();
            if (educationDetail.Count() == 0)
            {
                return datas;
            }

            var educationInterventions = await _nursingInterventionMainRepository.GetInterventionMainByActionType(EDUCATION_ACTIONTYPE_3);

            if (educationInterventions.Count != 0)
            {
                foreach (var intervention in educationInterventions)
                {
                    var maxSort = datas.Where(m => m.NursingInterventionMainID == intervention.ID).OrderByDescending(m => m.Sort).Select(m => m.Sort).FirstOrDefault();
                    var detail = CloneData.Clone(educationDetail);
                    foreach (var item in detail)
                    {
                        item.NursingInterventionMainID = intervention.ID;
                        item.Sort += maxSort;
                    }
                    datas.AddRange(detail);
                }
            }
            return datas;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingInterventionDetail.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 透过措施主序号获取措施名细资料
        /// </summary>
        /// <param name="NursingIntervenMainID">措施主序号</param>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<List<NursingInterventionDetailInfo>> GetByMainID(int[] NursingIntervenMainID)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas.Where(m => NursingIntervenMainID.Contains(m.NursingInterventionMainID))
                .OrderBy(m => m.ContentGroup).ThenBy(m => m.Sort).ThenBy(m => m.Stratum).ToList();
        }

        /// <summary>
        /// 透过措施主序号获取措施名细资料
        /// </summary>
        /// <param name="NursingIntervenMainID">措施主序号</param>
        /// <param name="language">语言</param>
        /// <returns></returns>
        public async Task<List<NursingInterventionDetailInfo>> GetNursingInterventionDetailByMainID(int nursingIntervenMainID)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas.Where(m => m.NursingInterventionMainID == nursingIntervenMainID)
                          .OrderBy(m => m.ContentGroup).ThenBy(m => m.Sort).ThenBy(m => m.Stratum).ToList();
        }

        /// <summary>
        /// 根据措施明细ID和语言获取数据
        /// </summary>
        /// <param name="nursingInterventionDetailID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<NursingInterventionDetailInfo> GetByDetailIDAsync(int? nursingInterventionDetailID)
        {
            if (nursingInterventionDetailID == null)
            {
                return null;
            }

            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas.Where(m => m.InterventionDetailID == nursingInterventionDetailID).FirstOrDefault();
        }

        public async Task<List<RenewAssessID>> GetNeedRenewClearData(int language, int interventionID)
        {
            var session = await _sessionCommonServer.GetSession();
            var query = await (from a in _medicalDbContext.NursingInterventionDetail
                               join b in _medicalDbContext.AssessLists
                               on new { ID = a.AssessListID, a.Language, a.DeleteFlag }
                               equals new { b.ID, b.Language, b.DeleteFlag }
                               join c in _medicalDbContext.PatientProfileCondition
                               on new { a.AssessListID, session.HospitalID }
                               equals new { c.AssessListID, c.HospitalID }
                               into temp
                               from d in temp.DefaultIfEmpty()
                               where a.NursingInterventionMainID == interventionID && a.Language == language
                                    && (a.HospitalID == "999999" || a.HospitalID == session.HospitalID)
                                    && a.DeleteFlag != "*" && b.RenewExclude == false
                               select new RenewAssessID
                               {
                                   AssessListID = a.AssessListID,
                                   NewAssessListID = d.NewAssessListID
                               }).ToListAsync();
            return query;
        }

        public async Task<List<NursingInterventionDetailInfo>> GetAsync()
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas;
        }

        /// <summary>
        /// 根据措施明细ID集合获取字典
        /// </summary>
        /// <param name="nursingInterventionDetailIDs">明细ID集合</param>
        /// <returns></returns>
        public async Task<List<NursingInterventionDetailInfo>> GetListByDetailIDAsync(List<int> nursingInterventionDetailIDs)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas.Where(m => nursingInterventionDetailIDs.Contains(m.InterventionDetailID)).ToList();
        }
        public async Task<List<NursingInterventionDetailInfo>> GetByDetailIDAndGroupID(int nursingIntervenMainID, int groupID)
        {
            var datas = await GetNursingInterventionDetailByMainID(nursingIntervenMainID);

            return datas.Where(m => m.ContentGroup == groupID).ToList();
        }
        public async Task<List<NursingInterventionDetailInfo>> GetByAssessListID(int assessListID)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();
            return datas.Where(m => m.AssessListID == assessListID).ToList();
        }
        public async Task<NursingInterventionDetailInfo> GetByMainIDAndAssessListID(int nursingInterventionMainID, int assessListID)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();
            return datas.Where(m => m.NursingInterventionMainID == nursingInterventionMainID && m.AssessListID == assessListID).FirstOrDefault();
        }


        public async Task<List<AssessListSelectView>> GetNursingInterventionDetailByDetailIDs(List<int> nursingInterventionDetailIDs)
        {
            var datas = (List<NursingInterventionDetailInfo>)await GetCacheAsync();

            return datas.Where(m => nursingInterventionDetailIDs.Contains(m.InterventionDetailID)).Select(n =>
                new AssessListSelectView
                {
                    Value = n.InterventionDetailID,
                    Label = n.ShowName,
                }).ToList();
        }

        /// <summary>
        /// 不根据缓存存储数据 开发使用 其它不允许使用
        /// </summary>
        /// <param name="nursingInterventionDetailID"></param>
        /// <param name="language"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<NursingInterventionDetailInfo> GetDetailINotByCacheAsync(int nursingInterventionDetailID, int language, string hospitalID)
        {
            var list = await _medicalDbContext.NursingInterventionDetail.Where(m => m.InterventionDetailID == nursingInterventionDetailID && m.Language == language
                     && m.DeleteFlag != "*" && (m.HospitalID == "999999" || m.HospitalID == hospitalID)).ToListAsync();
            if (list.Count == 0)
            {
                return null;
            }
            var hospitalInfo = list.Find(m => m.HospitalID == hospitalID);
            return hospitalInfo ?? list[0];
        }
    }
}