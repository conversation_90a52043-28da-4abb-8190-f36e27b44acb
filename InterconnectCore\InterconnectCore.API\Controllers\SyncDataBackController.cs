﻿using Medical.ViewModels.View;
using Medical.ViewModels;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Medical.Common;
using InterconnectCore.API.Extensions;
using InterconnectCore.Service.Interface;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 数据回传
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncDataBack")]
    [EnableCors("any")]
    public class SyncDataBackController : ControllerBase
    {
        private readonly ISyncDataService _syncDataBackService;
        private readonly IOptions<SystemConfig> _config;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="syncDataBackService"></param>
        /// <param name="config"></param>
        public SyncDataBackController(
              ISyncDataService syncDataBackService
            , IOptions<SystemConfig> config
            )
        {
            _syncDataBackService = syncDataBackService;
            _config = config;
        }
        /// <summary>
        /// 回传HIS数据 启动定时任务 Medical调用
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncBackToHIS")]
        public async Task<IActionResult> SyncBackToHIS([FromBody] SyncDataBackLog syncData)
        {
            var result = new ResponseResult
            {
                Data = await _syncDataBackService.SyncBackToHISAsyncJob(syncData, _config.Value.Language, _config.Value.HospitalID)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 回传HIS数据 
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncBackToHISAsyncJob")]
        public async Task<IActionResult> SyncBackToHISAsyncJob([FromBody] SyncDataBackLog syncData)
        {
            var result = new ResponseResult
            {
                Data = await _syncDataBackService.SyncBackToHISAsyncJob(syncData, _config.Value.Language, _config.Value.HospitalID)
            };
            return result.ToJson();
        }
    }
}
