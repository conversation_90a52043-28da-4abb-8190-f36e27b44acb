﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientCriticallyVisitsRecordRepository : IPatientCriticallyVisitsRecordRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;

        public PatientCriticallyVisitsRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据患者住院序号获取危重访视记录
        /// </summary>
        /// <param name="inpatientIDs">患者住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientCriticallyListView>> GetRecordListByInpatientIDs(List<string> inpatientIDs)
        {
            var datas = await _medicalDbContext.PatientCriticallyVisitsRecordInfos.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*")
                .Select(m => new PatientCriticallyListView
                {
                    StationID = m.StationID,
                    DepartmentListID = m.DepartmentListID,
                    PatientCriticallyPatrolRecordID = m.PatientCriticallyPatrolRecordID,
                    InpatientID = m.InpatientID,
                    InformFamilyDateTime = m.InformFamilyDateTime,
                    AdmissionDiagnosis = m.AdmissionDiagnosis,
                    CriticalIllnessDiagnosis = m.CriticalIllnessDiagnosis,
                    BriefDiseaseCondition = m.BriefDiseaseCondition,
                    ObservationPoints = m.ObservationPoints,
                    HeadNurseGuidanceContent = m.HeadNurseGuidanceContent,
                    ThirdLevelPatrolGuidanceContent = m.ThirdLevelPatrolGuidanceContent,
                    VisitsFlag = m.VisitsFlag,
                    VisitsDateTime = m.VisitsDateTime,
                    EmployeeID = m.EmployeeID,
                    HeadNurseVisitsDateTime = m.HeadNurseVisitsDateTime,
                    HeadNurseEmployeeID = m.HeadNurseEmployeeID,
                    NursingDeptEmployeeID = m.NursingDeptEmployeeID,
                    NursingDeptVisitsDateTime = m.NursingDeptVisitsDateTime,
                    CriticalIllnessType = m.CriticalIllnessType,
                    SourceID = m.SourceID,
                    OccurDateTime = m.OccurDateTime,
                    AddDateTime = m.AddDateTime,
                }).ToListAsync();
            return datas;
        }
        /// <summary>
        /// 根据主键ID获取危重访视记录
        /// </summary>
        /// <param name="patientCriticallyVisitsRecordID">危重访视记录主键</param>
        /// <returns></returns>
        public async Task<PatientCriticallyVisitsRecordInfo> GetRecordByRecordID(string patientCriticallyVisitsRecordID)
        {
            var data = await _medicalDbContext.PatientCriticallyVisitsRecordInfos.FirstOrDefaultAsync(m => m.PatientCriticallyPatrolRecordID == patientCriticallyVisitsRecordID && m.DeleteFlag != "*");
            return data;
        }

    }
}
