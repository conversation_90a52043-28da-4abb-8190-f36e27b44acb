﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SendMessageDetailRepository : ISendMessageDetailRepository
    {
        private readonly MessageDBContext _messageDBContext;
        public SendMessageDetailRepository(MessageDBContext messageDBContext)
        {
            _messageDBContext = messageDBContext;
        }

        public async Task<List<SendMessageDetailInfo>> GetUnReadMessagesAsync(int SendStationID, string SendEmployeeID)
        {
            return await _messageDBContext.SendMessageDetailInfos.Where(m => m.SendStationID == SendStationID
            && m.SendEmployeeID == SendEmployeeID && m.ReadFlag == false && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
