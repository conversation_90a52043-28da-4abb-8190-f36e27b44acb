﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using NLog;
using InterconnectCore.Common;

namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// 分布式锁
    /// </summary>
    public class DistributedLockFilterAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string _lockPrefix;
        private readonly LockType _lockType;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 分布式锁
        /// </summary>
        /// <param name="keyPrefix"></param>
        /// <param name="lockType">锁类型</param>
        public DistributedLockFilterAttribute(string keyPrefix, LockType lockType = LockType.Local)
        {
            _lockPrefix = keyPrefix;
            _lockType = lockType;
        }

        /// <summary>
        /// 执行验证
        /// </summary>
        /// <param name="context"></param>
        /// <param name="next"></param>
        /// <returns></returns>
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            IDistributedLock distributedLock = context.HttpContext.RequestServices.GetRequiredKeyedService<IDistributedLock>(_lockType.GetDescription());
            string lockKey = GetHttpParameterHasCode(context).ToString();
            bool isLockAcquired = await distributedLock.TryAcquireLockAsync(lockKey, new TimeSpan(0, 0, 30));

            if (!isLockAcquired)
            {
                context.Result = new ObjectResult(new { code = 400, data = true, message = "请不要重复操作" });
                return;
            }
            try
            {
                await next();
            }
            finally
            {
                await distributedLock.ReleaseLockAsync(lockKey);
            }
        }

        private int GetHttpParameterHasCode(ActionExecutingContext context)
        {
            var httpContext = context.HttpContext;
            string readFromJson = "";

            try
            {
                if (httpContext.Request.Method != "GET")
                {
                    readFromJson = context.ActionArguments == null ? "" : ListToJson.ToJson(context.ActionArguments);
                }
            }
            catch (Exception e)
            {
                _logger.Error(e.ToString());
            }
            string controllerName = context.RouteData.Values["controller"]?.ToString() ?? "";
            var queryString = httpContext.Request.QueryString;
            var bodyString = readFromJson == null ? string.Empty : readFromJson.ToString();

            var builder = $"{_lockPrefix}-{controllerName}-{queryString}-{bodyString}";
            return builder.GetHashCode();
        }
    }
}