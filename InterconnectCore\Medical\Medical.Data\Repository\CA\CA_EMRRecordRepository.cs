﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CA_EMRRecordRepository : ICA_EMRRecordRepository
    {
        private readonly CADBContext _cADBContext = null;

        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CA_EMRRecordRepository(CADBContext cADBContext)
        {
            _cADBContext = cADBContext;
        }

        public async Task<bool> SaveAsync(CA_EMRRecordInfo data)
        {
            try
            {
                _cADBContext.Add(data);

                return await _cADBContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex);

                _logger.Error("CA写入数据失败,异常数据:" + Common.ListToJson.ToJson(data));

                return false;
            }
        }
        public async Task<bool> SaveAsync(List<CA_EMRRecordInfo> datas)
        {
            try
            {
                await _cADBContext.AddRangeAsync(datas);

                return await _cADBContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex);

                _logger.Error("CA写入数据失败,异常数据:" + Common.ListToJson.ToJson(datas));

                return false;
            }
        }
    }
}