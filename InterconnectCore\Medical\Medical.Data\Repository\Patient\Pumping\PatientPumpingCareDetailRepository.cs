﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPumpingCareDetailRepository : IPatientPumpingCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPumpingCareDetailRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据维护主记录获取数据
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<List<PatientPumpingCareDetailInfo>> GetDataByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientPumpingCareDetailInfos.Where(m => m.PatientPumpingCareMainID == careMainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据维护主记录集合获取数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientPumpingCareDetailInfo>> GetDataByCareMainIDs(List<string> careMainIDs)
        {
            return await _medicalDbContext.PatientPumpingCareDetailInfos.Where(m => careMainIDs.Contains(m.PatientPumpingCareMainID) && m.DeleteFlag != "*").ToListAsync();
        }



    }
}
