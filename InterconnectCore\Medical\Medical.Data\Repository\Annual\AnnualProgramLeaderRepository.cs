﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.Annual;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class AnnualProgramLeaderRepository : IAnnualProgramLeaderRepository
    {
        private MedicalDbContext _dbContext = null;

        public AnnualProgramLeaderRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<AnnualProgramLeaderInfo>> GetProgramLeaderBySourceID(string sourceID)
        {
            return await _dbContext.AnnualProgramLeaderInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
