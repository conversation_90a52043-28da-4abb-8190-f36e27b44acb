﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Service.Interface;
using Medical.Common;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 第三方发送控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/SendThirdParty")]
    [EnableCors("any")]
    public class SendThirdPartyController : ControllerBase
    {
        private readonly ISendThirdPartyService _sendThirdPartyService;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="sendThirdPartyService">第三方发送服务</param>
        public SendThirdPartyController(
            ISendThirdPartyService sendThirdPartyService
            )
        {
            _sendThirdPartyService = sendThirdPartyService;
        }
        /// <summary>
        /// 获取或解绑仪器数据
        /// </summary>
        /// <param name="instrumentView">仪器视图数据字典</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [NoAuthorization]
        [Route("UnOrBindInstrument")]
        public async Task<IActionResult> GetPatientDeathData([FromBody] Dictionary<string, object> instrumentView)
        {
            var result = new ResponseResult
            {
                Data = await _sendThirdPartyService.UnOrBindInstrumentAsync(instrumentView)
            };
            return result.ToJson();
        }
    }
}
