﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientBasicDataRepository : IPatientBasicDataRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientBasicDataRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        public async Task<PatientBasicDataInfo> GetAsync(string chartNO)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNO && m.DeleteFlag != "*").Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public async Task<PatientBasicDataInfo> GetAsync(string hospitalID, string chartNO)
        {
            var data = await _dbContext.PatientBasicDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNO).Select(m => new PatientBasicDataInfo
            {
                PatientID = m.PatientID,
                HospitalID = m.HospitalID,
                ChartNo = m.ChartNo,
                InpatientNo = m.InpatientNo,
                IdentityID = m.IdentityID,
                PatientName = m.PatientName,
                Gender = m.Gender,
                DateOfBirth = m.DateOfBirth,
                TimeOfBirth = m.TimeOfBirth,
                BloodType = m.BloodType,
                NativePlace = m.NativePlace,
                RH = m.RH,
                LocalChartNO = m.LocalChartNO,
            }).FirstOrDefaultAsync();
            if (data?.HospitalID == "5")
            {
                data.IdentityID = EncryptionAndDecryption.DecryptStr(data?.IdentityID);
            }
            return data;
        }

        public PatientBasicDataInfo GetOnePatientBasic(string hospitalID, string chartNO)
        {
            var patientBasicData = _dbContext.PatientBasicDatas.Where(m => m.HospitalID == hospitalID && m.ChartNo == chartNO).FirstOrDefault();
            if (patientBasicData != null && patientBasicData?.HospitalID == "5")
            {
                patientBasicData.IdentityID = EncryptionAndDecryption.DecryptStr(patientBasicData?.IdentityID);
            }
            return patientBasicData;
        }
        public async Task<string> GetPatientNameAsync(string chartNO)
        {
            return await _dbContext.PatientBasicDatas.Where(m => m.ChartNo == chartNO && m.DeleteFlag != "*").Select(m => m.PatientName).FirstOrDefaultAsync();
        }
    }
}