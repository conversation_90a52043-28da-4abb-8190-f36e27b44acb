﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using NLog;

namespace InterconnectCore.Service
{
    public class NursingManagementService : INursingManagementService
    {
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly INurseShiftRepository _nurseShiftRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IStationShiftRepository _stationShiftRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IEmployeeDataRepository _employeeDataRepository;
        private readonly IUserRepository _userRepository;

        public NursingManagementService(
            IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IStationListRepository stationListRepository
            , INurseShiftRepository nurseShiftRepository
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , IStationShiftRepository stationShiftRepository
            , IEmployeeDataRepository employeeDataRepository
            , IUserRepository userRepository)
        {
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _stationListRepository = stationListRepository;
            _nurseShiftRepository = nurseShiftRepository;
            _unitOfWork = unitOfWork;
            _stationShiftRepository = stationShiftRepository;
            _employeeDataRepository = employeeDataRepository;
            _userRepository = userRepository;
        }
        #region 同步护理管理排班信息
        /// <summary>
        /// 同步排班数据
        /// </summary>
        /// <param name="nurseShiftList">排班数据</param>
        /// <returns></returns>
        public async Task<bool> SyncNursingManagementShiftData(List<NurseShiftView> nurseShiftList)
        {
            var employeeIDList = nurseShiftList.Select(m => m.EmployeeID).ToList();
            var employeeDepartmentSwitchList = await _employeeDepartmentSwitchRepository.GetEmployeeSwitchByemployeeIDs(employeeIDList);
            var stationList = await _stationListRepository.GetAllAsync();
            try
            {
                var firstNurseShift = nurseShiftList.OrderBy(m => m.ShiftDate).FirstOrDefault();
                var lastNurseShift = nurseShiftList.OrderBy(m => m.ShiftDate).LastOrDefault();
                var startDate = firstNurseShift.ShiftDate;
                var endDate = lastNurseShift.ShiftDate;
                var oldNurseShiftList = await _nurseShiftRepository.GeListByDate(startDate.Date, endDate.Date, employeeIDList);
                foreach (var nurseShift in nurseShiftList)
                {
                    var (successFlag, stationID, shiftList) = await GetShiftListAndStationID(employeeDepartmentSwitchList, stationList, nurseShift);
                    if (!successFlag) continue;
                    var (hasOldNurseShiftData, shiftInfo) = GetShiftByHRShift(oldNurseShiftList, nurseShift, shiftList);
                    if (hasOldNurseShiftData) continue;
                    InsertNurseShiftInfo(nurseShift, stationID, shiftInfo);
                }
                return _unitOfWork.SaveChanges() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"异常信息：{ex}");
            }
            return false;
        }
        /// <summary>
        /// 插入护士值班表
        /// </summary>
        /// <param name="nurseShift"></param>
        /// <param name="stationID"></param>
        /// <param name="shiftInfo"></param>
        private void InsertNurseShiftInfo(NurseShiftView nurseShift, int? stationID, StationShiftInfo shiftInfo)
        {
            var info = new NurseShiftInfo
            {
                ShiftDate = nurseShift.ShiftDate.Date,
                StationID = stationID.Value,
                HRShift = string.IsNullOrEmpty(nurseShift.HRShift) ? "" : nurseShift.HRShift,
                ManPower = 1,
                StationShiftID = shiftInfo.ID,
                SystemShift = shiftInfo.Shift,
                EmployeeID = nurseShift.EmployeeID,
                HRShiftName = nurseShift.HRShiftName,
                HRShiftSection = nurseShift.HRShiftSection,
                DeleteFlag = ""
            };
            info.Modify("TongBu");
            _unitOfWork.GetRepository<NurseShiftInfo>().Insert(info);
        }
        /// <summary>
        /// 根据HRShift选择对应的班次
        /// </summary>
        /// <param name="oldNurseShiftList"></param>
        /// <param name="nurseShift"></param>
        /// <param name="shiftList"></param>
        /// <returns></returns>
        private static (bool,StationShiftInfo) GetShiftByHRShift(List<NurseShiftInfo> oldNurseShiftList, NurseShiftView nurseShift, List<StationShiftInfo> shiftList)
        {
            var shiftInfo = new StationShiftInfo();
            if (string.IsNullOrEmpty(nurseShift.HRShift))
            {
                shiftInfo = shiftList.FirstOrDefault(m => m.ShiftName.Contains('日'));
            }
            if (!string.IsNullOrEmpty(nurseShift.HRShift) && nurseShift.HRShift.Contains("小夜"))
            {
                shiftInfo = shiftList.FirstOrDefault(m => m.ShiftName.Contains('中'));
            }
            if (!string.IsNullOrEmpty(nurseShift.HRShift) && !nurseShift.HRShift.Contains("小夜"))
            {
                shiftInfo = shiftList.FirstOrDefault(m => m.ShiftName.Contains('夜'));
            }
            var oldNurseShiftData = oldNurseShiftList.FirstOrDefault(m => m.ShiftDate == nurseShift.ShiftDate && m.SystemShift.Trim() == shiftInfo.Shift.Trim() && m.EmployeeID.Trim() == nurseShift.EmployeeID.Trim());
            if (oldNurseShiftData != null)
            {
                return (true, shiftInfo);
            }

            return (false, shiftInfo);
        }
        /// <summary>
        /// 获取病区ID和班别列表
        /// </summary>
        /// <param name="employeeDepartmentSwitchList"></param>
        /// <param name="stationList"></param>
        /// <param name="nurseShift"></param>
        /// <returns></returns>
        private async Task<(bool,int?, List<StationShiftInfo>)> GetShiftListAndStationID(List<EmployeeDepartmentSwitchInfo> employeeDepartmentSwitchList, List<StationListInfo> stationList, NurseShiftView nurseShift)
        {
            var employeeDepartmentSwitch = employeeDepartmentSwitchList.FirstOrDefault(m => m.EmployeeID == nurseShift.EmployeeID);
            if (employeeDepartmentSwitch == null || string.IsNullOrEmpty(employeeDepartmentSwitch.DepartmentCode))
            {
                return (false,null,null);
            }
            var stationID = stationList.FirstOrDefault(m => m.StationCode == employeeDepartmentSwitch.DepartmentCode)?.ID;
            if (!stationID.HasValue)
            {
                return (false, stationID, null);
            }
            var shiftList = await _stationShiftRepository.GetShiftByStationIDAsync(stationID.Value);
            if (shiftList.Count <= 0)
            {
                return (false, stationID, shiftList);
            }
            return (true, stationID, shiftList);
        }

        #endregion

        #region 同步护理管理人员数据
        /// <summary>
        /// 同步护理管理人员数据
        /// </summary>
        /// <param name="employeeList">人员信息</param>
        /// <returns></returns>
        public async Task<bool> SyncNursingManagementEmployeeData(List<EmployeeBasicDataView> employeeList)
        {
            try
            {
                if (employeeList == null || employeeList.Count <= 0)
                {
                    _logger.Warn("护理管理人员信息数据无需要同步的数据");
                    return false;
                }
                var employeeIDList = employeeList.Select(m => m.EmployeeID).ToList();
                var originalEmployeeList = await _employeeDataRepository.GetEmployeeListByEmployeeIDsAsync(employeeIDList);
                var userList = await _userRepository.GetUserByEmployeeIDsNoCache(employeeIDList);
                employeeList = employeeList.Where(m => m.BirthDay.HasValue).GroupBy(m=>m.EmployeeID).Select(m=>m.FirstOrDefault()).ToList();
                var addEmployeeDataList = new List<EmployeeDataInfo>();
                foreach (var employee in employeeList)
                {
                    var employeeData = originalEmployeeList.FirstOrDefault(m => m.EmployeeID == employee.EmployeeID);
                    var userInfo = userList.FirstOrDefault(m => m.UserID == employee.EmployeeID);
                    var employeeDataInfo = SetEmployeeData(employee, employeeData, userInfo);
                    //空，则为新增
                    if (employeeData == null)
                    {
                        addEmployeeDataList.Add(employeeDataInfo);
                    }
                }
                if (addEmployeeDataList.Count > 0)
                {
                    _unitOfWork.GetRepository<EmployeeDataInfo>().Insert(addEmployeeDataList);
                }
                return _unitOfWork.SaveChanges() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"护理管理人员信息数据同步失败，异常信息：{ex}");
            }
            return false;
        }
        /// <summary>
        /// 创建或修改人员信息
        /// </summary>
        /// <param name="employee">人员信息</param>
        /// <param name="employeeData">已同步人员信息</param>
        /// <param name="userInfo">用户信息</param>
        /// <returns></returns>
        private EmployeeDataInfo SetEmployeeData(EmployeeBasicDataView employee, EmployeeDataInfo employeeData,UserInfo userInfo)
        {
            bool insertFlag = false;
            if (employeeData == null)
            {
                insertFlag = true;
            }
            var (departmentID, stationID, shortCutSpell, lunarBirthday) = GetDepartAndOtherValue(employee, employeeData, userInfo);

            employeeData = employeeData ?? new EmployeeDataInfo();
            employeeData.EmployeeID = employee.EmployeeID;
            employeeData.EmployeeName = employee.EmployeeName;
            employeeData.ShortCutSpell = shortCutSpell;
            employeeData.Gender = employee.Gender;
            employeeData.Race = employee.Race;
            employeeData.Birthday = employee.BirthDay.Value;
            employeeData.LunarBirthday = lunarBirthday;
            employeeData.IDCard = employee.IDCard?.ToUpper();
            employeeData.DepartmentID = departmentID;
            employeeData.PositionID = employee.PositionID;
            employeeData.StationID = stationID;
            employeeData.HireCategory = employee.HireCategory;
            employeeData.RankID = employee.RankID;
            employeeData.EmployeeCategory = employee.EmployeeCategory;
            employeeData.NativePlace = employee.NativePlace;
            employeeData.Marriage = employee.Marriage;
            employeeData.HealthStatus = employee.HealthStatus;
            employeeData.MandarinSkill = employee.MandarinSkill;
            employeeData.Political = employee.Political;
            employeeData.EducationDegree = employee.EducationDegree;
            employeeData.GraduatedSchool = employee.GraduatedSchool;
            employeeData.TitleCategory = employee.TitleCategory;
            employeeData.Title = employee.Title;
            employeeData.JoinDate = employee.JoinDate;
            employeeData.EntryDate = employee.EntryDate?.Date;
            employeeData.Probation = employee.Probation;
            employeeData.FileID = employee.FileID;
            employeeData.CardID = employee.CardID;
            employeeData.PhysicianCardNo = employee.PhysicianCardNo;
            employeeData.PassportNo = employee.PassportNo;
            employeeData.LeaveStatus = employee.LeaveStatus;
            employeeData.LeaveDate = employee.LeaveDate;
            employeeData.LeaveMemo = employee.LeaveMemo;
            employeeData.Remark = employee.Remark;
            employeeData.Sort = employee.Sort;
            employeeData.AddPersonID = "TongBu";
            employeeData.AddDate = DateTime.Now;
            employeeData.HREmployeeID = employee.HREmployeeID;
            //新增或数据有修改，更新
            if (insertFlag || _unitOfWork.DbContext.Entry(employeeData).State == EntityState.Modified)
            {
                employeeData.Modify("TongBu");
            }
            return employeeData;
        }
        /// <summary>
        /// 获取需要的值
        /// 为便于解读，将三元复合表达式拆分为多个if...else...
        /// </summary>
        /// <param name="employee"></param>
        /// <param name="employeeData"></param>
        /// <param name="userInfo"></param>
        /// <returns></returns>
        private static (string,string,string,string) GetDepartAndOtherValue(EmployeeBasicDataView employee, EmployeeDataInfo employeeData, UserInfo userInfo)
        {
            string departmentID;
            string stationID;
            string shortCutSpell;
            string lunarBirthday;
            if (string.IsNullOrEmpty(employee.DepartmentID))
            {
                departmentID = userInfo?.DepartmentID.ToString() ?? string.Empty;
            }
            else
            {
                departmentID = employeeData.DepartmentID;
            }
            if (string.IsNullOrEmpty(employee.StationID))
            {
                stationID = userInfo?.StationID.ToString() ?? string.Empty;
            }
            else
            {
                stationID = employeeData.StationID;
            }
            if (string.IsNullOrEmpty(employee.ShortCutSpell))
            {
                shortCutSpell = userInfo?.PinYinCode ?? string.Empty;
            }
            else
            {
                shortCutSpell = employeeData.ShortCutSpell;
            }
            if (string.IsNullOrEmpty(employee.LunarBirthday))
            {
                lunarBirthday = employee.BirthDay?.ToString("MM-dd") ?? string.Empty;
            }
            else
            {
                lunarBirthday = employeeData.LunarBirthday;
            }
            return (departmentID,stationID,shortCutSpell,lunarBirthday);
        }
        #endregion
    }
}
