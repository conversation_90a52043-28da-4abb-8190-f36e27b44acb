﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PostTitleRepository : IPostTitleRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public PostTitleRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }


        public async Task<List<PostTitleInfo>> GetListByEmployeeDataIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.PostTitleInfos.Where(m => m.EmployeeDataID == employeeDataID
            && m.DeleteFlag != "*").ToListAsync();
        }
    }
}