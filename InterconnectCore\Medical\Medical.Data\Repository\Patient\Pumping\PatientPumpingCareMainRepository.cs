﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPumpingCareMainRepository : IPatientPumpingCareMainRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPumpingCareMainRepository(MedicalDbContext medicalDbContext)
        {
            _medicalDbContext = medicalDbContext;
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<PatientPumpingCareMainInfo> GetDataByCareMainID(string careMainID)
        {
            return await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.PatientPumpingCareMainID == careMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientPumpingCareMainInfo>> GetDataByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.PatientPumpingRecordID == recordID && m.DeleteFlag != "*").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToListAsync();
        }
        /// <summary>
        /// 根据inpatientID和执行时间获取数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="perfromData"></param>
        /// <returns></returns>
        public async Task<List<PatientPumpingCareMainInfo>> GetDataByInpatientID(string inpatientID, DateTime? perfromData)
        {
            var list = await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
            if (perfromData.HasValue)
            {
                list = list.Where(m => m.PerformDate == perfromData.Value).ToList();
            }
            return list.OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).ToList();
        }

        public async Task<List<PumpDrugView>> GetPerformData(string inpatientID, string hospitalID, int stationID, DateTime shiftDate)
        {
            var query = await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.ShiftDate == shiftDate && m.DeleteFlag != "*")
                         .Join(_medicalDbContext.PumpingDrugListInfos.Where(n => n.HospitalID == hospitalID && n.DeleteFlag != "*")
                         , m => new { m.PumpingDrugType, m.PumpingDrugKind }
                         , n => new { n.PumpingDrugType, n.PumpingDrugKind }
                         , (m, n) => new PumpDrugView
                         {
                             PumpRecordID = m.PatientPumpingRecordID,
                             DrugName = n.DrugName,
                             Unit = n.Unit,
                             PerformDate = m.PerformDate,
                             PerformTime = m.PerformTime,
                             Dosage = m.Dosage,
                             Speed = m.Speed
                         }).ToListAsync();

            return query;
        }
        /// <summary>
        /// 获取最后一条记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientPumpingCareMainInfo> GetLastDataByRecordID(string recordID)
        {
            return await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.PatientPumpingRecordID == recordID && m.DeleteFlag != "*").OrderBy(m => m.PerformDate).ThenBy(m => m.PerformTime).LastOrDefaultAsync();
        }
        public async Task<List<PatientPumpingCareMainInfo>> GetDataByRecordIDAndTime(string recordID, DateTime starTime, DateTime endTime)
        {
            var result = await _medicalDbContext.PatientPumpingCareMainInfos.Where(m => m.PatientPumpingRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
            result = result.Where(m => m.PerformDate.Date.Add(m.PerformTime) >= starTime && m.PerformDate.Date.Add(m.PerformTime) <= endTime).ToList();
            return result;
        }
    }
}
