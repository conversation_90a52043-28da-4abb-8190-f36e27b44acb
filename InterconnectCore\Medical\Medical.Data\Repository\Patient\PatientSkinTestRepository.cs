﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientSkinTestRepository : IPatientSkinTestRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientSkinTestRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }
        /// <summary>
        /// 根据主键获取皮试记录
        /// </summary>
        /// <param name="patientSkinTestID">主键</param>
        /// <returns></returns>
        public async Task<PatientSkinTestInfo> GetByID(string patientSkinTestID)
        {
            return await _dbContext.PatientSkinTestInfos.Where(m => m.PatientSkinTestID == patientSkinTestID).FirstOrDefaultAsync();
        }
    }
}