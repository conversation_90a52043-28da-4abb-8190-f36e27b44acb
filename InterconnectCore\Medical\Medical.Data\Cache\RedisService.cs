﻿using Medical.Data.Interface;
using Medical.ViewModels;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Medical.Data
{
    public class RedisService : IRedisService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDistributedCache _cache;
        private readonly IMemoryCache _memoryCache;
        private IOptions<SystemConfig> _sysConfig;
        private IConfiguration Configuration { get; }
        /// <summary>
        /// 设定内存过期时间为一周60*60*24*7
        /// </summary>
        private const int MEMORYCACHE_TIMEOUT = 604800;

        public RedisService(
            IDistributedCache cache,
             IMemoryCache memoryCache,
             IOptions<SystemConfig> options
            )
        {
            _cache = cache;
            _memoryCache = memoryCache;
            _sysConfig = options;
        }

        public async Task<bool> Add<T>(string key, int expirationSecondTime, T value)
        {
            byte[] val = null;
            if (value.ToString() != "")
            {
                string data = JsonConvert.SerializeObject(value);

                val = Encoding.UTF8.GetBytes(data);
            }
            if (val == null)
            {
                return false;
            }

            //添加缓存
            await _cache.SetAsync(key, val);
            //刷新缓存
            await _cache.RefreshAsync(key);
            return true;
        }

        public async Task<bool> Exists(string key)
        {
            bool check = true;

            byte[] val = await _cache.GetAsync(key);

            if (val == null || val.Length == 0)
            {
                check = false;
            }
            return check;
        }

        public async Task<string> GetAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                return "";
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                return Encoding.UTF8.GetString(value);
            }
            return "";
        }

        public async Task<T> GetAsync<T>(string key)
        {
            T datas = default(T);
            if (string.IsNullOrEmpty(key))
            {
                return datas;
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                string str = Encoding.UTF8.GetString(value);
                datas = JsonConvert.DeserializeObject<T>(str);
            }
            return datas;
        }


        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, Func<Task<T>> datas)
        {
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke();

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<object> GetOrCreateAsync(string key, int expirationSecondTime, int language, Func<int, Task<object>> datas)
        {
            var memoryData = await GetAsync(key);

            if (memoryData != null && !string.IsNullOrEmpty(memoryData.ToString()))
            {
                return memoryData;
            }

            var cacheDatas = await datas.Invoke(language);

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, Func<string, Task<T>> datas)
        {
            //获取内存缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                if (memoryData is List<object> temp && temp.Count() > 0)
                {
                    return memoryData;
                }
                if (memoryData is not List<object>)
                {
                    return memoryData;
                }
            }

            //查新数据库
            var cacheDatas = await datas.Invoke(hospitalID);

            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }
        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, int language, Func<string, int, Task<T>> datas)
        {
            //获取内存缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke(hospitalID, language);
            await Add(key, expirationSecondTime, cacheDatas);

            return cacheDatas;
        }

        public async Task<bool> Update<T>(string key, int outSecondTime, T value)
        {
            bool check = false;
            if (value == null)
            {
                return check;
            }
            if (key != "" || key != null)
            {
                if (await Remove(key))
                {
                    check = await Add(key, outSecondTime, value.ToString());
                }
            }
            return check;
        }

        public async Task<bool> Remove(string key)
        {
            bool check = false;
            if (key != "" || key != null)
            {
                _cache.Remove(key);
                if (!await Exists(key))
                {
                    check = true;
                }
            }
            return check;
        }

        /// <summary>
        /// 模糊查找Key,并且清除,all 清除所有缓存
        /// </summary>
        /// <param name="keyStr"></param>
        public async Task<List<string>> RemoveCacheByFuzzyKey(string keyStr, string hospitalID)
        {
            var keys = GetDateBaseKeys();
            var result = new List<string>();
            hospitalID = "_H" + hospitalID;
            foreach (var item in keys)
            {
                if (!item.Contains(hospitalID) && item.Contains("_H"))
                {
                    continue;
                }
                if (item.Contains(keyStr) || keyStr.ToUpper() == "ALL")
                {
                    var flag = await Remove(item);
                    if (flag)
                    {
                        result.Add(item);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取Redis链接
        /// </summary>
        /// <returns></returns>
        public List<string> GetDateBaseKeys()
        {
            var redisConnection = _sysConfig.Value.RedisConnection;
            // 创建 Redis 连接   
            var options = ConfigurationOptions.Parse(redisConnection);
            var connection = ConnectionMultiplexer.Connect(options);
            var keys = connection.GetServers().First().Keys().ToList();
            connection.Close();
            var keysStr = new List<string>();
            foreach (var item in keys)
            {
                keysStr.Add(item.ToString());
            }
            return keysStr;
        }

    }
}
