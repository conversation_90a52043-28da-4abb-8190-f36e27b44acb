﻿using System.ComponentModel.DataAnnotations;

namespace InterconnectCore.ViewModels
{
    public class EmployeeBasicDataView
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [Required(ErrorMessage = "员工ID不能为空")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 拼音码
        /// </summary>
        public string ShortCutSpell { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 籍贯
        /// </summary>
        public string NativePlace { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string IDCard { get; set; }
        /// <summary>
        /// 出生日期（时间戳）
        /// </summary>
        public DateTime? BirthDay { get; set; }
        /// <summary>
        /// 农历生日
        /// </summary>
        public string LunarBirthday { get; set; }
        /// <summary>
        /// 民族
        /// </summary>
        public string Race { get; set; }
        /// <summary>
        /// 婚姻状况
        /// </summary>
        public string Marriage { get; set; }
        /// <summary>
        /// 职工性质
        /// </summary>
        public string HireCategory { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 最高学历
        /// </summary>
        public string EducationDegree { get; set; }
        /// <summary>
        /// 最高学历毕业学校
        /// </summary>
        public string GraduatedSchool { get; set; }
        /// <summary>
        /// 入职日期（时间戳）
        /// </summary>
        public DateTime? EntryDate { get; set; }
        /// <summary>
        /// 参加工作时间（时间戳）
        /// </summary>
        public DateTime? JoinDate { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string DepartmentID { get; set; }
        /// <summary>
        /// 职务ID
        /// </summary>
        public string PositionID { get; set; }
        /// <summary>
        /// 护士站ID
        /// </summary>
        public string StationID { get; set; }
        /// <summary>
        /// 级别ID
        /// </summary>
        public string RankID { get; set; }
        /// <summary>
        /// 员工性质ID
        /// </summary>
        public string EmployeeCategory { get; set; }
        /// <summary>
        /// 健康状况
        /// </summary>
        public string HealthStatus { get; set; }
        /// <summary>
        /// 普通话水平
        /// </summary>
        public string MandarinSkill { get; set; }
        /// <summary>
        /// 政治面貌
        /// </summary>
        public string Political { get; set; }
        /// <summary>
        /// 职称分类
        /// </summary>
        public string TitleCategory { get; set; }
        /// <summary>
        /// 试用期
        /// </summary>
        public DateTime? Probation { get; set; }
        /// <summary>
        /// 档案编号
        /// </summary>
        public string FileID { get; set; }
        /// <summary>
        /// CardID
        /// </summary>
        public string CardID { get; set; }
        /// <summary>
        /// 诊疗卡号
        /// </summary>
        public string PhysicianCardNo { get; set; }
        /// <summary>
        /// 护照号码
        /// </summary>
        public string PassportNo { get; set; }
        /// <summary>
        /// 离职状态
        /// </summary>
        public string LeaveStatus { get; set; }
        /// <summary>
        /// 离职日期
        /// </summary>
        public DateTime? LeaveDate { get; set; }
        /// <summary>
        /// 离职备注
        /// </summary>
        public string LeaveMemo { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 人事部门ID
        /// </summary>
        public string HREmployeeID { get; set; }
    }
}
