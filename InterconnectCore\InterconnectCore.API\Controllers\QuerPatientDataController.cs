﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Common;
using InterconnectCore.QueryService.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 患者信息查询接口
    /// </summary>
    [Produces("application/json")]
    [Route("api/QuerPatientData")]
    [EnableCors("any")]
    public class QuerPatientDataController : ControllerBase
    {
        private readonly IQueryPatientDataService _queryPatientDataService;
        public QuerPatientDataController(
            IQueryPatientDataService queryPatientDataService
         )
        {
            _queryPatientDataService = queryPatientDataService;
        }

        /// <summary>
        /// 获取患者信息
        /// 厦门国家传染病智能监测预警前置接口调用，获取死亡患者信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("GetPatientDeathData")]
        public async Task<ResponseResult> GetPatientDeathData(string caseNumber)
        {
            var result = new ResponseResult
            {
                Data = await _queryPatientDataService.GetPatientDeathData(caseNumber)
            };
            return result;
        }
    }
}
