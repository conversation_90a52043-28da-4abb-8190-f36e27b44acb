﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class BodyPartListRepository : IBodyPartListRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public BodyPartListRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<BodyPartListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.BodyPartListInfos.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.BodyPartList.GetKey(_sessionCommonServer);
        }

        public async Task<BodyPartListInfo> GetAsync(int id)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Where(t => t.ID == id).FirstOrDefault();
            }
            return null;
        }

        public async Task<List<BodyPartListInfo>> GetDataByIDArr(List<string> idArr)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Where(t => idArr.Contains(t.ID.ToString().Trim())).ToList();
            }
            return null;
        }

        public async Task<List<BodyPartListInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<BodyPartListInfo>;
        }

        public async Task<string> GetBodyPartNameAsync(int bodyPartID)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Find(t => t.ID == bodyPartID)?.BodyPartName;
            }
            return null;
        }
    }
}