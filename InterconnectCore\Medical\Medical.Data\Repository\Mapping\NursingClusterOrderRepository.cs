﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingClusterOrderRepository : INursingClusterOrderRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public NursingClusterOrderRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingClusterOrderInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.NursingClusterOrderInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsyncByType(string clusterType)
        {
            var datas = await GetCacheAsync() as List<NursingClusterOrderInfo>;
            return datas.Where(t => t.ClusterType == clusterType && t.DeleteFlag != "*").ToList();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsync(string sourceTable)
        {
            var datas = await GetCacheAsync() as List<NursingClusterOrderInfo>;
            return datas.Where(t => t.SourceTable == sourceTable && t.DeleteFlag != "*").ToList();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsync(string[] sourceTables)
        {
            var datas = await GetCacheAsync() as List<NursingClusterOrderInfo>;
            return datas.Where(t => sourceTables.Contains(t.SourceTable) && t.DeleteFlag != "*").ToList();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsyncByType(string clusterType, int departmentListID, int stationID)
        {
            var data = await GetAsyncByType(clusterType);
            var departmentSetting = data.Where(m => m.DepartmentListID == departmentListID).ToList();
            var stationSetting = data.Where(m => m.StationID == stationID).ToList();
            if (departmentSetting.Count > 0)
            {
                return departmentSetting;
            }
            if (stationSetting.Count > 0)
            {
                return stationSetting;
            }
            return data.Where(m => m.DepartmentListID == 999999 && m.StationID == 999999).ToList();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsync(string sourceTable, int departmentListID, int stationID)
        {
            var data = await GetAsync(sourceTable);
            var departmentSetting = data.Where(m => m.DepartmentListID == departmentListID).ToList();
            var stationSetting = data.Where(m => m.StationID == stationID).ToList();
            if (departmentSetting.Count > 0)
            {
                return departmentSetting;
            }
            if (stationSetting.Count > 0)
            {
                return stationSetting;
            }
            return data.Where(m => m.DepartmentListID == 999999 && m.StationID == 999999).ToList();
        }

        public async Task<List<NursingClusterOrderInfo>> GetAsync(string[] sourceTables, int departmentListID, int stationID)
        {
            var data = await GetAsync(sourceTables);
            var departmentSetting = data.Where(m => m.DepartmentListID == departmentListID).ToList();
            var stationSetting = data.Where(m => m.StationID == stationID).ToList();
            if (departmentSetting.Count > 0)
            {
                return departmentSetting;
            }
            if (stationSetting.Count > 0)
            {
                return stationSetting;
            }
            return data.Where(m => m.DepartmentListID == 999999 && m.StationID == 999999).ToList();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingClusterOrder.GetKey(_sessionCommonServer);
        }

        public async Task<List<int>> GetAddScheduleNowProblemIDsAsync()
        {
            var datas = await GetCacheAsync() as List<NursingClusterOrderInfo>;
            return datas.Where(t => t.AddScheduleNowFlag.HasValue && t.AddScheduleNowFlag.Value).Select(m => m.NursingProblemID).ToList();
        }
    }
}