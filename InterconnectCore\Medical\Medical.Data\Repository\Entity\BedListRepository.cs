﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class BedListRepository : IBedListRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public BedListRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }


        /// <summary>
        /// 获取所有床位列表（非缓存、非异步）
        /// </summary>
        /// <param name="containsDelete">是否包含已删除数据，默认不包含</param>
        /// <returns></returns>
        public List<BedListInfo> GetBedListNoCacheSynchronize(bool containsDelete = false)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return _medicalDbContext.BedListInfos.Where(!containsDelete, t => t.DeleteFlag != "*")
                .Where(t => t.HospitalID == hospitalID).ToList();
        }

        /// <summary>
        /// 获取所有床位列表（非缓存）
        /// </summary>
        /// <param name="containsDelete">是否包含已删除数据，默认不包含</param>
        /// <returns></returns>
        public async Task<List<BedListInfo>> GetBedListNoCache(bool containsDelete = false)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.BedListInfos.Where(!containsDelete, t => t.DeleteFlag != "*")
                .Where(t => t.HospitalID == hospitalID).ToListAsync();
        }


        /// <summary>
        /// 根据ID获取床位信息
        /// </summary>
        /// <param name="bedID">床位ID</param>
        /// <returns></returns>
        public async Task<BedListInfo> GetByID(int bedID)
        {
            var datas = await this.GetAllAsync<BedListInfo>();
            return datas?.Find(t => t.ID == bedID);
        }
        /// <summary>
        /// 根据ID获取床位信息（非缓存，含删除）
        /// </summary>H
        /// <param name="bedID">床位ID</param>
        /// <returns></returns>
        public async Task<BedListInfo> GetBedByIDNoCacheIncludeDeleted(int bedID)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.BedListInfos.Where(t => t.ID == bedID && t.HospitalID == hospitalID).FirstOrDefaultAsync();
        }


        /// <summary>
        /// 根据病区ID获取床位列表
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <returns></returns>
        public async Task<List<BedListInfo>> GetByStationID(int stationID, bool disableFlag = false)
        {
            var datas = await this.GetAllAsync<BedListInfo>();
            return disableFlag ? datas.FindAll(m => m.StationID == stationID) : datas.FindAll(m => m.StationID == stationID && m.DisableFlag != "*");
        }
        /// <summary>
        /// 根据病区ID获取床位列表（非緩存，含删除）
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <returns></returns>
        public async Task<List<BedListInfo>> GetByStationIDNoCacheContainDelete(int stationID)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.BedListInfos.Where(t => t.StationID == stationID && t.HospitalID == hospitalID)
                .ToListAsync();
        }


        /// <summary>
        /// 根据病区ID、床位号获取床位信息
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <returns></returns>
        public async Task<BedListInfo> GetByStationIDAndBedNumber(int stationID, string bedNumber)
        {
            var datas = await this.GetAllAsync<BedListInfo>();
            return datas?.Find(t => t.StationID == stationID && t.BedNumber.Trim() == bedNumber);
        }
        /// <summary>
        /// 根据病区ID、床位号获取床位信息（非缓存）
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <returns></returns>
        public async Task<BedListInfo> GetByStationIDAndBedNumberNoCache(int stationID, string bedNumber)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.BedListInfos.Where(t => t.HospitalID == hospitalID && t.StationID == stationID &&
            t.BedNumber == bedNumber && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据病区ID、床位号获取已开启的床位ID（非缓存）
        /// </summary>
        /// <param name="stationID">病区ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <returns></returns>
        public async Task<int> GetBedIDByStationIDAndBedNumber(int stationID, string bedNumber)
        {
            var datas = await this.GetAllAsync<BedListInfo>();
            var bedID = datas.Find(m => m.StationID == stationID && m.BedNumber == bedNumber && m.DisableFlag != "*")?.ID;
            return bedID ?? 0;
        }
        /// <summary>
        /// 获取所有床位信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<BedListInfo>> GetAllAsync()
        {
            return (List<BedListInfo>)await GetCacheAsync();
        }
        /// <summary>
        /// 获取所有床位信息（非缓存）
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<BedListInfo>> GetAllAsyncNoCache(string hospitalID)
        {
            return _medicalDbContext.BedListInfos.Where(t => t.DeleteFlag != "*" && t.HospitalID == hospitalID).ToList();
        }
        /// <summary>
        /// 获取最新床位信息
        /// </summary>
        /// <returns></returns>
        public async Task<BedListInfo> GetLastData()
        {
            return await _medicalDbContext.BedListInfos.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取床位最大ID
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxAsync()
        {
            var id = await _medicalDbContext.BedListInfos.MaxAsync(m => m.ID);
            if (id == 0)
            {
                return 1;
            }
            else
            {
                return id + 1;
            }
        }
        /// <summary>
        /// 获取床位最大ID
        /// </summary>
        /// <returns></returns>
        public int GetMaxID()
        {
            var id = _medicalDbContext.BedListInfos.Max(m => m.ID);
            if (id == 0)
            {
                return 1;
            }
            else
            {
                return id + 1;
            }
        }

        public async Task<int> GetBedCount(int stationID)
        {
            var key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _medicalDbContext.BedListInfos.CountAsync(t => t.StationID == stationID && t.HospitalID == hospitalID && t.DeleteFlag != "*");
        }

        #region 缓存操作

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<BedListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.BedListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Bed.GetKey(_sessionCommonServer);
        }
        #endregion
    }
}