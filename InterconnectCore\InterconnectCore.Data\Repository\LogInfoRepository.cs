﻿using InterconnectCore.Data.Context;
using InterconnectCore.Data.Interface;
using InterconnectCore.Models;

namespace InterconnectCore.Data.Repository
{
    public class LogInfoRepository : ILogInfoRepository
    {
        private DataOutContext _DataOutConnection = null;


        public LogInfoRepository(DataOutContext db)
        {
            _DataOutConnection = db;
        }

        /// <summary>
        /// 删除历史同步Log
        /// </summary>
        /// <returns></returns>
        public bool DelAsync(int logSaveDays)
        {
            try
            {
                //删除N天前的数据
                var Logdata = _DataOutConnection.LogInfos.Where(m => m.Datetimes < m.Datetimes.AddDays(-logSaveDays)).ToList();
                _DataOutConnection.LogInfos.RemoveRange(Logdata);
                _DataOutConnection.SaveChanges();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取测试数据
        /// </summary>
        /// <returns></returns>
        public List<LogInfo> GetLog(string guid)
        {
            var Logdata = _DataOutConnection.LogInfos.Where(m => m.Guid == guid).ToList();
            return Logdata;
        }
    }
}
