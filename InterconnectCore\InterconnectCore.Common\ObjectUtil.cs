﻿namespace InterconnectCore.Common
{
    /// <summary>
    /// 对象工具
    /// </summary>
    public static class ObjectUtil
    {
        #region 判断对象是否为空

        /// <summary>
        /// 判断对象是否为空，为空返回true
        /// </summary>
        /// <typeparam name="T">要验证的对象的类型</typeparam>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty<T>(T data)
        {
            if (data == null)
            {
                return true;
            }
            //空串
            if (data.GetType() == typeof(string))
            {
                if (string.IsNullOrEmpty(data.ToString().Trim()))
                {
                    return true;
                }
            }
            //不为空
            return false;
        }

        /// <summary>
        /// 判断对象集合是否为空，为空返回true
        /// </summary>
        /// <typeparam name="T">要验证的对象的类型</typeparam>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty<T>(List<T> data)
        {
            if (data == null)
            {
                return true;
            }
            //空集合
            if (data.Count <= 0)
            {
                return true;
            }
            //不为空
            return false;
        }

        /// <summary>
        /// 判断对象是否为空，为空返回true
        /// </summary>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty(object data)
        {
            if (data == null)
            {
                return true;
            }
            //空串
            if (data.GetType() == typeof(string))
            {
                if (string.IsNullOrEmpty(data.ToString().Trim()))
                {
                    return true;
                }
            }
            //不为空
            return false;
        }

        #endregion 判断对象是否为空
    }
}