﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EMRSourceRepository : IEMRSourceRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public EMRSourceRepository(
              MedicalDbContext medicalDb
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = medicalDb;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }
        public async Task<List<EMRSourceInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<EMRSourceInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EMRSourceInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.EMRSourceInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据ID集合获取
        /// </summary>
        /// <param name="emrSourceIDs"></param>
        /// <returns></returns>
        public async Task<List<FieldView>> GetEMRSourceByIDs(int[] emrSourceIDs)
        {
            var emrSourceInfos = await this.GetAllAsync<EMRSourceInfo>();
            return emrSourceInfos.Where(m => emrSourceIDs.Contains(m.ID)).Select(m => new FieldView
            {
                ID = m.ID,
                ShowName = m.LocalShowName,
                Unit = m.Unit
            }).ToList();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EMRSource.GetKey(_sessionCommonServer);
        }
    }
}
