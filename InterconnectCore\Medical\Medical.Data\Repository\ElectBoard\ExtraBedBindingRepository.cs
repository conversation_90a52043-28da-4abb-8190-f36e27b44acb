﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.ElectBoard;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.ElectBoard
{
    public class ExtraBedBindingRepository : IExtraBedBindingRepository
    {
        private MedicalDbContext _medicalDBContext;
        private readonly IMemoryCache _memoryCache;
        private readonly IBedListRepository _bedListRepository;

        public ExtraBedBindingRepository(
              MedicalDbContext medicalDbContext
            , IMemoryCache memoryCache
            , IBedListRepository bedListRepository
            )
        {
            _medicalDBContext = medicalDbContext;
            _memoryCache = memoryCache;
            _bedListRepository = bedListRepository;
        }

        public async Task<string[]> GetExtraEedDataAsync(int stationID)
        {
            var bedList = await _bedListRepository.GetAllAsync<BedListInfo>();
            var datas = await _medicalDBContext.ExtraBedBindingInfos.Where(m => m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
            var queryDatas = (from m in datas
                              join b in bedList on m.ExtraBedID equals b.ID
                              orderby b.Sort ascending
                              select new ExtraBedBindingInfo
                              {
                                  ExtraBedNumber = m.ExtraBedNumber,
                                  BedNumber = m.BedNumber
                              }).ToList();
            return queryDatas.Select(m => m.ExtraBedNumber + "(" + m.BedNumber + ")").ToArray();

        }

        public async Task<List<ExtraBedBindingInfo>> GetAllExtraBedBinding(int stationID)
        {


            dynamic query = new DynamicDictionary();
            query.stationID = stationID;
            return await _medicalDBContext.ExtraBedBindingInfos.Where(m => m.DeleteFlag != "*" && m.StationID == stationID).ToListAsync();

        }

        public async Task<ExtraBedBindingInfo> GetExtraBedBindingById(string extraBedBindingID)
        {

            return await _medicalDBContext.ExtraBedBindingInfos.Where(t => t.ExtraBedBindingID == extraBedBindingID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }


        public async Task<ExtraBedBindingInfo> GetExtraBedBindingData(int extraBedID, int stationID)
        {
            return await _medicalDBContext.ExtraBedBindingInfos.Where(t => t.ExtraBedID == extraBedID && t.StationID == stationID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
