﻿using InterconnectCore.Data.Interface;
using InterconnectCore.Models;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Interface;
using Medical.Data.Repository;
using Medical.Models;
using Medical.ViewModels.View;
using Medical.ViewModels;
using Microsoft.Extensions.Options;
using NLog;
using Medical.Data.Context;

namespace InterconnectCore.Services
{
    public class PatientAllergyService : IPatientAllergyService
    {
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();
        //Medical
        private readonly  IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly  IInpatientDataRepository _inpatientDataRepository;
        private readonly  IAllergyBasicRepository _allergyBasicRepository;
        private readonly  IPatientAllergyRepository _patientAllergyRepository;
        private readonly MedicalDbContext _medicalDbContext = null;

        //InterconnectCore
        private readonly ISyncLogService _syncLogService;
        private readonly CommonHelper _commonHelper;
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private readonly IRequestApiService _requestApiService;

        #region 常量
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";
        #endregion

        public PatientAllergyService( IAppConfigSettingRepository appConfigSettingRepository
            ,IInpatientDataRepository inpatientDataRepository
            ,IAllergyBasicRepository allergyBasicRepository
            ,IPatientAllergyRepository patientAllergyRepository
            ,MedicalDbContext medalicalDbContext
            ,ISyncLogService syncLogService
            ,CommonHelper commonHelper
            ,IOptions<ViewModels.SystemConfig> config
            ,IRequestApiService requestApiService
            )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _allergyBasicRepository = allergyBasicRepository;
            _patientAllergyRepository = patientAllergyRepository;
            _medicalDbContext = medalicalDbContext;
            _syncLogService = syncLogService;
            _commonHelper = commonHelper;
            _config = config;   
            _requestApiService = requestApiService;
        }
        /// <summary>
        /// 同步患者过敏史信息
        /// </summary>
        /// <param name="patientAllergicViewList"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> SyncPatientAllergic(List<PatientAllergicView> patientAllergicViewList)
        {
            if (patientAllergicViewList == null || patientAllergicViewList.Count <= 0)
            {
                return false;
            }
            var flag = await SyncOnePatientAllergyData(patientAllergicViewList);
            return flag;
        }

        /// <summary>
        /// 同步患者过敏数据
        /// </summary>
        /// <param name="hisPatientAllergyDataViews"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task<bool> SyncOnePatientAllergyData(List<PatientAllergicView> hisPatientAllergyDataViews)
        {
            var inpatient = await _inpatientDataRepository.GetInpatientByChartNo(hisPatientAllergyDataViews[0].ChartNo, _config.Value.HospitalID);
            //判断病人是否存在
            if (inpatient == null)
            {
                _logger.Info("过敏数据同步，InPatientList CaseNumber [" + hisPatientAllergyDataViews[0].CaseNumber + "]   查询信息错误!");
                return false;
            }
            //判断这个过敏是否存在
            var patientAllergy = await _patientAllergyRepository.GetListByCaseNumber(inpatient.CaseNumber);
            var patientDruglsit = patientAllergy.Select(m => m.AllergyBasicID).ToList();
            var allergyBasic = await _allergyBasicRepository.GetAllergyBasic();
            var allergyDataViews = new List<AddAllergyDataView>();
            var deleteData = new List<string>();
            foreach (var item in hisPatientAllergyDataViews)
            {
                if (string.IsNullOrEmpty(item.AllergenName))
                {
                    continue;
                }
                var allergy = allergyBasic.Where(m => item.AllergenName.Trim().Contains(m.AllergyName.Trim())).FirstOrDefault();
                if (allergy != null && patientDruglsit.Contains(allergy.AllergyBasicID))
                {
                    continue;
                }
                var patientAllergyTemp = patientAllergy.Find(m => m.CustomName == item.AllergenName || item.AllergenName.Contains(m.CustomName));
                if (patientAllergyTemp != null && (item.CancelDate.HasValue || item.DeleteTime.HasValue))
                {
                    deleteData.Add(patientAllergyTemp.PatientAllergyID);
                }
                else
                {
                    var allergyDataView = CreateAddAllergyDataView(item, allergy);
                    allergyDataViews.Add(allergyDataView);
                }
            }
            if (allergyDataViews.Count <= 0 && deleteData.Count <= 0)
            {
                return false;
            }
            var saveView = new AllergySaveView
            {
                ChartNo = inpatient.ChartNo,
                InpatientID = inpatient.ID,
                AddAllergy = allergyDataViews,
                DelAllergy = deleteData,
                UpdateAllergy = new List<UpdateAllergy>()
            };
            try
            {
                await _requestApiService.RequestAPI("SaveAllergy", ListToJson.ToJson(saveView), inpatient.ID, inpatient.CaseNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "过敏数据同步，InPatientList CaseNumber [" + hisPatientAllergyDataViews[0].CaseNumber + "]   保存数据失败!");
                return false;
            }
        }

        /// <summary>
        /// 创建患者过敏史数据
        /// </summary>
        /// <param name="hISPatientAllergyDataView"></param>
        /// <param name="allergy"></param>
        /// <returns></returns>
        private static AddAllergyDataView CreateAddAllergyDataView(PatientAllergicView hISPatientAllergyDataView, AllergyData allergy)
        {

            //单病人
            var allergyDataView = new AddAllergyDataView
            {
                CaseNumber = hISPatientAllergyDataView.CaseNumber,
                ChartNo = hISPatientAllergyDataView.ChartNo,
                UserID = MODIFYPERSONID,
                AllergyBasicID = 9999,
                //他们的过敏药物类型可能与我们的有所区别，这里可能不合适
                AllergyType = hISPatientAllergyDataView.AllergyType,
                CustomName = hISPatientAllergyDataView.AllergenName,
                AddDate = DateTime.Now.Date,
                AddTime = DateTime.Now.TimeOfDay,
                SourceID = MODIFYPERSONID
            };
            if (allergy != null)
            {
                allergyDataView.CustomName = allergy.AllergyName;
                allergyDataView.AllergyType = allergy.AllergyType;
                allergyDataView.AllergyBasicID = allergy.AllergyBasicID;
            }
            else
            {
                allergyDataView.AllergyType = hISPatientAllergyDataView.AllergyType == "1" ? "D" : hISPatientAllergyDataView.AllergyType == "2" ? "F" : "O";
            }
            return allergyDataView;
        }

    }
}