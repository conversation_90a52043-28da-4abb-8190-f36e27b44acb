﻿using InterconnectCore.Models;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IPatientBasicService
    {
        /// <summary>
        /// 病人信息同步
        /// </summary>
        /// <returns></returns>
        /// <returns></returns>
        Task<bool> SynchronizationMain(string ChartNo);

        /// <summary>
        /// 同步病人基本信息
        /// </summary>
        /// <param name="originalData"></param>
        /// <returns></returns>
        Task<List<PatientBasicDataInfo>> SyncPatientBaseDetail(List<PatientBasicInfo> originalData);
    }
}
