﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Medical.Data.Repository
{
    public class NurseShiftDataRepository : INurseShiftDataRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public NurseShiftDataRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<NurseShiftDataInfo>> GetAllAsync(DateTime startDate, DateTime endDate, int stationID)
        {
            var data = await _medicalDbContext.NurseShiftDatas.Where(m => m.DeleteFlag != "*"
            && m.StatisticsDate >= startDate
            && m.StatisticsDate <= endDate
            && m.StationID == stationID).ToListAsync();

            var NurseShiftDataList = new List<NurseShiftDataInfo>();
            NurseShiftDataList = data.OrderBy(m => m.StatisticsDate).ThenBy(m => m.StationID).ThenBy(m => m.Shifts).ToList();

            return NurseShiftDataList;
        }

        public async Task<List<NurseShiftDataInfo>> GetAsync(DateTime startDate, DateTime endDate)
        {
            var data = await _medicalDbContext.NurseShiftDatas.Where(m => m.DeleteFlag != "*"
            && m.StatisticsDate >= startDate
            && m.StatisticsDate <= endDate).ToListAsync();

            return data;
        }

        public async Task<List<NurseShiftDataInfo>> GetByDate(DateTime date)
        {
            return await _medicalDbContext.NurseShiftDatas.Where(m => m.StatisticsDate.Date == date.Date).ToListAsync();
        }

        /// <summary>
        /// 获取当天数据量
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<int> getCountByDate(DateTime dateTime)
        {
            return await _medicalDbContext.NurseShiftDatas.Where(m => m.StatisticsDate == dateTime.Date && m.DeleteFlag != "*").CountAsync();
        }
    }
}
