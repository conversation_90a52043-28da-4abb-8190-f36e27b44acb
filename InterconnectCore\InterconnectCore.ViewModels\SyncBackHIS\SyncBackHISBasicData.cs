﻿namespace InterconnectCore.ViewModels
{
    public class SyncBackHISBasicData
    {
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        public string HospitalCode { get; set; }
        /// <summary>
        /// 医院名称
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 患者主键
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病人姓名 
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 入院次数
        /// </summary>
        public int NumberOfAdmissions { get; set; }
        /// <summary>
        /// 院区单位编码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 病区名称
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
    }
}
