﻿/*
 * 2022-05-22 2643 我需要调整专项护理明细写入逻辑（反比写入），并可以将操作记录于PatientNursingRecordDetailLog -En
 */

using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientPainCareDetailRepository : IPatientPainCareDetailRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public PatientPainCareDetailRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据维护主表ID查询明细数据
        /// </summary>
        /// <param name="careMainID">维护主表ID</param>
        /// <param name="deleteFlag">是否进行删除标记过滤，默认是</param>
        /// <returns></returns>
        public async Task<List<PatientPainCareDetailInfo>> GetByPainCareMainID(string careMainID, bool deleteFlag = true)
        {
            if (deleteFlag)
            {
                return await _medicalDbContext.PatientPainCareDetailInfos.Where(t => t.PatientPainCareMainID == careMainID && t.DeleteFlag != "*").ToListAsync();
            }
            else
            {
                return await _medicalDbContext.PatientPainCareDetailInfos.Where(t => t.PatientPainCareMainID == careMainID).ToListAsync();
            }
        }

        public async Task<List<Detail>> GetPatientPainCareToHandover(string inpatientID, int stationID, DateTime startTime, DateTime endTime)
        {
            var careMainIDs = await _medicalDbContext.PatientPainCareMainInfos.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                               && m.BringToShift == true && m.DeleteFlag != "*"
                               && m.AssessDate >= startTime.Date && m.AssessDate <= endTime.Date)
             .Select(m => m.PatientPainCareMainID).ToListAsync();
            if (careMainIDs.Count <= 0)
            {
                return new List<Detail>();
            }
            return await _medicalDbContext.PatientPainCareDetailInfos.Where(t => careMainIDs.Contains(t.PatientPainCareMainID) && t.DeleteFlag != "*")
                .Select(m => new Detail
                {
                    ID = m.PatientPainCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                }).ToListAsync();
        }

        /// <summary>
        /// 根据维护记录ID集合获取明细
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<Detail>> GetDetailsByMainIDs(params string[] careMainIDs)
        {


            return await _medicalDbContext.PatientPainCareDetailInfos.Where(t => careMainIDs.Contains(t.PatientPainCareMainID) && t.DeleteFlag != "*")
                .Select(m => new Detail
                {
                    ID = m.PatientPainCareMainID,
                    AssessListID = m.AssessListID,
                    AssessValue = m.AssessValue
                }).ToListAsync();
        }
    }
}
