﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScoreInterventionRepository : IPatientScoreInterventionRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public PatientScoreInterventionRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<PatientScoreInterventionInfo>> GetByScoreMainIDs(List<string> scoreMainIDs)
        {
            return await _medicalDbContext.PatientScoreInterventionInfos.Where(m =>
                 scoreMainIDs.Contains(m.PatientScoreMainID) && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScoreInterventionInfo>> GetByScoreMainID(string scoreMainID)
        {
            return await _medicalDbContext.PatientScoreInterventionInfos.Where(m =>
                m.PatientScoreMainID == scoreMainID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientScoreInterventionInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientScoreInterventionInfos.Where(m =>
                m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
