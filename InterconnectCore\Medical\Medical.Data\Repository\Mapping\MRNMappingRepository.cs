﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class MRNMappingRepository : IMRNMappingRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public MRNMappingRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<MRNMappingInfo>> GetAllAsync()
        {
            return await _medicalDbContext.MrnMappingInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<MRNMappingInfo>> GetAsync(string mainMrn)
        {
            return await _medicalDbContext.MrnMappingInfos.Where(m => m.MainMRN == mainMrn && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
