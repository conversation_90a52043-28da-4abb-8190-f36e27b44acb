﻿
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientEventRepository : IPatientEventRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly MedicalStatisticsDbContext _medicalStatisticsDbContext;

        /// <summary>
        /// 入院事件
        /// </summary>
        private const int ASSESSLIST_2872 = 2872;
        /// <summary>
        /// 转入事件
        /// </summary>
        private const int ASSESSLIST_2874 = 2874;
        /// <summary>
        /// 转入床事件
        /// </summary>
        private const int ASSESSLIST_5318 = 5318;

        public PatientEventRepository(MedicalDbContext db
            , MedicalStatisticsDbContext medicalStatisticsDbContext)
        {
            _medicalDbContext = db;
            _medicalStatisticsDbContext = medicalStatisticsDbContext;
        }

        /// <summary>
        ///  获取病人所有事件记录
        /// </summary>
        /// <param name="stationID">单位代码</param>
        /// <param name="inPatientID">病人在院号</param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetEventAsync(int? stationID, string inPatientID)
        {
            if (stationID.HasValue)
            {
                return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inPatientID
                    && m.StationID == stationID.Value && m.DeleteFlag != "*").ToListAsync();
            }

            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inPatientID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据chartNo获取特定患者事件
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetEventAsyncByCharNo(string chartNo, int assessListID)
        {
            var a = await _medicalDbContext.PatientEventInfos.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*" && m.AssessListID == assessListID).ToListAsync();
            return a.OrderBy(m => m.OccurDate.Add(m.OccurTime)).LastOrDefault();
        }
        public async Task<List<PatientEventInfo>> GetEventByDate(DateTime startDate, DateTime endDate, int AssessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(
                m => m.OccurDate >= startDate && m.OccurDate <= endDate && m.AssessListID == AssessListID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据事件ID获取事件对象
        /// </summary>
        /// <param name="patientEventID"></param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetEventByIDAsync(string patientEventID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.PatientEventID == patientEventID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientEventInfo>> GetEventBySourceIDAsync(string sourceID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetUnCompletedAsync(DateTime startTime)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.AddDate >= startTime
            && (m.InpatientID == "-1" || m.DepartmentListID == 0 || m.BedID == 0)
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetAsync(int stationID, DateTime startDate, DateTime endDate)
        {
            return await _medicalDbContext.PatientEventInfos.Where(
                m => m.OccurDate >= startDate && m.OccurDate <= endDate && m.StationID == stationID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByStartEndTime(string inpatientID, DateTime startDate, DateTime endDate)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID).ToListAsync();

            datas = datas.Where(m => m.OccurDate.Date.Add(m.OccurTime) >= startDate
                                && m.OccurDate.Date.Add(m.OccurTime) < endDate).ToList();

            return datas;
        }

        public async Task<DateTime?> GetLastEventDateTime(string inpatientID, int assessListID)
        {
            var dateTimes = await _medicalDbContext.PatientEventInfos.Where(
                                m => m.InpatientID == inpatientID
                             && m.AssessListID == assessListID
                             && m.DeleteFlag != "*").OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime)
                             .Select(m => m.OccurDate.Date.Add(m.OccurTime))
                             .FirstOrDefaultAsync();
            if (dateTimes == new DateTime(1, 1, 1) || dateTimes == DateTime.MinValue)
            {
                return null;
            }
            return dateTimes;
        }

        public async Task<DateTime?> GetLastEventDateTime(string inpatientID, List<int> assessListIDs)
        {
            var dateTime = await _medicalDbContext.PatientEventInfos.Where(
                                 m => m.InpatientID == inpatientID
                              && assessListIDs.Contains(m.AssessListID)
                              && m.DeleteFlag != "*").OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime)
                              .Select(m => m.OccurDate.Date.Add(m.OccurTime))
                              .FirstOrDefaultAsync();

            if (dateTime == new DateTime(1, 1, 1))
            {
                return null;
            }
            return dateTime;
        }

        public async Task<List<PatientEventInfo>> GetEventByAssessListIDs(string inpatientID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID &&
                            assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 透过事件编号取得全部病人事件（包含物理删除的）
        /// (2022-08-18 中山手术同步使用该方法)
        /// </summary>
        /// <param name="inpatientID">住院病人序号</param>
        /// <param name="assessListIDs">事件AssessListIDs</param>
        /// <param name="asNoTrack">是否进行实体跟踪查询，默认不跟踪</param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetAllEventByAssessListIDs(string inpatientID, List<int> assessListIDs, bool asNoTrack = false)
        {
            if (asNoTrack)
            {
                return await _medicalDbContext.PatientEventInfos.AsNoTracking().Where(m =>
                    m.InpatientID == inpatientID && assessListIDs.Contains(m.AssessListID)).ToListAsync();
            }
            return await _medicalDbContext.PatientEventInfos.Where(m =>
                m.InpatientID == inpatientID && assessListIDs.Contains(m.AssessListID)).ToListAsync();
        }

        public async Task<List<int>> GetPatientEventByAssessListIDs(string inpatientID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID &&
                            assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*").Select(m => m.AssessListID).ToListAsync();
        }

        public async Task<PatientEventInfo> GetLastEvent(string inpatientID, int[] assessListID, DateTime dateTime)
        {
            var data = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID &&
                            assessListID.Contains(m.AssessListID) && m.DeleteFlag != "*").ToListAsync();

            if (data.Count == 0)
            {
                return null;
            }

            return data.Where(m => m.OccurDate.Date.Add(m.OccurTime) <= dateTime)
                        .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefault();
        }

        public async Task<PatientEventInfo> GetLastEvent(string inpatientID, int assessListID, DateTime dateTime)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                                 && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();

            return datas.Where(m => m.OccurDate.Date.Add(m.OccurTime) <= dateTime)
                          .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefault();
        }
        public async Task<PatientEventInfo> GetLastEvent(string inpatientID, int assessListID)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                                 && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();

            return datas.OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefault();
        }

        public async Task<PatientEventInfo> GetLastEventByAssessListID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                                && m.AssessListID == assessListID && m.DeleteFlag != "*")
               .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefaultAsync();
        }

        public async Task<PatientEventInfo> GetLastEventByAssessListIDView(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                                && m.AssessListID == assessListID && m.DeleteFlag != "*")
               .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).Select
               (m => new PatientEventInfo
               {
                   InpatientID = m.InpatientID,
                   PatientID = m.PatientID,
                   CaseNumber = m.CaseNumber,
                   ChartNo = m.ChartNo,
                   DepartmentListID = m.DepartmentListID,
                   StationID = m.StationID,
                   BedID = m.BedID,
                   BedNumber = m.BedNumber,
                   OccurDate = m.OccurDate,
                   OccurTime = m.OccurTime
               }
               ).FirstOrDefaultAsync();
        }

        public async Task<bool> GetEventByAssessListIDExist(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                                && m.AssessListID == assessListID && m.DeleteFlag != "*").CountAsync() > 0;
        }

        public async Task<PatientEventInfo> GetLastEvent(string inpatientID, DateTime dateTime)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();

            if (datas.Count == 0)
            {
                return null;
            }

            return datas.Where(m => m.OccurDate.Date.Add(m.OccurTime) < dateTime)
                             .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefault();
        }

        public async Task<PatientEventInfo> GetEventAsync(string inpatientID, int assessListID, DateTime date, TimeSpan time)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m =>
                m.InpatientID == inpatientID && m.AssessListID == assessListID
            && m.OccurDate == date.Date && m.OccurTime == time
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<bool> GetEventExistAsync(string inpatientID, int assessListID, DateTime date, TimeSpan time)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m =>
                m.InpatientID == inpatientID && m.AssessListID == assessListID
            && m.OccurDate == date.Date && m.OccurTime == time
            && m.DeleteFlag != "*").CountAsync() > 0;
        }

        public async Task<PatientEventInfo> GetNextEvent(string inpatientID, int[] assessListID, DateTime dateTime)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();

            if (datas.Count == 0)
            {
                return null;
            }

            return datas.Where(m => m.OccurDate.Date.Add(m.OccurTime) > dateTime && assessListID.Contains(m.AssessListID))
                             .OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).FirstOrDefault();
        }
        public async Task<int> GetLastStationID(string inpatientID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime)
                .Select(m => m.StationID).FirstOrDefaultAsync();
        }

        public async Task<List<string>> GetEventByStationIDAndAssessListIDs(int stationId, int[] assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.StationID == stationId &&
                            assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*").Select(m => m.InpatientID).ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByInpatientIDAsNoTracking(string inpatientID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").AsNoTracking()
                .ToListAsync();
        }
        /// <summary>
        /// 中山手术同步在用202205-31 注意一并调整
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="sourceID"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetEventBySourceIDAndAssessListIDAsync(string inpatientID, string sourceID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID && m.SourceID == sourceID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientEventInfo>> GetEventByAssessListIDAsync(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientEventInfo>> GetEventByCaseNumberAndAssessListIDAsync(string caseNumber, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.CaseNumber == caseNumber && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByCaseNumberAndAssessListIDAsync(string caseNumber, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.CaseNumber == caseNumber && m.AssessListID == assessListID && m.DeleteFlag != "*")
                .Select(m => new PatientEventInfo
                {
                    PatientEventID = m.PatientEventID,
                    AssessListID = m.AssessListID,
                    OccurTime = m.OccurTime,
                    OccurDate = m.OccurDate,
                    SourceID = m.SourceID
                }).ToListAsync();
        }

        public async Task<DateTime?> GetLastEventDateTime(string inpatientID, int assessListID, int preHours, int nextHours)
        {
            var events = await _medicalDbContext.PatientEventInfos.Where(
                                m => m.InpatientID == inpatientID
                             && m.AssessListID == assessListID
                             && m.DeleteFlag != "*").Select(m => new
                             {
                                 m.AddDate,
                                 m.OccurDate,
                                 m.OccurTime
                             }).ToListAsync();

            if (assessListID == 1860)
            {
                //事件时间在开始 结束时间内
                events = events.Where(m => (m.AddDate > DateTime.Now.AddHours(preHours * -1) && m.AddDate < DateTime.Now.AddHours(nextHours))
                || (m.OccurDate.Add(m.OccurTime) > DateTime.Now.AddHours(preHours * -1) && m.OccurDate.Add(m.OccurTime) < DateTime.Now.AddHours(nextHours))).ToList();
                //事件时间在前后7天内
                events = events.Where(m => m.OccurDate.Add(m.OccurTime) > DateTime.Now.AddHours(preHours * -1) && m.OccurDate.Add(m.OccurTime) < DateTime.Now.AddDays(7)).ToList();
            }
            else
            {
                //新增时间 或事件时间在开始 结束时间内
                events = events.Where(m => (m.OccurDate.Add(m.OccurTime) > DateTime.Now.AddHours(preHours * -1) && m.OccurDate.Add(m.OccurTime) < DateTime.Now.AddHours(nextHours))).ToList();
            }

            if (events.Count == 0)
            {
                return null;
            }
            var datetime = events.OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime)
                            .Select(m => m.OccurDate.Date.Add(m.OccurTime))
                            .FirstOrDefault();

            if (datetime == new DateTime(1, 1, 1))
            {
                return null;
            }
            return datetime;
        }

        public async Task<List<PatientEventInfo>> GetEventByDateTime(int stationID, DateTime dateTime)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.StationID == stationID && m.OccurDate == dateTime && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByDatetimeAndEventAssessListID(int stationID, int assessListID
            , DateTime startDate, DateTime EndDate)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.StationID == stationID &&
                            m.AssessListID == assessListID && m.DeleteFlag != "*"
                            && m.OccurDate >= startDate && m.OccurDate <= EndDate).ToListAsync();
        }

        public async Task<List<string>> GetInpatientIDsByDatetimeAndAssessListID(int stationID, int assessListID, DateTime startDate, DateTime endDate)
        {
            var query = await (from aa in _medicalDbContext.PatientEventInfos
                               join bb in _medicalDbContext.InpatientDatas
                                on aa.InpatientID equals bb.ID
                               where bb.StationID == stationID && aa.DeleteFlag != "*" && bb.DeleteFlag != "*"
                              && aa.AssessListID == assessListID
                             && InHospitalStatus.INHOSPITALLIST.Contains(bb.InHospitalStatus ?? -1)
                             && aa.OccurDate >= startDate.Date && aa.OccurDate < endDate.Date
                               select aa.InpatientID).Distinct().ToListAsync();
            return query;

        }

        /// <summary>
        /// 根据AssessListID获取最近一条患者事件
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListID"></param>
        /// <param name="startDate"></param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetByInpatientIDAndEventAssessListID(string inpatientID, int assessListID
         , DateTime startDate, int stationID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID &&
                            m.AssessListID == assessListID && m.DeleteFlag != "*" && m.StationID != stationID
                            && m.OccurDate >= startDate.Date).OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).LastOrDefaultAsync();


        }
        /// <summary>
        /// 获取多病人患者事件
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetDataByInpatientIDArr(string[] inpatientIDs, int assessListID)
        {
            if (inpatientIDs.Count() <= 0)
            {
                return new List<PatientEventInfo>();
            }
            return await _medicalDbContext.PatientEventInfos
                .Where(m => inpatientIDs.Contains(m.InpatientID) && m.AssessListID == assessListID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientEventInfo>> GetByInpatientIDArr(string[] inpatientIDs, int assessListID)
        {
            if (inpatientIDs.Count() <= 0)
            {
                return new List<PatientEventInfo>();
            }
            return await _medicalDbContext.PatientEventInfos
                .Where(m => inpatientIDs.Contains(m.InpatientID) && m.AssessListID == assessListID && m.DeleteFlag != "*")
                .Select(m => new PatientEventInfo
                {
                    InpatientID = m.InpatientID,
                    AssessListID = m.AssessListID,
                    PatientEventID = m.PatientEventID,
                }
                )
                .ToListAsync();
        }
        /// <summary>
        /// 获取多病人患者事件（未回传的）
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetDataByDataPumpFlag()
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.DataPumpFlag != "*")
                .Take(500)
                .ToListAsync();
        }
        /// <summary>
        /// 依据病区ID和AssessListID（事件类型）获取所有事件
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetDataByStationIDAndAssessListIDAsync(int stationID, int assessListID)
        {
            var query = await (from a in _medicalDbContext.PatientEventInfos.Where(m => m.AssessListID == assessListID && m.DeleteFlag != "*")
                               join b in _medicalDbContext.InpatientDatas.Where(m => InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                               && stationID != 0 && m.DeleteFlag != "*")
                               on a.InpatientID equals b.ID
                               join c in _medicalDbContext.PatientEventInfos.Where(m => m.StationID == stationID
                               && m.AssessListID == assessListID && m.DeleteFlag != "*")
                               on new { a.InpatientID, a.AssessListID } equals new { c.InpatientID, c.AssessListID }
                               select a).ToListAsync();
            //此处过滤病区：确保最后一次的转出病区是当前病区
            var f = query.GroupBy(m => m.InpatientID, (key, group) => group.OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).Last()).Where(m => m.StationID == stationID).ToList();
            return f;
        }

        public async Task<List<SimpleEventView>> GetEventViewByAssessListIDs(string inpatientID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos
                .Where(m => m.InpatientID == inpatientID
                    && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                .Select(m => new SimpleEventView
                {
                    AssessListID = m.AssessListID,
                    OccurDate = m.OccurDate,
                    OccurTime = m.OccurTime,
                    StationID = m.StationID
                })
                .OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).ToListAsync();
        }

        public async Task<List<SimpleEventView>> GetEventViewByStationIDAndAssessListIDs(string inpatientID, int stationID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos
                .Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                    && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                .Select(m => new SimpleEventView
                {
                    AssessListID = m.AssessListID,
                    OccurDate = m.OccurDate,
                    OccurTime = m.OccurTime,
                    StationID = m.StationID
                })
                .OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).ToListAsync();
        }

        public async Task<SimpleEventView> GetLastEventView(string inpatientID, int assessListID, DateTime dateTime)
        {
            var datas = await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID
                        && m.AssessListID == assessListID && m.DeleteFlag != "*")
                    .Select(m => new SimpleEventView
                    {
                        AssessListID = m.AssessListID,
                        OccurDate = m.OccurDate,
                        OccurTime = m.OccurTime
                    }).ToListAsync();

            return datas.Where(m => m.OccurDate.Date.Add(m.OccurTime) <= dateTime)
                          .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefault();
        }

        /// <summary>
        /// 获取最后一笔患者事件的BedID与BedNumber
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetLastPatientEvent(string inpatientID)
        {
            return await _medicalDbContext.PatientEventInfos
                .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AddDate)
                .Select(m => new PatientEventInfo
                {
                    InpatientID = m.InpatientID,
                    BedID = m.BedID,
                    BedNumber = m.BedNumber
                }
                ).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取本病区患者事件
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetLastPatientEventByOccurDateTime(string inpatientID, int stationID)
        {
            return await _medicalDbContext.PatientEventInfos
                .Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.DeleteFlag != "*")
                .Select(m => new PatientEventInfo
                {
                    PatientEventID = m.PatientEventID,
                    AssessListID = m.AssessListID,
                    StationID = m.StationID,
                    OccurDate = m.OccurDate,
                    OccurTime = m.OccurTime
                }
                ).ToListAsync();
        }

        /// <summary>
        /// 根据住院号获取指定事件最新的事件时间
        /// </summary>
        /// <param name="caseNumbers">住院号</param>
        /// <param name="assessListID">事件ID</param>
        /// <returns></returns>
        public async Task<DateTime?> GetLastEventOccurDateByCaseNumbersAndAssessListID(List<string> caseNumbers, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.AssessListID == assessListID && caseNumbers.Contains(m.CaseNumber))
                 .OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime)
                 .Select(m => m.OccurDate.Add(m.OccurTime)).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取病人经历病区ID
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<List<int>> GetEnteredStationIDByInpatientID(string inpatientID)
        {
            var eventViewList = await GetEventViewByAssessListIDs(inpatientID, new List<int> { ASSESSLIST_2872, ASSESSLIST_2874 });
            return eventViewList.Select(m => m.StationID).Distinct().ToList();
        }
        /// <summary>
        /// 获取某个病人的某些事件的SourceID
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListIDs"></param>
        /// <returns></returns>
        public async Task<List<string>> GetEventSourceIDByAssessListIDs(string inpatientID, List<int> assessListIDs)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID &&
                            assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*" && !string.IsNullOrEmpty(m.SourceID)).Select(m => m.SourceID).ToListAsync();
        }
        /// <summary>
        /// 通过stationID获取数据
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetEventByStation(int stationID, List<int> assessListIDs)
        {
            return await (from a in _medicalDbContext.PatientEventInfos.Where(m => m.StationID == stationID && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                          join b in _medicalDbContext.InpatientDatas.Where(m => m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                               && stationID != 0 && m.DeleteFlag != "*")
                          on a.InpatientID equals b.ID
                          select a).OrderBy(m => m.OccurDate).ThenBy(m => m.OccurTime).ToListAsync();
        }
        /// <summary>
        /// 根据ID获取是否有当前集合中的患者事件
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="assessListIDs">评估编码集合</param>
        /// <param name="stationID">病区ID</param>
        /// <returns></returns>
        public async Task<bool> GetEventsExistenceByInpatientID(string inpatientID, List<int> assessListIDs, int? stationID = null)
        {
            return await _medicalDbContext.PatientEventInfos.Where(stationID.HasValue, m => m.StationID == stationID).AnyAsync(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && assessListIDs.Contains(m.AssessListID));
        }
       
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetDataByCaseNumbers(List<string> caseNumbers, int assessListID)
        {
            if (caseNumbers.Count() <= 0)
            {
                return new List<PatientEventInfo>();
            }
            return await _medicalDbContext.PatientEventInfos.Where(m => caseNumbers.Contains(m.CaseNumber) && m.AssessListID == assessListID).ToListAsync();
        }
        
        
        /// <summary>
        /// 根据inpatientID和sourceID获取对应的患者事件（部分字段）
        /// </summary>
        /// <param name="inpatientID">用户CCC唯一ID</param>
        /// <param name="sourceID">来源业务主键ID</param>
        /// <returns>部分字段|AssessListID、PatientEventID、OccurDate、OccurTime</returns>
        public async Task<PatientEventInfo> GetEventByInpatientIDAndSourceIDAsync(string inpatientID, string sourceID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.SourceID == sourceID && m.DeleteFlag != "*")
                .Select(m => new PatientEventInfo
                {
                    AssessListID = m.AssessListID,
                    PatientEventID = m.PatientEventID,
                    OccurDate = m.OccurDate,
                    OccurTime = m.OccurTime,
                }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取时间段内的特定患者事件
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListID"></param>
        /// <param name="occurDate"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetPatientEventByIDAndDateTime(string inpatientID, int assessListID, DateTime occurDate, TimeSpan startTime,TimeSpan endTime)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID && m.DeleteFlag != "*" && m.OccurDate == occurDate.Date && m.OccurTime >= startTime && m.OccurTime <= endTime ).OrderByDescending(m => m.OccurDate).ThenByDescending(m => m.OccurTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取患者特定时间的事件集合
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListIDs"></param>
        /// <param name="occurDate"></param>
        /// <param name="occurTime"></param>
        /// <returns></returns>
        public async Task<List<PatientEventInfo>> GetPatientEventByIDAndEventsAndDateTime(string inpatientID, List<int> assessListIDs, DateTime occurDate, TimeSpan occurTime)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*" && m.OccurDate == occurDate.Date && m.OccurTime == occurTime).ToListAsync();
        }
        /// <summary>
        /// 根据AssessListID和病区ID获取事件
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="assessListID"></param>
        /// <param name="stationID"></param>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<PatientEventInfo> GetEventByIDAnStation(string inpatientID, int assessListID, int stationID,DateTime? dateTime)
        {
            if (dateTime.HasValue)
            {
                return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID && m.DeleteFlag != "*" && m.StationID == stationID && m.OccurDate.Add(m.OccurTime) >= dateTime.Value).FirstOrDefaultAsync();
            }    
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.AssessListID == assessListID && m.DeleteFlag != "*" && m.StationID == stationID).FirstOrDefaultAsync();
        }
        #region 统计视图查询
        public async Task<PatientEventInfo> GetPatientEventViewByInpatientID(string inpatientID, int assessListID)
        {
            return await _medicalDbContext.PatientEventInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*"
            && m.AssessListID == m.AssessListID)
                .Select(m => new PatientEventInfo
                {
                    InpatientID = m.InpatientID,
                    CaseNumber = m.CaseNumber,
                    ChartNo = m.ChartNo,
                    PatientEventID = m.PatientEventID,
                    OccurDate = m.OccurDate,
                    OccurTime = m.OccurTime,
                    StationID=m.StationID,
                    DepartmentListID=m.DepartmentListID
                }).FirstOrDefaultAsync();

        }
        #endregion
    }
}