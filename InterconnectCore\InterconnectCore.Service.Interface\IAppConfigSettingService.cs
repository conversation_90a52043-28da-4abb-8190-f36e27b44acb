﻿using Medical.Models;

namespace InterconnectCore.Services.Interface
{
    public interface IAppConfigSettingService
    {
        /// <summary>
        /// 取得配置
        /// </summary>
        /// <param name="hospitalID">医疗院所代码</param>
        /// <param name="settingType">配置类型</param>
        /// <param name="settingCode">配置码</param>
        /// <returns></returns>
        Task<string> GetConfigSetting(string hospitalID, string settingType, string settingCode);

        /// <summary>
        /// 取得配置
        /// </summary>
        /// <param name="hospitalID">医疗院所代码</param>
        /// <param name="settingType">配置类型</param>
        /// <returns></returns>
        Task<List<AppConfigSettingInfo>> GetConfigSetting(string hospitalID, string settingType);
    }
}
