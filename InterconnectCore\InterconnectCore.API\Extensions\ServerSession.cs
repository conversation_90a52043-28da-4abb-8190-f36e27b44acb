﻿using InterconnectCore.Common.SessionCommon;
using InterconnectCore.Data.Interface;

namespace InterconnectCore.API
{
    /// <summary>
    /// session服务
    /// </summary>
    public class SessionService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IRedisService _redisService;

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="redisService"></param>
        public SessionService(
            IHttpContextAccessor httpContextAccessor
            , IRedisService redisService
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _redisService = redisService;
        }

        /// <summary>
        /// 设置登录session
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<string> SetUserLoginSession(string hospitalID, int language)
        {
            var token = "UserLoginHospitalLanguage";
            var session = new Session()
            {
                Token = token,
                LoginTime = DateTime.Now,
                Language = language,
                HospitalID = hospitalID,
                Roles = new List<int>()
            };
            await SetAsync(session);
            return token;
        }

        /// <summary>
        /// 获取session
        /// </summary>
        /// <returns></returns>
        public async Task<Session> GetSession()
        {
            var token = "";
            try
            {
                token = _httpContextAccessor.HttpContext.GetCommonToken();
                if (string.IsNullOrEmpty(token))
                {
                    token = "UserLoginHospitalLanguage";
                }
                token = token.Trim();
            }
            catch (Exception)
            {
                return null;
            }
            var session = await GetAsync(token);
            return await Task.Run(() => session);
        }

        /// <summary>
        /// 检核Redis中是否已有token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public async Task<bool> ContainAsync(string token)
        {
            if (token == null)
            {
                return false;
            }
            return await _redisService.Exists(token);
        }

        /// <summary>
        /// 依据token获取session
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public async Task<Session> GetAsync(string token)
        {
            if (await ContainAsync(token))
            {
                return await _redisService.GetAsync<Session>(token);
            }
            return null;
        }

        /// <summary>
        /// 移除redis中session
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        public async Task<bool> RemoveAsync(Session session)
        {
            return await _redisService.Remove(session.Token);
        }

        /// <summary>
        /// 将session加入redis
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        public async Task SetAsync(Session session)
        {
            await _redisService.Add(session.Token, 0, session);
        }

        /// <summary>
        /// 获取缓存集合
        /// </summary>
        /// <param name="keys">缓存Key集合</param>
        /// <returns></returns>
        public async Task<IDictionary<string, object>> GetAll<T>(IEnumerable<string> keys)
        {
            if (keys == null)
            {
                throw new ArgumentNullException(nameof(keys));
            }
            var dict = new Dictionary<string, object>();
            try
            {
                foreach (var key in keys)
                {
                    var sessionObj = await _redisService.GetAsync<object>(key);
                    if (sessionObj is T temp)
                    {
                        dict.Add(key, temp);
                    }
                }
                return dict;
            }
            catch { }
            return dict;
        }
    }
}