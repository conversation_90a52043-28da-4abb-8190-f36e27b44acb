﻿using InterconnectCore.API.Extensions;
using InterconnectCore.Services.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace InterconnectCore.API.Controllers
{
    /// <summary>
    /// 文书查询控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/documentQuery")]
    [EnableCors("any")]
    public class DocumentQueryController : ControllerBase
    {
        private readonly IDocumentQueryService _documentQueryService;
        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="documentQueryService"></param>
        public DocumentQueryController(IDocumentQueryService documentQueryService)
        {
            _documentQueryService = documentQueryService;
        }
        /// <summary>
        /// 提供东华接口-获取患者电子病历集合列表
        /// </summary>
        /// <param name="requestParam">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("GetPDFList")]
        public async Task<ActionResult<object>> GetPDFList([FromBody] Dictionary<string,string> requestParam)
        {
            var caseNumber = requestParam["SerialNumber"];
            return await _documentQueryService.GetPatientPDFList(caseNumber);
        }
        /// <summary>
        /// 提供东华接口-获取患者病历PDF数据（Base64）
        /// </summary>
        /// <param name="requestParam">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("GetPDFData")]
        public async Task<ActionResult<object>> GetPDFData([FromBody] Dictionary<string, string> requestParam)
        {
            var nurseEMRFileListID = requestParam["ReportID"];
            return await _documentQueryService.GetPatientPDFData(nurseEMRFileListID);
        }

    }
}