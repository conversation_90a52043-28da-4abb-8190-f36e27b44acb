﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_DiscrepancyRecordRepository : ICDA_DiscrepancyRecordRepository
    {
        private readonly CDADBContext _cDADBContext = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_DiscrepancyRecordRepository(CDADBContext cDADBContext)
        {
            _cDADBContext = cDADBContext;
        }

        public async Task<DateTime?> GetLastAsync()
        {
            var data = await _cDADBContext.CDA_DiscrepancyRecordInfos
                .OrderByDescending(m => m.TimeStamp.Value).FirstOrDefaultAsync();
            if (data == null || data.TimeStamp == null)
            {
                return null;
            }
            return data.TimeStamp.Value;
        }

        public async Task<CDA_DiscrepancyRecordInfo> GetByIDAsync(string patientIntakeOutputID)
        {
            return await _cDADBContext.CDA_DiscrepancyRecordInfos
                .Where(m => m.DCID == patientIntakeOutputID)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> SaveAsync(CDA_DiscrepancyRecordInfo data, bool saveFlag = true)
        {
            try
            {
                await _cDADBContext.AddAsync(data);
                if (saveFlag)
                {
                    await _cDADBContext.SaveChangesAsync();
                }

            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量新增数据失败,异常数据:" + Common.ListToJson.ToJson(data) + "异常信息:" + ex);
                return false;
            }
            return true;
        }

        public async Task<bool> SaveDBAsync()
        {
            try
            {
                await _cDADBContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量保存数据失败,异常信息:" + ex.ToString());
                return false;
            }
            return true;
        }

        public async Task<bool> SaveAsync(List<CDA_DiscrepancyRecordInfo> translatedatas)
        {
            try
            {
                await _cDADBContext.AddRangeAsync(translatedatas);
                return await _cDADBContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量新增数据失败,异常数据:" + Common.ListToJson.ToJson(translatedatas) + "异常信息:" + ex);
                return false;
            }
        }

        public async Task<bool> UpdateAsync(CDA_DiscrepancyRecordInfo data)
        {
            var old = await GetByIDAsync(data.DCID);
            if (old == null)
            {
                return await SaveAsync(data);
            }
            try
            {
                _cDADBContext.Entry(old).CurrentValues.SetValues(data);
                return await _cDADBContext.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error("CDA出入量更新数据失败,异常数据:" + Common.ListToJson.ToJson(data) + "异常信息:" + ex);
                return false;
            }
        }
        /// <summary>
        /// 获取未同步的数据 一次最多获取1000笔数据推送
        /// </summary>
        /// <returns>List<CDA_DiscrepancyRecordInfo></returns>
        public async Task<List<CDA_DiscrepancyRecordInfo>> GetUnSyncDataAsync(DateTime? startDateTime, DateTime? endDateTime)
        {
            if (!startDateTime.HasValue)
            {
                startDateTime = await _cDADBContext.CDA_DiscrepancyRecordInfos.Where(m => m.DataPumpFlag != "*").MinAsync(m => m.TimeStamp);
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = startDateTime.Value.AddDays(1);
            }
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }
            return await _cDADBContext.CDA_DiscrepancyRecordInfos.Where(m => m.DataPumpFlag != "*" && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime).OrderBy(m => m.TimeStamp).Take(1000).ToListAsync();

        }
        /// <summary>
        /// 更新数据推送标记
        /// </summary>
        /// <param name="dcid">主记录主键</param>
        /// <returns></returns>
        public async Task<bool> UpdateSyncFlagAsync(string dcID)
        {
            var affectRows = await _cDADBContext.Database.ExecuteSqlRawAsync("update CDA_DiscrepancyRecord set DataPumpFlag='*',DataPumpDate=GetDate() where DCID = {0}", dcID);

            return affectRows > 0;

        }

        /// <summary>
        /// Batch更新数据推送标记
        /// </summary>
        /// <param name="dcid">主记录主键</param>
        /// <returns></returns>
        public async Task<bool> BatchUpdateSyncFlagAsync(List<string> dcIDs)
        {
            if (dcIDs == null || dcIDs.Count <= 0)
            {
                return true;
            }
            var affectRows = 0;
            using (var transaction = await _cDADBContext.Database.BeginTransactionAsync())
            {
                transaction.ConfigureAwait(true);
                var str = dcIDs.Select(m => $"'{m}'").Aggregate((a, b) => a + "," + b).ToString();

                affectRows = await _cDADBContext.Database.ExecuteSqlRawAsync($"update CDA_DiscrepancyRecord set DataPumpFlag='*',DataPumpDate=GetDate() where DCID in ({str})");

                await transaction.CommitAsync();
            }


            return affectRows > 0;

        }
    }
}