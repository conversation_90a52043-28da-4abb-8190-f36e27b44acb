﻿namespace InterconnectCore.ViewModels
{
    public class InpatientOrderScheduleView
    {
        public string HISKey { get; set; }
        /// <summary>
        /// 医嘱KEY
        /// </summary>
        public string OrderID { get; set; }
        /// <summary>
        ///子医嘱编号 
        /// </summary>
        public string OrderSubID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 床位号码
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 床位代码
        /// </summary>
        public string BedCode { get; set; }
        /// <summary>
        /// 医嘱类型标记(1:长期,0:临时)
        /// </summary>
        public string TypeCode { get; set; }
        /// <summary>
        /// 医嘱状态标记
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 医嘱状态名称:医嘱开立、护士执行、医嘱作废等
        /// </summary>
        public string StatusName { get; set; }
        /// <summary>
        /// 医嘱分类:如化验，药品，治疗，其他
        /// </summary>
        public string CategoryCode { get; set; }
        /// <summary>
        /// 医嘱分类名称
        /// </summary>
        public string CategoryName { get; set; }
        /// <summary>
        /// 医嘱代码
        /// </summary>
        public string OrderCode { get; set; }
        /// <summary>
        /// 医嘱类别
        /// </summary>
        public string OrderPattern { get; set; }
        /// <summary>
        /// 医嘱内容
        /// </summary>
        public string OrderContent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 一次剂量:药品医嘱必须
        /// </summary>
        public decimal? OrderDose { get; set; }
        /// <summary>
        /// 频次
        /// </summary>
        public string Frequency { get; set; }
        /// <summary>
        /// 单位:药品医嘱必须
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 总剂量:药品医嘱必须
        /// </summary>
        public decimal? TotalVolume { get; set; }
        /// <summary>
        /// 服法/途径/姿势
        /// </summary>
        public string OrderRule { get; set; }
        /// <summary>
        /// 部位
        /// </summary>
        public string Location { get; set; }
        /// <summary>
        /// 预计执行时间
        /// </summary>
        public DateTime? ScheduleDatetime { get; set; }
        /// <summary>
        /// 新增人员工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime? AddDate { get; set; }
        /// <summary>
        /// 作废医嘱人员工号
        /// </summary>
        public string CancalPersonID { get; set; }
        /// <summary>
        /// 作废日期
        /// </summary>
        public DateTime? CancelDate { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>       
        public string DeleteFlag { get; set; }
    }
}
