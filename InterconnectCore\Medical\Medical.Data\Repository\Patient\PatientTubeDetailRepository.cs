﻿using Medical.Data.Interface;

namespace Medical.Data.Repository
{
    //排除原因： PatientTubeDetail表不再使用
    //操作人员：梁宝华 2020-03-17
    public class PatientTubeDetailRepository : IPatientTubeDetailRepository
    {
        //private MedicalDbContext _medicalDbContext = null;
        //public PatientTubeDetailRepository(MedicalDbContext db)
        //{
        //    _medicalDbContext = db;
        //}
        //public async Task<PatientTubeDetailInfo> GetByIDAsync(string id)
        //{
        //    return await _medicalDbContext.PatientTubeDetailInfos.Where(m => m.PatientTubeDetailID == id).SingleOrDefaultAsync();
        //}

        //public async Task<List<PatientTubeDetailInfo>> GetAsync(string patientTubeID)
        //{
        //    return await _medicalDbContext.PatientTubeDetailInfos.Where(
        //        m => m.PatientTubeID == patientTubeID && m.DeleteFlag != "*")
        //        .OrderBy(m=>m.PerformDate).ThenBy(m=>m.PerformTime).ToListAsync();
        //}

        //public async Task<List<PatientTubeDetailInfo>> GetNoPumpAsync()
        //{
        //    return await _medicalDbContext.PatientTubeDetailInfos.Where(t => t.DataPumpFlag != "*").ToListAsync();
        //}
    }
}