﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PersonalLanguageRepository : IPersonalLanguageRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public PersonalLanguageRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<PersonalLanguageInfo> GetByEmployeeBasicIDAsync(int employeeBasicID)
        {
            return await _medicalDbContext.PersonalLanguageInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeBasicID == employeeBasicID).SingleOrDefaultAsync();
        }

        public async Task<List<PersonalLanguageInfo>> GetList()
        {
            return await _medicalDbContext.PersonalLanguageInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<PersonalLanguageInfo> GetList(int ID)
        {
            return await _medicalDbContext.PersonalLanguageInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeLanguageID == ID).SingleAsync();
        }
    }
}