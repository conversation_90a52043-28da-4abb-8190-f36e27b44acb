﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class LoginLogRepository : ILoginLogRepository
    {
        private MedicalDbContext _dbContext = null;

        public LoginLogRepository(
            MedicalDbContext db
        )
        {
            _dbContext = db;
        }
        /// <summary>
        /// 等保测评获取用户登陆日志
        /// </summary>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<List<LoginLogInfo>> GetLoginInfosByUserID(string userID)
        {
            return await _dbContext.LoginLogInfos.Where(m => m.EmployeeID == userID && string.IsNullOrWhiteSpace(m.LoginContent))
                .OrderByDescending(m => m.LoginDate).ToListAsync();
        }

        public async Task<Tuple<int, DateTime?>> GetSameIPLoginInfoByDate(DateTime startTime, DateTime endTime, string userID, string ip)
        {
            var list = await _dbContext.LoginLogInfos.Where(m => m.EmployeeID == userID
                                                && string.IsNullOrWhiteSpace(m.LoginContent) && m.IPAddress == ip
                                                && m.LoginDate > startTime && m.LoginDate <= endTime)
                                                .OrderByDescending(m => m.LoginDate).ToListAsync();
            DateTime? loginTime = null;
            if (list.Count > 0)
            {
                loginTime = list[0].LoginDate;
            }
            return new Tuple<int, DateTime?>(list.Count, loginTime);
        }
    }
}