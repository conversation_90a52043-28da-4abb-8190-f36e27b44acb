﻿using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Interface;
using Medical.Models;
using NLog;
using System.Dynamic;

namespace InterconnectCore.Services.Core
{

    public class DocumentQueryService : IDocumentQueryService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IEMRListRepository _eMRList;
        private readonly INurseEMRFileListRepository _nurseEMRFileListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IClinicSettingRepository _clinicSettingRepository;
        private readonly INewBornRecordRepository _newBornRecordRepository;

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public DocumentQueryService(
              IEMRListRepository eMRList
            , INurseEMRFileListRepository nurseEMRFileListRepository
            , IInpatientDataRepository inpatientDataRepository
            , IRequestApiService requestApiService
            , IClinicSettingRepository clinicSettingRepository
            , INewBornRecordRepository newBornRecordRepository
         )
        {
            _eMRList = eMRList;
            _nurseEMRFileListRepository = nurseEMRFileListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _requestApiService = requestApiService;
            _clinicSettingRepository = clinicSettingRepository;
            _newBornRecordRepository = newBornRecordRepository;
        }
        /// <summary>
        /// 获取患者病历集合列表
        /// </summary>
        /// <param name="caseNumber">住院号</param>
        /// <returns></returns>
        public async Task<object> GetPatientPDFList(string caseNumber)
        {
            var returnDatas = new List<DocumentReturnView>();
            // 创建可修改的 ExpandoObject
            dynamic returnView = new ExpandoObject();
            returnView.ResultCode = 1;
            returnView.ErrorMsg = "";
            returnView.Data = new ExpandoObject();
            returnView.Data.Report = returnDatas;
           var inpatientData = await _inpatientDataRepository.GetByCaseNumberAsNoTracking(caseNumber);
            if (inpatientData == null)
            {
                var errorMessage = $"没有查询到该患者住院信息，住院号：{caseNumber}";
                _logger.Error(errorMessage);
                returnView.ErrorMsg = errorMessage;
                returnView.ResultCode = 0;
                return returnView;
            }
            var inpatientIDs = await GetChildAndMotherInpatientID(inpatientData);
            if (inpatientIDs.Count == 0)
            {
                return returnView;
            }
            var eMRList = await _eMRList.GetEMRList();
            var nurseEMRFileListInfos = await _nurseEMRFileListRepository.GetDataByInpatientIDs(inpatientIDs);
            var noArchiveEmrIDs = await _clinicSettingRepository.GetSettingValuesByTypeCode("NoArchiveEmrIDs");
            nurseEMRFileListInfos = nurseEMRFileListInfos.Where(m => !noArchiveEmrIDs.Contains(m.FileClass.ToString())).ToList();
            foreach (var nurseEMRFileListInfo in nurseEMRFileListInfos)
            {
                var eMR = eMRList.Where(m => m.FileClassID == nurseEMRFileListInfo.FileClass).FirstOrDefault();
                //var operateTime = !string.IsNullOrEmpty(inpatientData.EMRArchivingFlag) ? nurseEMRFileListInfo.ModifyDate?.ToString("yyyy-MM-dd HH:mm:ss") : "";
                var returnData = new DocumentReturnView
                {
                    ReportID = nurseEMRFileListInfo.ID.ToString(),
                    ReportName = nurseEMRFileListInfo.ShowName,
                    ReportType = "NIS",
                    ReportChildType = eMR?.FileCode,
                    IsPaperFiling = "0",
                    OperateTime = nurseEMRFileListInfo.ModifyDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    IsDone = string.IsNullOrEmpty(inpatientData.EMRArchivingFlag) ? "0" : "1"
                };
                returnDatas.Add(returnData);
            }
            return returnView;
        }
        /// <summary>
        /// 获取患者及其子女的住院ID列表
        /// 若患者是新生儿，则不返回
        /// </summary>
        /// <param name="inpatientData">患者信息</param>
        /// <returns></returns>
        private async Task<List<string>> GetChildAndMotherInpatientID(InpatientDataInfo inpatientData)
        {
            var inpatientIDs = new List<string>();
            var childNewBornRecord = await _newBornRecordRepository.GetDataByCasenumber(inpatientData.CaseNumber);
            // 如果是新生儿，则不返回
            if (childNewBornRecord != null)
            {
                return inpatientIDs;
            }
             inpatientIDs.Add(inpatientData.ID);
            var parentNewBornRecord = await _newBornRecordRepository.GetDataByParentCasenumber(inpatientData.CaseNumber);
            var childCaseNumbers = parentNewBornRecord.Where(m => !string.IsNullOrEmpty(m.Casenumber)).Select(m => m.Casenumber).Distinct().ToList();
            var childInpatientDatas = await _inpatientDataRepository.GetInpatientDataByCaseNumberListAsync(childCaseNumbers);
            if (childInpatientDatas.Count > 0)
            {
                var childInpatientIDs = childInpatientDatas.Select(m => m.ID).ToList();
                inpatientIDs.AddRange(childInpatientIDs);
            }
            return inpatientIDs;
        }
        /// <summary>
        /// 获取患者病历PDF数据（Base64）
        /// </summary>
        /// <param name="nurseEMRFileListID">病历唯一ID号</param>
        /// <returns></returns>
        public async Task<object> GetPatientPDFData(string nurseEMRFileListID)
        {
            string errorMessage = "";
            dynamic returnView = new ExpandoObject();
            returnView.Data = "";
            returnView.DataType = "application/pdf";
            returnView.ResultCode = 0;
            if (!int.TryParse(nurseEMRFileListID,out var id))
            {
                errorMessage = "病历/报告唯一ID号不规范，ID号：" + nurseEMRFileListID;
                returnView.ErrorMsg = errorMessage;
                _logger.Error(errorMessage);
                return returnView;
            }
            var nurseEMRFile = await _nurseEMRFileListRepository.GetFileIDByIDView(id);
            if (nurseEMRFile == null)
            {
                errorMessage = "未查到该病历/报告，ID号：" + nurseEMRFileListID;
                returnView.ErrorMsg = errorMessage;
                _logger.Error(errorMessage);
                return returnView;
            }
            var requestParam = $"?fileID={nurseEMRFile.FileId}";
            var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientDocumentAPI", requestParam, 2, null, "application/json");
            if (result == null)
            {
                errorMessage = "数据查询失败，ID号：" + nurseEMRFileListID;
                returnView.ErrorMsg = errorMessage;
                _logger.Error(errorMessage);
                return returnView;
            }
            returnView.Data = result.Data;
            returnView.ResultCode = result.Code == 200 ? "1" : "0";
            returnView.ErrorMsg = result.Message;
            return returnView;
        }
    }
}
