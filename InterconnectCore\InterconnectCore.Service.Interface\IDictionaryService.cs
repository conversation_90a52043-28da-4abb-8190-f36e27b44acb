using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IDictionaryService
    {
        /// <summary>
        /// 同步床位字典数据
        /// </summary>
        /// <param name="bedListInfos"></param>
        /// <returns></returns>
        Task<bool> SyncBedListAsync(List<BedListInfo> bedListInfos);
        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="userList">员工列表</param>
        /// <returns></returns>
        Task<bool> SyncAllEmployeeData(List<UserView> userList);

        /// <summary>
        /// 根据员工编号获取单个员工
        /// </summary>
        /// <param name="user">员工信息</param>
        /// <returns></returns>
        Task<bool> SyncOneEmployeeData(UserView user);
        /// <summary>
        /// 同步病区科室信息
        /// </summary>
        /// <param name="stationAndDepartmentInfos"></param>
        /// <returns></returns>
        Task<bool> SyncStationAndDepartmentAsync(StationAndDepartmentView stationAndDepartmentInfos);
        /// <summary>
        /// 同步医嘱字典
        /// </summary>
        /// <param name="orderDictViewList"></param>
        /// <returns></returns>
        Task<bool> SyncPhysionOrder(List<OrderDictView> orderDictViewList);

        /// <summary>
        /// 同步药品字典
        /// </summary>
        /// <param name="drugDictViewList"></param>
        /// <returns></returns>
        Task<bool> SyncDrugList(List<DrugDictView> drugDictViewList);

        /// <summary>
        /// 同步用户CA数据
        /// </summary>
        /// <param name="employeeCAViews"></param>
        /// <returns></returns>
        Task<bool> SyncEmployeeCAData(List<EmployeeCAView> employeeCAViews);
    }
}
