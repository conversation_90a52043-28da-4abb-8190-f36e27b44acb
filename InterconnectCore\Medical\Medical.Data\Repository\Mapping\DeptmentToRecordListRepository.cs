﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class DeptmentToRecordListRepository : IDeptmentToRecordListRepository
    {

        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public DeptmentToRecordListRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<DepartmentToRecordListInfo>> GetAsync(int deptmentId, int? recordListId)
        {
            var datas = await GetCacheAsync() as List<DepartmentToRecordListInfo>;
            if (datas != null)
            {
                var query = datas.Where(t => t.DepartmentListID == deptmentId || t.DepartmentListID == 999999);
                if (recordListId != null)
                {
                    query = query.Where(t => t.RecordListID == recordListId);
                }
                return query.ToList();
            }
            //999999表示所有科室适用
            return datas;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<DepartmentToRecordListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.DeptmentToRecordListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DeptmentToRecordList.GetKey(_sessionCommonServer);
        }
        public async Task<List<DepartmentToRecordListInfo>> GetAllAsync()
        {
            return await GetCacheAsync() as List<DepartmentToRecordListInfo>;
        }

        public async Task<DepartmentToRecordListInfo> GeByIdAsync(int Id)
        {
            return (await this.GetAllAsync()).Where(t => t.ID == Id).FirstOrDefault();
        }
        /// <summary>
        ///调用该方法为了获取实时的DepartmentToRecordListInfo主键，所以不走缓存
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetByMaxIDAsync()
        {
            return await _medicalDbContext.DeptmentToRecordListInfos.MaxAsync(m => m.ID);
        }
    }
}