﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Common;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Data;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using NLog;
using Org.BouncyCastle.X509;

namespace InterconnectCore.Services
{
    public class PatientDiagnosisService : IPatientDiagnosisService
    {
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();
        //Medical
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientDiagnosisRepository _patientDiagnosisRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly INurseEMRFileListRepository _nurseEMRFileListRepository;
        private readonly DataTableEditListService _dataTableEditListService;
        //InterconnectCore
        private readonly ISyncLogService _syncLogService;
        private readonly CommonHelper _commonHelper;
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private readonly MQCommonService _mQCommonService;
        private readonly ExternalCommonService _externalCommonService;

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="appConfigSettingRepository"></param>
        /// <param name="inpatientDataRepository"></param>
        /// <param name="patientDiagnosisRepository"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="medicalDbContext"></param>
        /// <param name="syncLogService"></param>
        /// <param name="commonHelper"></param>
        /// <param name="config"></param>
        /// <param name="mQCommonService"></param>
        public PatientDiagnosisService(
            IAppConfigSettingRepository appConfigSettingRepository,
            IInpatientDataRepository inpatientDataRepository,
            IPatientDiagnosisRepository patientDiagnosisRepository,
            IUnitOfWork<MedicalDbContext> unitOfWork,
            MedicalDbContext medicalDbContext,
              ISyncLogService syncLogService,
            CommonHelper commonHelper,
            IOptions<ViewModels.SystemConfig> config,
            MQCommonService mQCommonService,
            INurseEMRFileListRepository nurseEMRFileListRepository,
            DataTableEditListService dataTableEditListService,
            ExternalCommonService externalCommonService
         )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientDiagnosisRepository = patientDiagnosisRepository;
            _unitOfWork = unitOfWork;
            _medicalDbContext = medicalDbContext;
            _syncLogService = syncLogService;
            _commonHelper = commonHelper;
            _config = config;
            _mQCommonService = mQCommonService;
            _nurseEMRFileListRepository = nurseEMRFileListRepository;
            _dataTableEditListService = dataTableEditListService;
            _externalCommonService = externalCommonService;
        }


        /// <summary>
        /// 同步患者诊断信息
        /// </summary>
        /// <param name="patientntDiagnosisList"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientDiagnosisAsync(List<PatientDiagnosisItemView> patientntDiagnosisList)
        {
            _logger.Info("开始进行患者诊断数据同步，数据条数：" + patientntDiagnosisList.Count);
            string tablename = "PatientDiagnosis";
            var caseNumber = patientntDiagnosisList[0].CaseNumber;
            var hospitalID = _config.Value.HospitalID;
            var modifyPersonID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID");
            var patientDiagnosis = new PatientDiagnosisInfo();
            //获取病人入院记录
            var tempPatient = await _inpatientDataRepository.GetAsyncByCaseNumber(caseNumber, hospitalID);
            if (tempPatient == null)
            {
                _logger.Info(tablename + " 对表: InPatientData  住院号CaseNumber[" + caseNumber + "]  查询在院病人信息错误!");
                return false;
            }
            //获取病人诊断数据
            var cccInpatientDiagnosisList = await _patientDiagnosisRepository.GetAsync(tempPatient.ID);
            _logger.Info(tablename + " 开始进行住院号" + caseNumber + "诊断数据同步，数据条数：" + patientntDiagnosisList.Count);
            var list = new List<PatientDiagnosisInfo>();
            foreach (var item in patientntDiagnosisList)
            {
                var cccInpatientDiagnosisInfo = cccInpatientDiagnosisList.FirstOrDefault(p => p.DiagnosisCode == item.DiagnosisCode);
                //写入诊断表
                if (cccInpatientDiagnosisInfo == null)
                {
                    patientDiagnosis = CreatePatientDiagnosisInfo(item, tempPatient);
                    list.Add(patientDiagnosis);
                }
                else
                {
                    patientDiagnosis = UPPatientDiagnosisInfo(item, cccInpatientDiagnosisInfo, modifyPersonID);
                }
            }
            if (list.Count > 0)
            {
                _unitOfWork.GetRepository<PatientDiagnosisInfo>().Insert(list);
            }
            var mainDiagnosis = patientntDiagnosisList.Where(m => m.MainFlag == "*").FirstOrDefault();
            if (mainDiagnosis == null)
            {
                _logger.Info(tablename + " 住院号" + caseNumber + "没有主诊断!");
            }
            if (mainDiagnosis != null)
            {
                //对病人的诊断进行更新（只有是主诊断才更新）
                 UPInpatientDiagnosis(mainDiagnosis, tempPatient);
                await UpdateEmrFileListAsync(tempPatient);
            }
            try
            {
                _unitOfWork.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(tablename + "||同步失败||" + ex.ToString());
                return false;
            }
            _logger.Info(tablename + " 同步完成");
            return true;
        }

        /// <summary>
        /// 产生诊断记录
        /// </summary>
        /// <param name="hisItem"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <returns></returns>
        private static PatientDiagnosisInfo CreatePatientDiagnosisInfo(PatientDiagnosisItemView hisItem, InpatientDataInfo inpatientDataInfo)
        {
            //诊断数据为空，不记录
            if (string.IsNullOrEmpty(hisItem.DiagnosisName))
            {
                return null;
            }
            var patientDiagnosisInfo = new PatientDiagnosisInfo()
            {
                InpatientID = inpatientDataInfo.ID,
                PatientID = inpatientDataInfo.PatientID,
                StationID = inpatientDataInfo.StationID,
                DepartmentListID = inpatientDataInfo.DepartmentListID,
                CaseNumber = inpatientDataInfo.CaseNumber,
                ChartNo = inpatientDataInfo.ChartNo,
                DiagnosisCode = hisItem.DiagnosisCode ?? "",
                ICDCode = hisItem.ICDCode,
                DiagnosisName = hisItem.DiagnosisName ?? "",
                DiagnosisType = hisItem.DiagnosisType,
                DiagnosisHISNote = "",
                Sort = hisItem.Sort,
                DoctorNo = "",
                MainFlag = hisItem.MainFlag,
                OutTime = hisItem.OutTime,
                ModifyPersonID = hisItem.ModifyPersonID,
                AddDate = hisItem.AddDate,
                AddEmployeeID = hisItem.AddEmployeeID,
                ModifyDate = hisItem.ModifyDate,
                DeleteFlag = hisItem.DeleteFlag,
            };
            return patientDiagnosisInfo;
        }

        /// <summary>
        /// 更新诊断数据
        /// </summary>
        /// <param name="hisItem"></param>
        /// <param name="patientDiagnosisInfo"></param>
        private PatientDiagnosisInfo UPPatientDiagnosisInfo(PatientDiagnosisItemView hisItem, Medical.Models.PatientDiagnosisInfo patientDiagnosisInfo, string modifyPersonID)
        {
            var upFlag = false;
            if (!string.IsNullOrEmpty(hisItem.DiagnosisName) && patientDiagnosisInfo.DiagnosisName != hisItem.DiagnosisName)
            {
                patientDiagnosisInfo.DiagnosisName = hisItem.DiagnosisName;
            }
            if (!string.IsNullOrEmpty(hisItem.DiagnosisType) && patientDiagnosisInfo.DiagnosisType != hisItem.DiagnosisType)
            {
                patientDiagnosisInfo.DiagnosisType = hisItem.DiagnosisType;
            }
            patientDiagnosisInfo.MainFlag = hisItem.MainFlag;
            //确认数据是否发生改变
            upFlag = _medicalDbContext.Entry(patientDiagnosisInfo).State != Microsoft.EntityFrameworkCore.EntityState.Unchanged;
            if (upFlag)
            {
                patientDiagnosisInfo.DiagnosisTime = DateTime.Now;
                patientDiagnosisInfo.Modify(modifyPersonID);
            }
            return patientDiagnosisInfo;
        }


        /// <summary>
        /// 呼叫profile   
        /// </summary>
        /// <param name="patientProfile"></param>
        /// <param name="messageModel"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <returns></returns>
        private async Task<bool> CallProfile(List<PatientProfile> patientProfile, MessageModel messageModel, InpatientDataInfo inpatientDataInfo)
        {
            if (patientProfile == null || messageModel == null)
            {
                return false;
            }

            //发送信息
            var messageModels = new List<MessageModel>
            {
              messageModel
            };
            try
            {
                if (patientProfile.Count > 0)
                {
                    await _commonHelper.AddProfile(patientProfile);
                }
                await _mQCommonService.SendingMessage(messageModels);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("诊断同步呼叫Profile失败，" + ex.ToString());
                return false;
            }
        }
        /// <summary>
        /// 更新病人电子病历异动
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task UpdateEmrFileListAsync(InpatientDataInfo inpatient)
        {
            if (!string.IsNullOrEmpty(inpatient.EMRArchivingFlag))
            {
                return;
            }
            var nurseEMRFileViews = await _nurseEMRFileListRepository.GetAllNurseEMRFileList(inpatient.ID);
            nurseEMRFileViews = nurseEMRFileViews.GroupBy(m => new { m.FileClass, m.RecordListID,m.SerialNumber,m.Stationid }).Select(g => g.First()).ToList();
            if (nurseEMRFileViews == null && nurseEMRFileViews.Count <= 0)
            {
                return;
            }
            foreach (var item in nurseEMRFileViews)
            {
                int.TryParse(item.Stationid, out int stationID);
                await _dataTableEditListService.AddEditLog(item.Inpatientid, stationID, "", item.FileClass, item.SerialNumber, item.RecordListID);
            }
        }
        /// <summary>
        /// 更新病人诊断信息
        /// </summary>
        /// <param name="patientDiagnosisInfo"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <returns></returns>
        private static bool UPInpatientDiagnosis(PatientDiagnosisItemView patientMainDiagnosis, InpatientDataInfo inpatientDataInfo)
        {
            var diagnosisTypes = new List<string>
            {
                "A","P"
            };
            if (patientMainDiagnosis == null || string.IsNullOrEmpty(patientMainDiagnosis.ICDCode))
            {
                return false;
            }
            //只接收住院中的诊断信息
            if (!diagnosisTypes.Contains(patientMainDiagnosis.DiagnosisType))
            {
                return false;
            }
            //诊断码发生变化，呼叫Profile
            if (patientMainDiagnosis.ICDCode != inpatientDataInfo.ICDCode)
            {
                inpatientDataInfo.ICDCode = patientMainDiagnosis.ICDCode;
            }

            if (patientMainDiagnosis.DiagnosisName != inpatientDataInfo.Diagnosis)
            {
                inpatientDataInfo.Diagnosis = patientMainDiagnosis.DiagnosisName;
            }
            //if (upFlag)
            //{
            //    var ids = await _externalCommonService.GetAssessListIDByICDCode(inpatientDataInfo.ICDCode, inpatientDataInfo.DepartmentListID);

            //    var profiles = new List<PatientProfile>();

            //    foreach (var item in ids)
            //    {
            //        var profile = _externalCommonService.CreateProfile(inpatientDataInfo, "HisDiagnosis", item, "", _config.Value.HospitalID, "TongBu");

            //        profiles.Add(profile);
            //    }
            //    if (profiles.Count > 0)
            //    {
            //        var sendMessage = _externalCommonService.CreateDiagnosisMessage(inpatientDataInfo);

            //        //呼叫Profile
            //        return new Tuple<List<PatientProfile>, MessageModel>(profiles, sendMessage);
            //    }
            //}
            return true;
        }
    }
}