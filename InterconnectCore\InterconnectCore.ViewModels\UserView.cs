﻿namespace Medical.Models
{
    /// <summary>
    /// 用户表
    /// </summary>
    public class UserView
    {
        /// <summary>
        /// 医疗院所代码
        /// </summary>       
        public string HospitalID { get; set; }
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserID { get; set; }
        /// <summary>
        /// 医师工号
        /// </summary>
        public string PhysicianID { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 授权科室
        /// </summary>        
        public List<string> DepartmentCodes { get; set; }
        /// <summary>
        /// 本科室
        /// </summary>        
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 级职
        /// </summary>
        public string Rank { get; set; }
        /// <summary>
        /// 工种：医师、护士、会计等。。
        /// </summary>        
        public int? JobID { get; set; }
        /// <summary>
        /// 电话号
        /// </summary>
        public string PhoneNumber { get; set; }
        /// <summary>
        /// 特殊标记，1 不同步人员护士站权限信息（中山数据同步）。（因标记意义广泛。谁使用了，一定要说明，切记）
        /// 宏力使用，1 不同步人员护士站权限信息
        /// 特殊标记，2 系统内置账号不进行删除
        /// </summary>
        public int? SpecialFlag { get; set; }

        /// <summary>
        /// 人事部门ID
        /// </summary> 
        public string HREmployeeID { get; set; }
        /// <summary>
        /// 账号失效日期
        /// </summary>
        public DateTime? ExpirationDate { get; set; }
        /// <summary>
        /// 用户类型
        /// </summary>
        public string EmployeeType { get; set; }
        /// <summary>
        /// 简拼
        /// </summary>
        public string PinYinCode { get; set; }
        /// <summary>
        /// 能力层级ID
        /// </summary>
        public int? CapabilityLevelID { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>
        public string DeleteFlag { get; set; } = "";
        /// <summary>
        /// 员工权限
        /// </summary>
        public List<int> UserRoles { get; set; }
    }
}