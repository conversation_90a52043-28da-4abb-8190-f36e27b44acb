﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class SynchronizeLogRepository : ISynchronizeLogRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        public SynchronizeLogRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLog()
        {
            return await _medicalDbContext.SynchronizeLogInfos.Where(m => m.SuccessFlag == "").ToListAsync();
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLog(byte retryTimes)
        {
            return await _medicalDbContext.SynchronizeLogInfos.Where(m => m.SuccessFlag == ""
            && m.RetryTimes <= retryTimes
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLogByUrl(byte retryTimes, string url)
        {
            return await _medicalDbContext.SynchronizeLogInfos.Where(m => m.SuccessFlag == ""
            && m.RetryTimes <= retryTimes
            && m.DeleteFlag != "*"
            && m.ApiUrl == url).ToListAsync();
        }

        public async Task<int> GetUnSyncCount()
        {
            return await _medicalDbContext.SynchronizeLogInfos.CountAsync(m => m.SuccessFlag == ""
            && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 获取指定病人的同步记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<SynchronizeLogInfo>> GetSyncLogByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.SynchronizeLogInfos.AsNoTracking().Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").
                Select(m => new SynchronizeLogInfo
                {
                    InpatientID = m.InpatientID,
                    ApiUrl = m.ApiUrl,
                    SuccessFlag = m.SuccessFlag,
                    Arguments = m.Arguments,

                }).Distinct().ToListAsync();
        }
        public async Task<SynchronizeLogInfo> GetErrorLogByLogID(string logID)
        {
            return await _medicalDbContext.SynchronizeLogInfos.Where(m => m.ID == logID
            && m.DeleteFlag != "*" && m.SuccessFlag == "").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据ID获取同步记录
        /// </summary>
        /// <param name="logId">日志主键</param>
        /// <returns></returns>
        public async Task<SynchronizeLogInfo> GetSynchronizeLogById(string logId)
        {
            return await _medicalDbContext.SynchronizeLogInfos.Where(m => m.ID == logId && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
