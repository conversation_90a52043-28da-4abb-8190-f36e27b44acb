﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientMedicineScheduleRepository : IPatientMedicineScheduleRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        public PatientMedicineScheduleRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        public async Task<PatientMedicineScheduleInfo> GetByID(string patientMedicineScheduleID)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => m.PatientMedicineScheduleID == patientMedicineScheduleID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        public async Task<List<PatientMedicineScheduleInfo>> GetByGroupAsync(string[] groupIDs)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => groupIDs.Contains(m.GroupID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据OrderMainID获取患者的给药数据
        /// </summary>
        /// <param name="inpatientID">患者主键</param>
        /// <param name="patientOrderMainIds">患者OrderID集合</param>
        /// <returns></returns>
        public async Task<List<PatientMedicineScheduleInfo>> GetMedicineScheduleByOrderIDsAsync(string inpatientID,List<string> patientOrderMainIds)
        {
            return await _medicalDbContext.PatientMedicineScheduleInfos.Where(m => inpatientID == m.InpatientID && patientOrderMainIds.Contains(m.PatientOrderMainID)).ToListAsync();
        }
    }
}