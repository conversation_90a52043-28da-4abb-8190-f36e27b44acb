﻿using StackExchange.Redis;

namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// Redis分布式锁
    /// </summary>
    public class RedisDistributedLock : IDistributedLock
    {
        private readonly ConnectionMultiplexer _redisConnection;
        private IDatabase _database;

        /// <summary>
        /// Redis分布式锁
        /// </summary>
        /// <param name="redisConnection"></param>
        public RedisDistributedLock(ConnectionMultiplexer redisConnection)
        {
            _redisConnection = redisConnection;
            _database = _redisConnection.GetDatabase();
        }

        /// <summary>
        /// 获取锁
        /// </summary>
        /// <param name="resourceKey"></param>
        /// <param name="lockDuration"></param>
        /// <returns></returns>
        public Task<bool> TryAcquireLockAsync(string resourceKey, TimeSpan? lockDuration = null)
        {
            var isLockAcquired = _database.StringSetAsync(resourceKey, 1, lockDuration, When.NotExists);
            return isLockAcquired;
        }

        /// <summary>
        /// 释放锁
        /// </summary>
        /// <param name="resourceKey"></param>
        /// <returns></returns>
        public Task ReleaseLockAsync(string resourceKey)
        {
            return _database.KeyDeleteAsync("");
        }
    }
}