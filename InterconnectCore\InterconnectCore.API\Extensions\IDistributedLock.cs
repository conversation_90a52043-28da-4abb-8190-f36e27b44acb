﻿namespace InterconnectCore.API.Extensions
{
    /// <summary>
    /// 分布式锁接口
    /// </summary>
    public interface IDistributedLock
    {
        /// <summary>
        /// 尝试获取分布式锁。
        /// </summary>
        /// <param name="resourceKey">要锁定的资源标识。</param>
        /// <param name="lockDuration">锁的持续时间。</param>
        /// <returns>是否成功获取锁。</returns>
        Task<bool> TryAcquireLockAsync(string resourceKey, TimeSpan? lockDuration = null);

        /// <summary>
        /// 释放分布式锁。
        /// </summary>
        /// <param name="resourceKey">要释放的资源标识。</param>
        Task ReleaseLockAsync(string resourceKey);
    }
}