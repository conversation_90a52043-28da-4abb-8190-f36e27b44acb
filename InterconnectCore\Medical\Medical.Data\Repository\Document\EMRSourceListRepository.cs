﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EMRSourceListRepository : IEMRSourceListRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly GetCacheService _getCacheService;

        public EMRSourceListRepository(
              MedicalDbContext medicalDb
            , IMemoryCache memoryCache
            , GetCacheService getCacheService
            )
        {
            _medicalDbContext = medicalDb;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<List<EMRSourceListInfo>> GetAllEMRSourceDetailAsync()
        {
            dynamic query = null;
            return (await GetCacheAsync(query)) as List<EMRSourceListInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<EMRSourceListInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalDbContext.EMRSourceListInfos.Where(m => m.DeleteFlag != "*")
                                .Select(m => new EMRSourceListInfo
                                {
                                    EMRSourceID = m.EMRSourceID,
                                    SourceType = m.SourceType,
                                    SourceTypeColumn = m.SourceTypeColumn,
                                    SourceID = m.SourceID,
                                    DataType = m.DataType
                                }).ToListAsync();
        }
        public async Task<List<EMRSourceListInfo>> GetAsync(int language, string hospitalID)
        {
            dynamic query = null;
            var datas = await GetCacheAsync(query);
            if (datas == null)
            {
                return null;
            }
            return (datas as List<EMRSourceListInfo>);
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EMRSourceDetail.ToString();
        }

        public async Task<List<EMRSourceListInfo>> GetTPRSource(string hospitalID)
        {
            var list = await (from m in _medicalDbContext.EMRSourceInfos
                              join n in _medicalDbContext.EMRSourceListInfos on m.ID equals n.EMRSourceID
                              where m.DeleteFlag != "*" && n.DeleteFlag != "*" && m.TPRFlag == "1" && !string.IsNullOrEmpty(n.SourceID)
                              && m.HospitalID == hospitalID
                              select n).ToListAsync();
            return list.GroupBy(m => new { m.EMRSourceID, m.SourceType, m.SourceID }).Select(m => m.First()).ToList();
        }
    }
}
