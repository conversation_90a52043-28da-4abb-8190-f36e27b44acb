﻿namespace InterconnectCore.ViewModels
{
    public class ClinicDataView
    {
        /// <summary>
        /// 患者流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime DataDate { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        public TimeSpan DataTime { get; set; }

        /// <summary>
        /// 评估序号
        /// </summary>
        public int AssessListID { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public string DataValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
    }
}
