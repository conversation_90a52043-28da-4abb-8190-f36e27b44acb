﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class StationListRepository : IStationListRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public StationListRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
            )
        {
            _dbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<StationListInfo> GetAsync(int id)
        {
            var list = await GetCacheAsync() as List<StationListInfo>;
            return list.FirstOrDefault(m => m.ID == id);
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<StationListInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            var data = await _dbContext.StationListInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
            return data;
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Station.GetKey(_sessionCommonServer);
        }
        public async Task<List<StationListInfo>> GetAllAsync()
        {
            return (List<StationListInfo>)await GetCacheAsync();
        }
        public async Task<List<SimpleInfo>> GetSimpleList()
        {
            var stations = (List<StationListInfo>)await GetCacheAsync();
            return stations.Select(m => new SimpleInfo
            {
                ID = m.ID,
                Name = m.StationName,
                Code = m.StationCode
            }).ToList();
        }
        public async Task<List<StationListInfo>> GetStationListNoCache()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.StationList.Where(m => m.HospitalID == hospitalID).ToListAsync();
        }
    }
}