﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ClinicDataRepository : IClinicDataRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public ClinicDataRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// 根据assessListID获取病人临床数据
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="assessListID">护理评估序号</param>
        /// <returns></returns>
        public async Task<List<ClinicDataView>> GetPatientClinicDataByAssessListID(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate
            , TimeSpan endTime, int assessListID)
        {
            var data = await _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID
                             && m.DataDate >= startDate && m.DataDate <= endDate && m.AssessListID == assessListID)
                               .Select(m => new ClinicDataView
                               {
                                   ClinicDataID = m.ClinicDataID,
                                   DataDate = m.DataDate,
                                   DataTime = m.DataTime,
                                   AssessListID = m.AssessListID,
                                   DataValue = m.DataValue,
                                   Unit = m.Unit
                               }).ToListAsync();

            data = data.Where(m => m.DataDate.Add(m.DataTime) >= startDate.Add(startTime)
              && m.DataDate.Add(m.DataTime) <= endDate.Add(endTime)).ToList();
            return data;
        }

        /// <summary>
        /// 根据assessListID集合获取病人临床数据
        /// </summary>
        /// <param name="inpatientID">病人住院序号</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="assessListID">护理评估序号</param>
        /// <returns></returns>
        public async Task<List<ClinicDataView>> GetPatientClinicDataByAssessListIDs(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate
            , TimeSpan endTime, List<int> assessListIDs)
        {
            var data = await _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID
                             && m.DataDate >= startDate && m.DataDate <= endDate && assessListIDs.Contains(m.AssessListID))
                               .Select(m => new ClinicDataView
                               {
                                   DataDate = m.DataDate,
                                   DataTime = m.DataTime,
                                   AssessListID = m.AssessListID,
                                   DataValue = m.DataValue,
                                   Unit = m.Unit
                               }).ToListAsync();

            data = data.Where(m => m.DataDate.Add(m.DataTime) >= startDate.Add(startTime)
              && m.DataDate.Add(m.DataTime) <= endDate.Add(endTime)).ToList();
            return data;
        }

        public async Task<List<ClinicDataView>> GetPatientClinicData(string inpatientID, List<DateTime> dates)
        {
            var clinicData = await _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID).ToListAsync();
            var data = clinicData.Where(m => dates.Contains(m.DataDate))
                .Select(m => new ClinicDataView
                {
                    DataDate = m.DataDate,
                    DataTime = m.DataTime,
                    AssessListID = m.AssessListID,
                    DataValue = m.DataValue,
                    Unit = m.Unit
                }).ToList();

            return data;

        }

        public async Task<List<ClinicDataView>> GetPatientClinicDataByTime(string inpatientID, DateTime startDate
            , TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var data = await _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID
                             && m.DataDate >= startDate && m.DataDate <= endDate && m.DeleteFlag != "*")
                               .Select(m => new ClinicDataView
                               {
                                   DataDate = m.DataDate,
                                   DataTime = m.DataTime,
                                   AssessListID = m.AssessListID,
                                   DataValue = m.DataValue,
                                   Unit = m.Unit
                               }).ToListAsync();

            data = data.Where(m => m.DataDate.Add(m.DataTime) >= startDate.Add(startTime)
              && m.DataDate.Add(m.DataTime) <= endDate.Add(endTime)).ToList();
            return data;
        }
        /// <summary>
        /// 根据病人ID获取病人数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public List<ClinicDataInfo> GetClinicDataInfosInpatientIDAsync(string inpatientID)
        {
            var data = _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToList();
            return data;
        }

        public async Task<List<ClinicDataInfo>> GetClinicDataBypatientIDAndDate(string inpatientID, DateTime dataDate)
        {
            return await _medicalDbContext.ClinicDatas.Where(m => m.InpatientID == inpatientID && m.DataDate == dataDate && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<ClinicDataInfo>> GetMedicalDataByIDs(List<string> inpatientIDs, DateTime? startDate = null, DateTime? endDate = null)
        {
            var clinicData = _medicalDbContext.ClinicDatas.Where(m => inpatientIDs.Contains(m.InpatientID) && m.DeleteFlag != "*");

            if (startDate.HasValue)
            {
                clinicData = clinicData.Where(m => m.DataDate >= startDate.Value.Date);
            }
            if (endDate.HasValue)
            {
                clinicData = clinicData.Where(m => m.DataDate <= endDate.Value.Date);
            }
            return await clinicData.ToListAsync();
        }
        /// <summary>
        /// 获取一定时间段内多个患者的仪器数据
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="assessListIDs"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<ClinicDataView>> GetPatientListClinicDataByTime(string[] inpatientIDs, List<int> assessListIDs
            , DateTime startDateTime, DateTime endDateTime, Expression<Func<ClinicDataInfo, bool>> predicate)
        {
            return await _medicalDbContext.ClinicDatas
                .Where(m => inpatientIDs.Contains(m.InpatientID) && assessListIDs.Contains(m.AssessListID) && m.DeleteFlag != "*")
                .Where(predicate)
                .Where(startDateTime.Date == endDateTime.Date, m => m.DataDate == startDateTime.Date && m.DataTime >= startDateTime.TimeOfDay && m.DataTime <= endDateTime.TimeOfDay)
                .Where(startDateTime.Date != endDateTime.Date, m => (m.DataDate == startDateTime.Date && m.DataTime >= startDateTime.TimeOfDay)
                    || (m.DataDate == endDateTime.Date && m.DataTime <= endDateTime.TimeOfDay))
                .Select(m => new ClinicDataView
                {
                    InpatientID = m.InpatientID,
                    DataDate = m.DataDate,
                    DataTime = m.DataTime,
                    AssessListID = m.AssessListID,
                    DataValue = m.DataValue,
                    Unit = m.Unit
                }).ToListAsync();
        }
    }
}
