﻿using System.ComponentModel;

namespace InterconnectCore.Common
{
    public class Enums
    {
        /// <summary>
        /// 性别
        /// </summary>
        public enum Gender
        {
            /// <summary>
            /// 男性
            /// </summary>
            [Description("男")]
            Male = 1,

            /// <summary>
            /// 女性
            /// </summary>
            [Description("女")]
            Female = 2
        }

        /// <summary>
        /// 客户端类别
        /// </summary>
        public enum ClientType
        {
            /// <summary>
            /// PC端
            /// </summary>
            [Description("PC端")]
            PC = 1,

            /// <summary>
            /// 移动端
            /// </summary>
            [Description("移动端")]
            Mobile = 2
        }

        /// <summary>
        /// 菜单快捷类别
        /// </summary>
        public enum ShutCutType
        {
            Common = 0,

            /// <summary>
            /// 用户快捷
            /// </summary>
            [Description("用户快捷")]
            User = 1,

            /// <summary>
            /// 病人快捷
            /// </summary>
            [Description("病人快捷")]
            Patient = 2
        }

        /// <summary>
        /// 文档类型
        /// </summary>
        public class FileType
        {
            /// <summary>
            /// 首次评估 （入院评估）
            /// </summary>
            public const string FirstAssess = "入院评估单";

            /// <summary>
            /// 历次评估
            /// </summary>
            public const string HistoryAssess = "历次评估单";

            /// <summary>
            /// 护理问题
            /// </summary>
            public const string NursingProblem = "护理问题单";

            /// <summary>
            /// 护理计划
            /// </summary>
            public const string NursingPlan = "护理计划单";

            /// <summary>
            /// 风险评估
            /// </summary>
            public const string Risck = "风险评估单";

            /// <summary>
            /// 交班列表
            /// </summary>
            public const string HandoverList = "交班列表单";

            /// <summary>
            /// 交班明细
            /// </summary>
            public const string HandoverDetail = "交班明细单";

            public const string NursingRecords = "护理记录单";

            /// <summary>
            /// 输血单
            /// </summary>
            public const string Blood = "输血单";
        }

        /// <summary>
        /// 记录码
        /// </summary>
        public enum RecordsCode
        {
            /// <summary>
            /// 入院评估码
            /// </summary>
            AdmissionAssess,

            /// <summary>
            /// 历次评估
            /// </summary>
            PhysicalAssessment
        }

        public class RiskTableCode
        {
            public const string ADL = "ActivitiesDailyLiving";
        }
        public enum PatientStatus
        {
            /// <summary>
            /// 住院登记
            /// </summary>
            [Description("住院登记")]
            R = 10,
            /// <summary>
            /// 病房接诊
            /// </summary>
            [Description("病房接诊")]
            I = 30,
            /// <summary>
            /// 预约出院
            /// </summary>
            [Description("预约出院")]
            P = 40,
            /// <summary>
            /// 不在科
            /// </summary>
            [Description("不在科")]
            OUT = 50,
            /// <summary>
            /// 无费退院
            /// </summary>
            [Description("无费退院")]
            N = 55,
            /// <summary>
            /// 出院登记
            /// </summary>
            [Description("出院登记")]
            B = 60,
            /// <summary>
            /// 出院结算
            /// </summary>
            [Description("出院结算")]
            O = 80,
        }
    }
}