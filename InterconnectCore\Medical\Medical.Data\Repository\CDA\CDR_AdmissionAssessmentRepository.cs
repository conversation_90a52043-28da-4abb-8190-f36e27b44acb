﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDR_AdmissionAssessmentRepository : ICDR_AdmissionAssessmentRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDR_AdmissionAssessmentRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<CDR_AdmissionAssessmentInfo> GetByID(string assessMainID)
        {
            return await _cDADBConnect.CDR_AdmissionAssessmentInfos.Where(m => m.PatientAssessMainID == assessMainID).FirstOrDefaultAsync();
        }

        public async Task<DateTime?> GetLast()
        {
            var lastData = await _cDADBConnect.CDR_AdmissionAssessmentInfos
                .OrderByDescending(m => m.TimeStamp.Value).FirstOrDefaultAsync();
            if (lastData == null || lastData.TimeStamp == null)
            {
                return null;
            }
            return lastData.TimeStamp.Value;
        }


        public async Task<bool> Save(CDR_AdmissionAssessmentInfo data)
        {
            try
            {
                _cDADBConnect.Add(data);

                return await _cDADBConnect.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"CDA入院护理评估写入数据失败,ex = {ex},异常数据:{Common.ListToJson.ToJson(data)}");
                return false;
            }
        }

        public async Task<bool> Update(CDR_AdmissionAssessmentInfo data)
        {
            var old = await _cDADBConnect.CDR_AdmissionAssessmentInfos.Where(m => m.PatientAssessMainID == data.PatientAssessMainID).FirstOrDefaultAsync();

            if (old == null)
            {
                return await Save(data);
            }

            try
            {
                _cDADBConnect.Entry(old).CurrentValues.SetValues(data);

                return await _cDADBConnect.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {

                _logger.Error("CDA入院护理评估更新数据失败,异常数据:" + Common.ListToJson.ToJson(data) + $"异常信息为{ex}");

                return false;
            }
        }

        /// <summary>
        /// 获取未推送的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<CDR_AdmissionAssessmentInfo>> GetSyncData(DateTime? startDateTime, DateTime? endDateTime)
        {
            var now = DateTime.Now.Date;
            if (!startDateTime.HasValue)
            {
                startDateTime = now;
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = now.AddDays(1);
            }
            // 开始结束传的是同一天，则取这一天（24小时）的数据
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }
            return await _cDADBConnect.CDR_AdmissionAssessmentInfos.Where(m => m.DataPumpFlag != "*"
            && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime).ToListAsync();
        }
        /// <summary>
        /// 检查数据是否存在
        /// </summary>
        /// <param name="assessMainID"></param>
        /// <returns>bool:存在为true</returns>
        public async Task<bool> CheckExistOrNotAsync(string assessMainID)
        {
            return await _cDADBConnect.CDR_AdmissionAssessmentInfos.Where(m => m.PatientAssessMainID == assessMainID).CountAsync() > 0;
        }
    }
}
