﻿using Newtonsoft.Json;

namespace InterconnectCore.ViewModels
{
    public class DocumentReturnView
    {
        /// <summary>
        /// 病历/报告唯一ID号
        /// </summary>
        [JsonProperty(nameof(ReportID))]
        public string ReportID { get; set; }
        /// <summary>
        /// 报告的具体名称，言简意赅，尽量不带数字字母
        /// </summary>
        [JsonProperty(nameof(ReportName))]
        public string ReportName { get; set; }
        /// <summary>
        /// 一级类型 例如 PACS
        /// </summary>
        [JsonProperty(nameof(ReportType))]
        public string ReportType { get; set; }
        /// <summary>
        /// 二级类型 例如 CT
        /// </summary>
        [JsonProperty(nameof(ReportChildType))]
        public string ReportChildType { get; set; }
        /// <summary>
        /// 是否纸质归档 1-是 非1-否(真对某些需要系统可能会打印纸质并归档的系统)
        /// </summary>
        [JsonProperty(nameof(IsPaperFiling))]
        public string IsPaperFiling { get; set; }
        /// <summary>
        /// 报告完成时间
        /// </summary>
        [JsonProperty(nameof(OperateTime))]
        public string OperateTime { get; set; }
        /// <summary>
        /// 是否完成 1-完成 非1-未完成 针对可能会迟归的报告
        /// </summary>
        [JsonProperty(nameof(IsDone))]
        public string IsDone { get; set; }
    }
}
