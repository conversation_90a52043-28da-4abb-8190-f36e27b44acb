<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.UnitOfWork" Version="3.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\InterconnectCore.Data.Interface\InterconnectCore.Data.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Data\InterconnectCore.Data.csproj" />
    <ProjectReference Include="..\InterconnectCore.Service.Interface\InterconnectCore.Service.Interface.csproj" />
    <ProjectReference Include="..\MedicalExternalCommon.Service\MedicalExternalCommon.Service.csproj" />
    <ProjectReference Include="..\Medical\Medical.Common\Medical.Common.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data.Interface\Medical.Data.Interface.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data\Medical.Data.csproj" />
    <ProjectReference Include="..\Medical\Medical.Models\Medical.Models.csproj" />
    <ProjectReference Include="..\Medical\Medical.ViewModels\Medical.ViewModels.csproj" />
  </ItemGroup>
</Project>