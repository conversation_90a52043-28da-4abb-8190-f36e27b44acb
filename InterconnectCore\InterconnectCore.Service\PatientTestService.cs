﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Common;
using InterconnectCore.Data.Context;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Interface;
using Microsoft.Extensions.Options;
using NLog;

namespace InterconnectCore.Service
{
    public class PatientTestService : IPatientTestService
    {
        #region 引用
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        //medical
        private readonly IStationListRepository _stationListRepository;
        
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly ITestItemToAssessListRepository _testItemToAssessListRepository;
        private readonly IPatientTestResultInfoRepository _patientTestResultInfoRepository;
        public readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IClinicDataRepository _clinicDataRepository;

        //interconnect
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private readonly ISyncLogService _syncLogService;
        public readonly IUnitOfWork<DataOutContext> _unitOfOutWork;
        private readonly CommonHelper _commonHelper;
        #endregion

        #region 常量
        /// <summary>
        /// 检验项目码
        /// </summary>
        private const string TESTSCHEDULE_TESTITEMCODE_GLU = "GLU";
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";
        #endregion

        #region 构造器
        public PatientTestService(
            IStationListRepository stationListRepository,
            IInpatientDataRepository inpatientDataRepository,
            ITestItemToAssessListRepository testItemToAssessListRepository,
            IPatientTestResultInfoRepository patientTestResultInfoRepository,
            IUnitOfWork<MedicalDbContext> unitOfWork,
            IClinicDataRepository clinicDataRepository,
            IOptions<ViewModels.SystemConfig> config,
            ISyncLogService syncLogService,
            IUnitOfWork<DataOutContext> unitOfOutWork,
            CommonHelper commonHelper
            )
        {
            _stationListRepository = stationListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _testItemToAssessListRepository = testItemToAssessListRepository;
            _patientTestResultInfoRepository = patientTestResultInfoRepository;
            _unitOfWork = unitOfWork;
            _clinicDataRepository = clinicDataRepository;
            _config = config;
            _syncLogService = syncLogService;
            _unitOfOutWork = unitOfOutWork;
            _commonHelper = commonHelper;
        }

        #endregion
        /// <summary>
        /// 同步患者检验信息
        /// </summary>
        /// <param name="patientTestResultViewList"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientTestResults(List<PatientTestResultView> patientTestResultViewList)
        {
            if (patientTestResultViewList == null || patientTestResultViewList.Count <= 0)
            {
                _logger.Error("检验数据同步，数据为空！");
                return false;
            }
            var hospitalID = _config.Value.HospitalID;
            var medicalInPatientInfo = await _inpatientDataRepository.GetAsyncByCaseNumber(patientTestResultViewList[0].CaseNumber, hospitalID);
            if (medicalInPatientInfo == null)
            {
                _logger.Info("检验数据同步，InPatientList CaseNumber [" + patientTestResultViewList[0].CaseNumber + "]   查询信息错误!");
                return false;
            }
            //检验项目字典
            var testItemToAssessList = await _testItemToAssessListRepository.GetAsync();
            var groupList = patientTestResultViewList.GroupBy(p => p.TestNo);
            foreach (var group in groupList)
            {
                await SyncOneTestDataList(group.ToList(), testItemToAssessList, medicalInPatientInfo, hospitalID);
            }
            return true;
        }

        /// <summary>
        /// 同步一组检验数据
        /// </summary>
        /// <param name="patientTestResultViews"></param>
        /// <param name="testItemToAssessList"></param>
        /// <param name="medicalInPatientInfo"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> SyncOneTestDataList(List<PatientTestResultView> patientTestResultViews, List<TestItemToAssessListInfo> testItemToAssessList, InpatientDataInfo medicalInPatientInfo, string hospitalID)
        {
            // 根据CaseNumber集合获取在院病人数据     
            if (patientTestResultViews.Count <= 0)
            {
                return true;
            }
            string tableName = "TestSchedule";
            _logger.Info(tableName + " 开始进行数据同步，数据条数：" + patientTestResultViews.Count);
            var patientProfileList = new List<PatientProfile>();
            //因给的检验数据会发生重复，所以先洗同一批保存数据
            var testDataTestCodeList = patientTestResultViews.Select(m => m.TestCode).Distinct().ToList();
            foreach (var item in testDataTestCodeList)
            {
                var oneTestDataListTemp = patientTestResultViews.Where(m => m.TestCode == item).ToList();

                //判断字典中是否存在，如果不存在不进行同步
                var testItemToAssessTemp = testItemToAssessList.Find(m => m.TestItemCode == item);
                if (testItemToAssessTemp == null)
                {
                    _logger.Info("TestItemCode:" + item + "TestItemToAssessList字典不存在，不进行同步,TestNO:" + patientTestResultViews[0].TestNo);
                    continue;
                }
                try
                {
                    var patientProfile = await SyncOneTest(oneTestDataListTemp[0], medicalInPatientInfo, testItemToAssessList, hospitalID);
                    if (patientProfile != null && patientProfile.InpatientID!=null)
                    {
                        patientProfileList.Add(patientProfile);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error("检验数据,TestNO" + patientTestResultViews[0].TestNo + "TestItemCode" + item + "||同步失败||" + ex.ToString());
                    _syncLogService.InsertSyncLog(1, "1", "检验同步", "SyncOneTestDataList", "检验数据,TestNO" + patientTestResultViews[0].TestNo + "TestItemCode" + item + "||同步失败||", "TongBu", true);
                }
            }

            try
            {
                var flag = _unitOfWork.SaveChanges() > 0;
                //检验数据写入Profile
                await AddProfile(patientProfileList);
                return flag;
            }
            catch (Exception ex)
            {
                _logger.Error("同步检验数据,TestNO," + patientTestResultViews[0].TestNo + "失败" + ex.ToString());
                _syncLogService.InsertSyncLog(1, "1", "检验同步", "SyncOneTestDataList", "同步检验数据, TestNO" + patientTestResultViews[0].TestNo + "失败", "TongBu", true);
                return false;
            }
        }

        /// <summary>
        /// 添加检验数据到Profile
        /// </summary>
        /// <param name="patientProfileList"></param>
        /// <returns></returns>
        public async Task AddProfile(List<PatientProfile> patientProfileList)
        {
            if (patientProfileList.Count <= 0)
            {
                return;
            }
            _logger.Info("开始写patientProfiles");
            try
            {
                await _commonHelper.AddProfile(patientProfileList);
            }
            catch (Exception ex)
            {
                _logger.Error("同步检验数据成功,CaseNumber:" + patientProfileList[0].CaseNumber + "，调用Profile失败" + ex.ToString());
                return;
            }
        }

        /// <summary>
        /// 同步一条检验数据 
        /// </summary>
        /// <param name="item"></param>
        /// <param name="medicalInPatientInfo"></param>
        /// <param name="testItemToAssessList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<PatientProfile> SyncOneTest(PatientTestResultView item
            , InpatientDataInfo medicalInPatientInfo
             , List<TestItemToAssessListInfo> testItemToAssessList, string hospitalID)
        {
            string tableName = "TestSchedule";
            var patientProfile = new PatientProfile();
            //格式化检验日期
            var testDate = Convert.ToDateTime(item.TestDate.ToString("yyyy-MM-dd HH:mm:ss"));

            var testItemToAssessListTemp = testItemToAssessList.Find(m => m.TestItemCode == item.TestCode && item.Specimen.Contains(m.Specimen) && m.ItemGroup == item.TestGroupName);

            testItemToAssessListTemp ??= testItemToAssessList.Find(m => m.TestItemCode == item.TestCode && item.Specimen.Contains(m.Specimen) && m.ItemGroup == null);
            testItemToAssessListTemp ??= testItemToAssessList.Find(m => m.TestItemCode == item.TestCode && m.Specimen == null && m.ItemGroup == null);

            if (testItemToAssessListTemp == null)
            {
                _logger.Info(tableName + "CaseNumber[" + item.CaseNumber + "] TestItem[" + item.TestItem + "]  表: TestItemToAssessList TestItemCode [" + item.TestCode + "] 查询信息错误!");
                return null;
            }
            var assessListId = testItemToAssessListTemp.AssessListID;
            //获取PatientTestResult表中的检验项目              
            var tempMedicalTest = _patientTestResultInfoRepository.GetOneTest(item.CaseNumber, item.TestNo, item.TestCode, testDate);

            //如果不存在进行新增
            if (tempMedicalTest == null)
            {
                var patientTest = await InsterTest(item, assessListId, testDate, medicalInPatientInfo);
                patientProfile = CreateProfile(medicalInPatientInfo, patientTest, hospitalID, assessListId);
            }
            else
            {
                //更新数据
                var upflag = UPMedicalTest(tempMedicalTest, item, testDate, assessListId);
                if (upflag)
                {
                    _logger.Info("修改检验数据," + "CaseNumber:" + item.CaseNumber + "|TestNO:" + item.TestNo + "|TestItemCode:" + item.TestCode + "|testDate:" + testDate.ToString());
                    //修改ClinicData中的数据血糖的数据                  
                    if (item.TestCode.Trim() == TESTSCHEDULE_TESTITEMCODE_GLU)
                    {
                        _logger.Info("CaseNumber=" + item.CaseNumber + " 增加血糖数据！");
                        await SaveClinicData(tempMedicalTest, medicalInPatientInfo);
                    }
                    patientProfile = CreateProfile(medicalInPatientInfo, tempMedicalTest, hospitalID, assessListId);
                }
            }
            return patientProfile;
        }
        /// <summary>
        /// 增加检验数据
        /// </summary>
        /// <param name="item"></param>
        /// <param name="assessListID"></param>
        /// <param name="testDate"></param>
        /// <param name="medicalInPatientInfo"></param>
        /// <returns></returns>
        private async Task<PatientTestResultInfo> InsterTest(PatientTestResultView item, int assessListID, DateTime testDate, InpatientDataInfo medicalInPatientInfo)
        {
            var patientTestResult = new PatientTestResultInfo
            {
                CaseNumber = item.CaseNumber,  //病历号
                TestNo = item.TestNo, //检验单号
                TestCode = item.TestCode,    //检验代码
                TestDate = testDate,   //检验日期
                TestItem = item.TestItem,    //项目名称
                AssessListID = assessListID,    //系统评估码 ,因为发生一对多问题，所以这个字段失去意义                      
                TestValue = item.TestValue,   //结果值
                Unit = item.Unit,    //单位
                NormalRange = item.NormalRange, //正常范围
                NormalAbnormal = item.NormalAbnormal,  //正常或异常
                Description = item.TestGroupName, //说明
                TestGroupName = item.TestGroupName,
                TestGroupCode = item.TestGroupCode,
                ModifyPersonID = MODIFYPERSONID,  //异动人员
                ModifyDate = DateTime.Now, //异动时间
                DeleteFlag = ""  //删除标识
            };
            //葡萄糖
            if (item.TestCode.Trim() == TESTSCHEDULE_TESTITEMCODE_GLU)
            {
                _logger.Info("CaseNumber=" + patientTestResult.CaseNumber + " 增加血糖数据！");
                await SaveClinicData(patientTestResult, medicalInPatientInfo);
            }
            await _unitOfWork.GetRepository<PatientTestResultInfo>().InsertAsync(patientTestResult);
            return patientTestResult;
        }
        /// <summary>
        /// 修改检验数据
        /// </summary>
        /// <param name="patientTestResultInfo"></param>
        /// <param name="item"></param>
        /// <param name="testDate"></param>
        /// <param name="assessListID"></param>
        private static bool UPMedicalTest(PatientTestResultInfo patientTestResultInfo, PatientTestResultView item, DateTime testDate, int assessListID)
        {
            var upFlag = false;
            //项目名称
            if (patientTestResultInfo.TestItem != item.TestItem)
            {
                patientTestResultInfo.TestItem = item.TestItem;
                upFlag = true;
            }
            //系统评估码 
            if (patientTestResultInfo.AssessListID != assessListID)
            {
                patientTestResultInfo.AssessListID = assessListID;
                upFlag = true;
            }
            //结果值
            if (patientTestResultInfo.TestValue != item.TestValue)
            {
                patientTestResultInfo.TestValue = item.TestValue;
                upFlag = true;
            }
            //单位
            if (patientTestResultInfo.Unit != item.Unit)
            {
                patientTestResultInfo.Unit = item.Unit;
                upFlag = true;
            }
            //正常范围
            if (patientTestResultInfo.NormalRange != item.NormalRange)
            {
                patientTestResultInfo.NormalRange = item.NormalRange;
                upFlag = true;
            }
            //正常或异常
            if (patientTestResultInfo.NormalAbnormal != item.NormalAbnormal)
            {
                patientTestResultInfo.NormalAbnormal = item.NormalAbnormal;
                upFlag = true;
            }
            if (upFlag)
            {
                patientTestResultInfo.ModifyPersonID = MODIFYPERSONID;  //异动人员
                patientTestResultInfo.ModifyDate = DateTime.Now; //异动时间
                patientTestResultInfo.DeleteFlag = "";  //删除标识
            }
            return upFlag;
        }
        /// <summary>
        /// 创建Profile数据
        /// </summary>
        /// <param name="inPatientData"></param>
        /// <param name="testResult"></param>
        /// <param name="hospitalID"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        private static PatientProfile CreateProfile(InpatientDataInfo inPatientData, PatientTestResultInfo testResult, string hospitalID, int assessListID)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatientData.ID,
                CaseNumber = inPatientData.CaseNumber,
                PatientID = inPatientData.PatientID,
                ChartNo = inPatientData.ChartNo,
                ModelName = "HIS.Lab",//写死，不能修改，会影响profile
                Source = "I",
                ProfileDate = testResult.TestDate.Date,
                ProfileTime = testResult.TestDate.TimeOfDay,

                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
            };
            if (assessListID != 0)
            {
                patientProfile.SerialNumber = inPatientData.CaseNumber + "|"
                + testResult.TestNo + "|" + testResult.TestCode + "|" + assessListID;
            }
            else
            {
                patientProfile.SerialNumber = inPatientData.CaseNumber + "|"
                + testResult.TestNo + "|" + testResult.TestCode;
            }

            //调用其他方法，传入原有assessListID
            patientProfile.AssessListID = assessListID;
            //问题
            patientProfile.AssessValue = testResult.TestValue;
            patientProfile.Unit = testResult.Unit;
            patientProfile.AbnormalFlag = testResult.NormalAbnormal;
            patientProfile.NomalRange = testResult.NormalRange;
            return patientProfile;
        }
        
        /// <summary>
        /// 保存仪器数据
        /// </summary>
        /// <param name="patientTestResultInfo"></param>
        /// <param name="inPatientData"></param>
        /// <returns></returns>
        private async Task SaveClinicData(PatientTestResultInfo patientTestResultInfo, InpatientDataInfo inPatientData)
        {
            //判断一个时间点，是有已经有数据
            var assessListID = 1957;
            var oldClinicDataList = await _clinicDataRepository.GetPatientClinicDataByAssessListID(inPatientData.ID, patientTestResultInfo.TestDate.Date,
                 patientTestResultInfo.TestDate.TimeOfDay, patientTestResultInfo.TestDate.Date,
                 patientTestResultInfo.TestDate.TimeOfDay, assessListID);
            var ids = oldClinicDataList.Select(m => m.ClinicDataID).ToList();
            if (ids.Count > 0)
            {
                foreach (var item in ids)
                {
                    _unitOfWork.GetRepository<ClinicDataInfo>().Delete(item);
                }
            }
            var clinicDataInfo = new ClinicDataInfo()
            {
                InpatientID = inPatientData.ID,
                PatientID = inPatientData.PatientID,
                StationID = inPatientData.StationID,
                BedID = inPatientData.BedID,
                System = "HIS",
                Source = "1",
                SourceID = "1",
                MachineID = "1",
                DataDate = patientTestResultInfo.TestDate.Date,
                DataTime = patientTestResultInfo.TestDate.TimeOfDay,
                AssessListID = assessListID,
                DataValue = patientTestResultInfo.TestValue,
                Unit = patientTestResultInfo.Unit,
                ModifyPersonID = patientTestResultInfo.ModifyPersonID,
                ModifyDate = patientTestResultInfo.ModifyDate.Value,
                DeleteFlag = patientTestResultInfo.DeleteFlag,
                SyncDateTime = DateTime.Now,
            };
            _unitOfWork.GetRepository<ClinicDataInfo>().Insert(clinicDataInfo);
        }

    }
}
