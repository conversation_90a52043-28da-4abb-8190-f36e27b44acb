﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ProblemToInterventionRepository : IProblemToInterventionRepository
    {
        public MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public ProblemToInterventionRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<ProblemToInterventionInfo>> GetAsync(int problemID)
        {
            var datas = await GetCacheAsync() as List<ProblemToInterventionInfo>;
            return datas.Where(t => t.ProblemID == problemID).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ProblemToInterventionInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);

            if (int.TryParse(hospitalID?.ToString(), out int parsedHospitalID))
            {
                return await _medicalDbContext.ProblemToInterventions
                    .Where(m => m.HospitalID == parsedHospitalID && m.DeleteFlag != "*")
                    .ToListAsync();
            }
            else
            {
                // 如果 hospitalID 无法转换为 int，处理这个情况
                return new List<ProblemToInterventionInfo>();  // 或者根据你的需求返回其他结果
            }
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.ProblemToIntervention.GetKey(_sessionCommonServer);
        }

        public async Task<ProblemToInterventionInfo> GetAsync(int problemID, int interventionID)
        {
            var datas = await GetCacheAsync() as List<ProblemToInterventionInfo>;
            return datas.Find(t => t.ProblemID == problemID && t.InterventionID == interventionID);
        }

        public async Task<List<ProblemToInterventionInfo>> GetTPRData()
        {
            var datas = await GetCacheAsync() as List<ProblemToInterventionInfo>;
            return datas.Where(m => m.TPRFlag == true).ToList();
        }

        public async Task<List<ProblemToInterventionInfo>> GetEventTriggerData()
        {
            var datas = await GetCacheAsync() as List<ProblemToInterventionInfo>;
            return datas.Where(m => m.TriggerPerformEvent != 0).ToList();
        }

        public async Task<List<ProblemToInterventionInfo>> GetAsync(List<int> problemIDs)
        {
            var datas = await GetCacheAsync() as List<ProblemToInterventionInfo>;
            return datas.Where(t => problemIDs.Contains(t.ProblemID)).ToList();
        }
    }
}
