﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.Patient;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientConsultRepository : IPatientConsultRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public PatientConsultRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        /// <summary>
        /// 根据会诊ID查询会诊信息
        /// </summary>
        /// <param name="patientConsultID"></param>
        /// <returns></returns>
        public async Task<PatientConsultInfo> GetByPatientConsultID(string patientConsultID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ID == patientConsultID && t.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据病人id查询
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientConsultInfo>> GetByPatientInpatientID(string inpatientID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientConsultInfo>> GetConsultByConsultGroupID(string patientConsultID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ConsultGroupID == patientConsultID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 会诊统计
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="consultStationID"></param>
        /// <param name="replyStationID"></param>
        /// <returns></returns>
        public async Task<List<PatientConsultInfo>> GetConsultStatistics(DateTime startDate, DateTime endDate, int consultStationID, int replyStationID)
        {
            List<PatientConsultInfo> patientList = new List<PatientConsultInfo>();
            if (consultStationID == 999999 && replyStationID == 999999)
            {
                patientList = await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyDate.Value.Date >= startDate && t.ReplyDate.Value.Date <= endDate && t.DeleteFlag != "*").ToListAsync();
            }
            if (consultStationID != 999999 && replyStationID == 999999)
            {
                patientList = await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyDate.Value.Date >= startDate && t.ReplyDate.Value.Date <= endDate && t.ConsultStationID == consultStationID && t.DeleteFlag != "*").ToListAsync();
            }
            if (consultStationID == 999999 && replyStationID != 999999)
            {
                patientList = await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyDate.Value.Date >= startDate && t.ReplyDate.Value.Date <= endDate && t.ReplyStationID == replyStationID && t.DeleteFlag != "*").ToListAsync();
            }
            if (consultStationID != 999999 && replyStationID != 999999)
            {
                patientList = await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyDate.Value.Date >= startDate && t.ReplyDate.Value.Date <= endDate && t.ConsultStationID == consultStationID && t.ReplyStationID == replyStationID && t.DeleteFlag != "*").ToListAsync();
            }
            return patientList;
        }

        public async Task<List<PatientConsultInfo>> GetPatientConsultByConsultedEmployeeID(string consultedEmployeeID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(m => (m.ConsultedEmployeeID == consultedEmployeeID || m.AssignEmployeeID == consultedEmployeeID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientConsultInfo>> GetPatientConsultByDate(DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ConsultDate >= startTime && t.ConsultDate <= endTime && t.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据回复病区查询会诊
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<List<PatientConsultInfo>> GetPatientConsultByReplyDate(DateTime startTime, DateTime endTime)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ConsultDate.Date >= startTime && t.ConsultDate.Date <= endTime && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientConsultInfo>> GetPatientConsultByReplySationID(int stationID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyStationID == stationID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据发起病区查询
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientConsultInfo>> GetPatientConsultBySationID(int stationID)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ConsultStationID == stationID && t.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 取得未回复内容
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public async Task<List<CheckCousnlt>> GetUnReplyList(DateTime dateTime)
        {
            var query = await (from a in _medicalDbContext.PatientConsultInfos
                               join b in _medicalDbContext.InpatientDatas on a.InpatientID equals b.ID
                               where a.ConsultDate >= dateTime && !a.ReplyDate.HasValue && a.DeleteFlag != "*"
                               && InHospitalStatus.INHOSPITALLIST.Contains(b.InHospitalStatus ?? -1) && b.DeleteFlag != "*"
                               select new CheckCousnlt
                               {
                                   PatientConsultID = a.ID,
                                   InpatientID = a.InpatientID,
                                   ConsultDate = a.ConsultDate,
                                   StationID = a.StationID,
                                   ConsultContent = a.ConsultContent,
                                   EmergencyFlag = a.EmergencyFlag
                               }).OrderBy(m => m.StationID).ToListAsync();

            return query;
        }
        /// <summary>
        /// 获取未回复消息
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientConsultInfo>> GetNoReplyData(DateTime referenceTime)
        {
            return await _medicalDbContext.PatientConsultInfos.Where(t => t.ReplyDate == null && t.DeleteFlag != "*" && t.ConsultDate.AddDays(1) <= referenceTime)
                .Select(m => new PatientConsultInfo
                {
                    ConsultContent = m.ConsultContent,
                    ConsultStationID = m.ConsultStationID,
                    ConsultEmployeeID = m.ConsultEmployeeID,
                    ConsultDate = m.ConsultDate,
                    ReplyStationID = m.ReplyStationID
                })
                .ToListAsync();
        }
    }
}
