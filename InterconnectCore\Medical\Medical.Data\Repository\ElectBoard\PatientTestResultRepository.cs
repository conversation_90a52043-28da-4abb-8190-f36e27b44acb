﻿using Medical.Data.Context;
using Medical.Data.Interface.ElectBoard;
using Medical.Models.Interconnect;
using Medical.ViewModels.ElectBoard;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository.ElectBoard
{
    public class PatientTestResultRepository : IPatientTestResultRepository
    {
        #region -- 字段
        private MedicalDbContext _medicalDbContext;
        #endregion

        #region -- 构造函数
        public PatientTestResultRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }
        #endregion

        public async Task<List<PatientTestResultView>> GetTestResultByCaseNumberAsync(string caseNumber)
        {
            return await (from a in _medicalDbContext.PatientTestResultInfos
                          where a.DeleteFlag != "*" && a.CaseNumber == caseNumber
                          select new PatientTestResultView
                          {
                              CaseNumber = a.<PERSON>,
                              TestNo = a.TestNo,
                              TestCode = a.TestCode,
                              TestDate = a.TestDate,
                              TestItem = a.TestItem,
                              TestValue = a.TestValue,
                              Unit = a.Unit,
                              NormalRange = a.NormalRange,
                              NormalAbnormal = a.NormalAbnormal,
                              TestGroupName = a.TestGroupName,
                              TestGroupCode = a.TestGroupCode
                          }).ToListAsync();
        }
        public async Task<List<TestResultView>> GetTestResultDetailAsync(string caseNumber, string testNo)
        {
            return await (from a in _medicalDbContext.PatientTestResultInfos
                          where a.DeleteFlag != "*" && a.CaseNumber == caseNumber && a.TestNo == testNo
                          select new TestResultView
                          {
                              TestItem = a.TestItem,
                              TestValue = a.TestValue,
                              Unit = a.Unit,
                              NormalRange = a.NormalRange,
                              NormalAbnormal = a.NormalAbnormal,
                              ResulTime = a.TestDate.ToString("yyyy-MM-dd HH:mm")
                          }).ToListAsync();
        }
    }
}
