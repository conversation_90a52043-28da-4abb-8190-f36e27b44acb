﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Query;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientOrderRepository : IPatientOrderRepository
    {
        private readonly MedicalDbContext _dbContext = null;

        public PatientOrderRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        /// <summary>
        /// 根据病人inpatientID，获取没有取消与删除的医嘱
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetAsync(string inpatientID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.CancalDate == null && m.DeleteFlag != "*").ToListAsync();
        }




        /// <summary>
        /// 根据病人inpatientID及OrderIDS，获取医嘱
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetByInpatientIDAndOrderIDAsync(string inpatientID, List<string> orderIDs)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && orderIDs.Contains(m.OrderID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据医嘱ID,获取医嘱数据
        /// </summary>
        /// <param name="OrderID"></param>
        /// <returns></returns>
        public async Task<PatientOrderMainInfo> GetPatientOrderMainByOrderID(string OrderID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.OrderID == OrderID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据病人inpatientID，获取所有医嘱
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetAllOrderByInpatientIDAsync(string inpatientID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID).ToListAsync();
        }

        /// <summary>
        /// 获取病人医嘱主记录
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <param name="stationID">病区</param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetAsync(string inpatientID, int stationID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID
                          && m.StationID == stationID
                          && m.CancalDate == null && m.DeleteFlag != "*").ToListAsync();
        }


        public async Task<List<PatientOrderMainInfo>> GetAsync(DateTime startDate)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.DeleteFlag != "*"
            && m.StartDate >= startDate).ToListAsync();
        }

        public async Task<PatientOrderMainInfo> GetByIDAsync(string id)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.PatientOrderMainID == id && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetViewByIDAsync(List<string> orderIds)
        {
            return await _dbContext.PatientOrderMains.Where(m => orderIds.Contains(m.OrderID) && m.DeleteFlag != "*")
                .Select(m => new PatientOrderMainInfo
                {
                    CaseNumber = m.CaseNumber,
                    OrderID = m.OrderID,
                    InpatientID = m.InpatientID
                }).ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetByCaseNumberAsync(string caseNumber)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").ToListAsync();
        }

        public List<PatientOrderMainInfo> GetPatientByOneorder(string caseNumber, string ChartNO, string OrderID)
        {
            return _dbContext.PatientOrderMains.Where(m => m.ChartNo == ChartNO && m.CaseNumber == caseNumber && m.OrderID == OrderID && m.DeleteFlag != "*").ToList();
        }

        public async Task<List<PatientOrderMainInfo>> GetOrderByCaseNumberAndOrders(List<string> orderIDs)
        {
            return await _dbContext.PatientOrderMains.Where(m => orderIDs.Contains(m.OrderID) && m.DeleteFlag != "*").ToListAsync();
        }

        public List<PatientOrderMainInfo> GetPatientByOneorder(string caseNumber, string ChartNO)
        {
            return _dbContext.PatientOrderMains.Where(m => m.ChartNo == ChartNO && m.CaseNumber == caseNumber && m.DeleteFlag != "*").ToList();
        }

        public List<PatientOrderMainInfo> GetPatientByOneOrder(string OrderID)
        {
            return _dbContext.PatientOrderMains.Where(m => m.OrderID == OrderID && m.DeleteFlag != "*").ToList();
        }


        public async Task<List<PatientOrderMainInfo>> GetAsync(string caseNumber, DateTime startDate)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.CaseNumber == caseNumber
            && m.StartDate > startDate && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetByStationIDAsync(int stationID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.StationID == stationID
             && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetByStationIDAsync(int stationID, DateTime startDate)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.StationID == stationID
             && m.AddDate > startDate && m.DeleteFlag != "*").ToListAsync();
        }


        /// <summary>
        /// 获取病人医嘱
        /// </summary>
        /// <param name="orderIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetByOrderIDs(string[] orderIDs)
        {
            var patientOrderMainList = new List<PatientOrderMainInfo>();
            for (int i = 0; i < orderIDs.Length; i++)
            {
                var tempList = await _dbContext.PatientOrderMains.Where(m =>
               m.OrderID == orderIDs[i] && m.DeleteFlag != "*").ToListAsync();
                patientOrderMainList = patientOrderMainList.Union(tempList).ToList();
            }
            return patientOrderMainList;
        }
        /// <summary>
        /// 根据住院号获取医嘱
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetByInpatientID(string inpatientID, int stationID)
        {
            return await _dbContext.PatientOrderMains.Where(m =>
            m.InpatientID == inpatientID && m.StationID == stationID &&
            m.DeleteFlag != "*").OrderBy(m => m.HISOrderSort).ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetAsync(OrdersQuery query)
        {
            var linq = _dbContext.PatientOrderMains.Where(m =>
                (m.OrderType == OrdersType.Temp.ToString("d") && ((m.StartDate == query.StartTime.Date && m.StartTime >= query.StartTime.TimeOfDay) || m.StartDate > query.StartTime.Date))
                || m.OrderType == OrdersType.Long.ToString("d"));
            if (query.InPatientID != null)
            {
                linq = linq.Where(t => t.InpatientID == query.InPatientID);
            }
            if (query.StationID != null)
            {
                linq = linq.Where(t => t.StationID == query.StationID.Value);
            }
            if (query.ValidFlag)
            {
                linq = linq.Where(t => t.OrderStatus == OrderStatus.Perform.ToString("d"));
            }
            if (query.OrdersFlag)
            {

            }
            else
            {

            }
            return await linq.ToListAsync();
        }

        public async Task<PatientOrderMainInfo> GetLastDataAsync()
        {
            return await _dbContext.PatientOrderMains.OrderByDescending(m => m.ModifyDate).FirstOrDefaultAsync();
        }

        public async Task<List<OrderMainDetailView>> GetUnEndAsync(int stationID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.StationID == stationID
                    && m.OrderType == "1"  //长期医嘱
                    && !m.EndDate.HasValue
                    && !m.CancalDate.HasValue
                    && m.PrintFlag == "*"
                    && m.DeleteFlag != "*")
                    .Select(m => new OrderMainDetailView
                    {
                        PatientOrderMainID = m.PatientOrderMainID,
                        OrderID = m.OrderID,
                        OrderType = m.OrderType,
                        InpatientID = m.InpatientID,
                        PatientID = m.PatientID,
                        StationID = m.StationID,
                        BedID = m.BedID,
                        CaseNumber = m.CaseNumber,
                        ChartNo = m.ChartNo,
                        BedNumber = m.BedNumber,
                        StartDate = m.StartDate,
                        StartTime = m.StartTime,
                        EndDate = m.EndDate,
                        EndTime = m.EndTime,
                        AddEmployeeID = m.AddEmployeeID,
                        AddDate = m.AddDate,
                        ConfirmPersonID = m.ConfirmPersonID,
                        ConfirmDate = m.ConfirmDate,
                        CancalPersonID = m.CancalPersonID,
                        CancalDate = m.CancalDate,
                        OrderStatus = m.OrderStatus,
                        FrequencyID = m.FrequencyID,
                        FrequencyStartTime = m.FrequencyStartTime,
                        Interval = m.Interval,
                        TimesOfDay = m.TimesOfDay,
                        OrderPattern = m.OrderPattern,
                        FirstDayTimes = m.FirstDayTimes,
                        FirstDayStartTime = m.FirstDayStartTime,
                        PrintFlag = m.PrintFlag,
                        LastPerformTime = m.LastPerformTime,
                        HISFrequency = m.HISFrequency,
                        HISFrequencySchedule = m.HISFrequencySchedule,
                        HISOrderSort = m.HISOrderSort,
                        CheckEmployeeID = m.CheckEmployeeID,
                        CheckDateTime = m.CheckDateTime,
                        PerformFlag = m.PerformFlag,
                        RecordFlag = m.RecordFlag
                    }).ToListAsync();
        }

        /// <summary>
        /// 临时使用
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllData()
        {
            return await _dbContext.PatientOrderMains.Select(m => m.OrderID).ToListAsync();
        }

        //2020-05-23加上排除频次为0的医嘱,展不出排程不用取出
        public async Task<List<PatientOrderMainInfo>> GetByStartDateTime(string inpatientID, int stationID, DateTime startDate)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID
             && m.StationID == stationID && m.StartDate <= startDate && m.FrequencyID != 0
             && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetUnCheckOrder(string inpatientID, int stationID, DateTime shiftEndTime)
        {
            //排除需要打印的或药品A、B类的
            var list = await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID
            && m.CheckDateTime == null && string.IsNullOrEmpty(m.CheckEmployeeID) && m.OrderStatus != "4"
            && m.PrintFlag != "*" && m.OrderPattern != "A" && m.OrderPattern != "B"
            && m.StationID == stationID && m.DeleteFlag != "*")
               .Select(m => new PatientOrderMainInfo
               {
                   StartDate = m.StartDate,
                   StartTime = m.StartTime,
                   OrderType = m.OrderType,
                   EndDate = m.EndDate,
                   CancalDate = m.CancalDate
               }).ToListAsync();

            //当天 班次结束时间之前，或小于当天
            list = list.Where(m => (m.StartDate == shiftEndTime.Date && m.StartTime < shiftEndTime.TimeOfDay)
            || m.StartDate < shiftEndTime.Date).ToList();

            for (int i = list.Count - 1; i >= 0; i--)
            {
                if (list[i].OrderType == "1")
                {
                    //排除掉长期医嘱，已经作废或已经停止的
                    if (list[i].EndDate.HasValue || list[i].CancalDate.HasValue)
                    {
                        list.Remove(list[i]);
                    }
                }
                else if (list[i].OrderType == "0")
                {
                    //排除掉已经取消的临时医嘱
                    if (list[i].CancalDate.HasValue)
                    {
                        list.Remove(list[i]);
                    }
                }
            }
            return list;
        }
        /// <summary>
        /// 获取未出院病人的未审核医嘱信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetUnCheckedOrderMain()
        {
            //return await _dbContext.PatientOrderMains.Where(m =>m.DeleteFlag != "*" && m.CheckDateTime == null).ToListAsync();
            //var result = (from orderItem in _dbContext.PatientOrderMains Where _dbContext.InpatientData.Any(m => m.DischargeDate == null &&));
            var result = (from orderMain in _dbContext.PatientOrderMains
                          where _dbContext.InpatientDatas.Any(
                          m => orderMain.InpatientID == m.ID
                          && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                          && m.DeleteFlag != "*") && orderMain.DeleteFlag != "*"
                          && orderMain.CheckDateTime == null
                          select orderMain);
            return await result.ToListAsync();
        }
        public async Task<List<PatientOrderMainInfo>> GetByIDs(string[] patientOrderMainIDs)
        {
            return await _dbContext.PatientOrderMains.Where(m => patientOrderMainIDs.Contains(m.PatientOrderMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<Dictionary<string, string>>> GeOrdersByDateTime(int stationID, DateTime date, string hospitalID)
        {
            return await (from orderMain in _dbContext.PatientOrderMains
                          join orderDetail in _dbContext.PatientOrderDetails
                          on new { orderMain.PatientOrderMainID } equals new { orderDetail.PatientOrderMainID }
                          join inpatient in _dbContext.InpatientDatas
                          on new { orderMain.InpatientID } equals new { InpatientID = inpatient.ID }
                          where orderMain.StationID == stationID && orderMain.StartDate.Date == date
                          && orderMain.DeleteFlag != "*" && orderDetail.DeleteFlag != "*"
                          && inpatient.DeleteFlag != "*" && inpatient.HospitalID == hospitalID
                          && InHospitalStatus.INHOSPITALLIST.Contains(inpatient.InHospitalStatus ?? -1)
                          select new Dictionary<string, string>()
                          {
                              { "InpatientID", orderMain.InpatientID },
                              { "BedNumber", orderMain.BedNumber },
                              { "OrderContent" , orderDetail.OrderContent??"".Trim() },
                              { "OrderDescription" , orderDetail.OrderDescription??"".Trim() },
                              { "OrderCode", orderDetail.OrderCode },
                              { "StartTime", orderMain.StartDate.Add(orderMain.StartTime).ToString("yyyy-MM-dd HH:mm") },
                              { "OrderStatus",orderMain.OrderStatus}
                          }).ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetTmpOrderByTime(DateTime dateTime)
        {
            var date = dateTime.Date;
            var time = dateTime.TimeOfDay;

            return await (from orderMain in _dbContext.PatientOrderMains
                          join orderDetail in _dbContext.PatientOrderDetails
                          on new { orderMain.PatientOrderMainID } equals new { orderDetail.PatientOrderMainID }
                          where orderDetail.OrderPattern == "A" && orderDetail.StartDate == date && orderDetail.StartTime > time
                          && orderMain.DeleteFlag != "*" && orderDetail.DeleteFlag != "*" && orderDetail.OrderType == "0"
                         && orderMain.CancalDate == null
                          select orderMain).ToListAsync();
        }

        public async Task<List<OrderStatusView>> GetByOrderID(string inpatientID, List<string> OrderIDs)
        {
            return await (from m in _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.OrderStatus != "-1")
                          join n in _dbContext.PatientOrderDetails.Where(m => OrderIDs.Contains(m.OrderCode) && m.DeleteFlag != "*")
                          on m.PatientOrderMainID equals n.PatientOrderMainID

                          select new OrderStatusView
                          {
                              OrderCode = n.OrderCode,
                              StartDate = m.StartDate,
                              StartTime = m.StartTime,
                              EndDate = m.EndDate,
                              EndTime = m.EndTime,
                              AddEmployeeID = m.AddEmployeeID,
                              AddDate = m.AddDate,
                              StationID = m.StationID,
                              InpatientID = m.InpatientID,
                              OrderContent = n.OrderContent
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据病人inpatientID集合和关键字获取符合条件的未停止医嘱的患者ID
        /// </summary>
        /// <param name="inpatientIDs"></param>
        /// <param name="keys">keys为医嘱内容关键字或为医嘱码</param>
        /// <param name="todayFlag">今日标记</param>
        /// <returns></returns>
        public async Task<List<string>> GetInpatientIDByKeys(List<string> inpatientIDs, List<string> keys, bool todayFlag = false)
        {
            var orders = await (from a in _dbContext.PatientOrderMains
                                join b in _dbContext.PatientOrderDetails
                                on a.PatientOrderMainID equals b.PatientOrderMainID
                                where inpatientIDs.Contains(a.InpatientID)
                                && a.EndDate == null
                                && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                                && (!todayFlag || a.StartDate.Date == DateTime.Now.Date)
                                select new Dictionary<string, string>()
                                {
                                    { "InpatientID", a.InpatientID },
                                    { "OrderCode", b.OrderCode },
                                    { "OrderContent", b.OrderContent }
                                }
                          ).ToListAsync();
            // keys为医嘱内容关键字或为医嘱码
            return orders.Where(m => keys.Find(key => m["OrderContent"].IndexOf(key) != -1) != null || keys.Contains(m["OrderCode"]))
                    .Select(m => m["InpatientID"]).ToList();
        }

        public async Task<List<OrderView>> GetOrderView(string inpatientID, int stationID)
        {
            return await (from orderMain in _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.StationID == stationID && m.DeleteFlag != "*")
                          join orderDetail in _dbContext.PatientOrderDetails.Where(m => m.DeleteFlag != "*")
                          on orderMain.PatientOrderMainID equals orderDetail.PatientOrderMainID
                          where orderMain.OrderStatus != "4"
                          select new OrderView
                          {
                              OrderID = orderMain.OrderID,
                              OrderContent = orderDetail.OrderContent,
                              OrderDose = orderDetail.OrderDose,
                              Unit = orderDetail.Unit,
                              OrderCode = orderDetail.OrderCode,
                              Frequency = orderMain.HISFrequency,
                              OrderRule = orderDetail.OrderRule,
                              OrderDescription = orderDetail.OrderDescription,
                              OrderDetailID = orderDetail.OrderDetailID,
                              OrderSort = orderMain.HISOrderSort,
                              Doctor = orderMain.AddEmployeeID,
                              StartTime = orderMain.StartDate.Add(orderMain.StartTime)
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据病人inpatientID，包括取消的医嘱
        /// </summary>
        /// <param name="inpatientID">住院序号</param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainInfo>> GetAllAsync(string inpatientID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取特定方式有效医嘱数量
        /// </summary>
        /// <param name="rules"></param>
        /// <param name="inpatientID"></param>
        /// <param name="OrderType"></param>
        /// <returns></returns>
        public async Task<int> GetCountByOrderRule(List<string> rules, string inpatientID, string OrderType)
        {
            return await (from orderMain in _dbContext.PatientOrderMains
                    .Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*" && m.OrderType == OrderType)
                          join orderDetail in _dbContext.PatientOrderDetails
                          .Where(m => m.DeleteFlag != "*" && rules.Contains(m.OrderRule))
                          on orderMain.PatientOrderMainID equals orderDetail.PatientOrderMainID
                          where orderMain.OrderStatus == "2"
                          select orderMain).CountAsync();
        }

        /// <summary>
        /// 获取患者特定类型的未停止医嘱数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="stationID"></param>
        /// <param name="orderType"></param>
        /// <param name="rules"></param>
        /// <returns></returns>
        public async Task<List<OrderView>> GetOrderViewByType(string inpatientID, int stationID, string orderType, List<string> rules)
        {
            return await (from orderMain in _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.StationID == stationID
                           && m.OrderType == orderType && m.DeleteFlag != "*")
                          join orderDetail in _dbContext.PatientOrderDetails.Where(m => rules.Contains(m.OrderRule) && m.DeleteFlag != "*")
                          on orderMain.PatientOrderMainID equals orderDetail.PatientOrderMainID
                          where orderMain.OrderStatus == "2"
                          select new OrderView
                          {
                              OrderID = orderMain.OrderID,
                              OrderContent = orderDetail.OrderContent,
                              OrderDose = orderDetail.OrderDose,
                              Unit = orderDetail.Unit,
                              OrderCode = orderDetail.OrderCode,
                              Frequency = orderMain.HISFrequency,
                              OrderRule = orderDetail.OrderRule,
                              OrderDescription = orderDetail.OrderDescription,
                              OrderDetailID = orderDetail.OrderDetailID,
                              OrderSort = orderMain.HISOrderSort,
                              Doctor = orderMain.AddEmployeeID,
                              StartTime = orderMain.StartDate.Add(orderMain.StartTime),
                              HISOrderSort = orderMain.HISOrderSort
                          }).ToListAsync();
        }

        public async Task<List<PatientOrderMainInfo>> GetByOrderIDsAsync(List<string> orderIDs)
        {
            return await _dbContext.PatientOrderMains.Where(m => orderIDs.Contains(m.OrderID) && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
        public async Task<List<PatientOrderMainInfo>> GetByMainIDsAsync(List<string> patientOrderMainIDs)
        {
            return await _dbContext.PatientOrderMains.Where(m => patientOrderMainIDs.Contains(m.PatientOrderMainID) && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }

        public async Task<bool> CheckOrderExistOrNotByOrderIDAsync(string inpatientID, string orderID)
        {
            return await _dbContext.PatientOrderMains.AsNoTracking().Where(m => m.DeleteFlag != "*" && m.InpatientID == inpatientID && m.OrderID == orderID).CountAsync() > 0;
        }
        /// <summary>
        /// 获取医嘱主-明细数据对应关系
        /// </summary>
        /// <param name="orderIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientOrderMainAndDetailView>> GetPatientOrderMainAndDetailViews(List<string> orderIDs)
        {
            return await (from orderMain in _dbContext.PatientOrderMains.Where(m => orderIDs.Contains(m.OrderID) && m.DeleteFlag != "*")
                          join orderDetail in _dbContext.PatientOrderDetails.Where(m => m.DeleteFlag != "*")
                          on orderMain.PatientOrderMainID equals orderDetail.PatientOrderMainID
                          select new PatientOrderMainAndDetailView
                          {
                              CaseNumber = orderMain.CaseNumber,
                              OrderStatus = orderMain.OrderStatus,
                              PatientOrderMainID = orderMain.PatientOrderMainID,
                              PatientOrderDetailID = orderDetail.PatientOrderDetailID,
                              OrderID = orderMain.OrderID,
                              OrderType = orderMain.OrderType,
                              BillingAttribution = orderDetail.BillingAttribution,
                              OrderCode = orderDetail.OrderCode,
                              OrderPattern = orderDetail.OrderPattern,
                              OrderContent = orderDetail.OrderContent,
                              OrderDose = orderDetail.OrderDose,
                              Unit = orderDetail.Unit,
                              Frequency = orderDetail.Frequency,
                              OrderRule = orderDetail.OrderRule,
                              Location = orderDetail.Location,
                              SpecimenCategory = orderDetail.SpecimenCategory,
                              OrderNO = orderDetail.OrderNO,
                              HISOrderSort = orderDetail.HISOrderSort,
                              AddEemployeeID = orderMain.AddEmployeeID,
                              Package = orderDetail.Package,
                              PackageUnit = orderDetail.PackageUnit,
                              TotalVolume = orderDetail.TotalVolume,
                              OrderDescription = orderDetail.OrderDescription,
                              DrugAttention = orderDetail.DrugAttention,
                              OrderAlertFlag = orderDetail.OrderAlertFlag,
                              StartDate = orderDetail.StartDate,
                              StartTime = orderDetail.StartTime,
                          }).ToListAsync();
        }
        /// <summary>
        /// 获取抗菌药物触发相关的view数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="performDate"></param>
        /// <param name="labSheetSNs"></param>
        /// <returns>LabSheetSN,PatientOrderDetailID,ItemName</returns>
        /// </summary>
        public async Task<List<ExternalAddScheduleView>> GetViewByLabSheetSNAsync(string inpatientID, DateTime performDate, List<string> labSheetSNs)
        {
            return await (from orderMain in _dbContext.PatientOrderMains.AsNoTracking().Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                          join orderDetail in _dbContext.PatientOrderDetails.AsNoTracking().Where(m =>
                          labSheetSNs.Contains(m.LabSheetSN) && m.StartDate == performDate.Date && m.DeleteFlag != "*")
                          on orderMain.PatientOrderMainID equals orderDetail.PatientOrderMainID
                          select new ExternalAddScheduleView
                          {
                              LabSheetSN = orderDetail.LabSheetSN,
                              PatientOrderDetailID = orderDetail.PatientOrderDetailID,
                              ItemName = orderDetail.OrderContent
                          }
                           ).ToListAsync();
        }
        public async Task<List<PatientOrderMainInfo>> GetUnEndAsync(string inpatientID)
        {
            return await _dbContext.PatientOrderMains.Where(m => m.InpatientID == inpatientID && m.EndDate == null && m.CancalDate == null && m.DeleteFlag != "*").ToListAsync();
        }

    }
}
