﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class InpatientDataRepository : IInpatientDataRepository
    {
        private readonly MedicalDbContext _dbContext = null;
        private readonly IOptions<SystemConfig> _config;

        public InpatientDataRepository(MedicalDbContext db
            , IOptions<SystemConfig> config
            )
        {
            _dbContext = db;
            _config = config;
        }

        /// <summary>
        /// 通过caseNumber获取患者数据（包含出院）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetAsyncByCaseNumber(string caseNumber, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.HospitalID == hospitalID
            && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 通过caseNumber获取患者数据（包含出院和删除）
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientAsyncByCaseNumber(string caseNumber, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.HospitalID == hospitalID
            ).FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetByCaseNumberAsNoTracking(string caseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").AsNoTracking().Select(m=>new InpatientDataInfo
            {
                ID= m.ID,
                PatientID = m.PatientID,
                CaseNumber = m.CaseNumber,
                ChartNo=m.ChartNo,
                NumberOfAdmissions=m.NumberOfAdmissions,
                BedID= m.BedID,
                BedNumber= m.BedNumber,
                HospitalID= m.HospitalID,
                EMRArchivingFlag = m.EMRArchivingFlag,
            }).FirstOrDefaultAsync();
        }
        public async Task<InpatientDataInfo> GetInPatientData(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && m.ID == inpatientID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 通过chartNo获取患者数据（在院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientByChartNo(string chartNo, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过chartNo获取患者数据（包含出院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetListByChartNo(string chartNo)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*").OrderBy(m => m.AdmissionDate).ToListAsync();
        }
        /// <summary>
        /// 通过caseNumberList获取患者数据（在院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByCaseNumberListAsync(List<string> caseNumberList)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in caseNumberList)
            {
                var tempList = await _dbContext.InpatientDatas.Where(m => m.CaseNumber == item && m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }
        /// <summary>
        /// 透过CaseNumber获取在院病人信息
        /// </summary>
        /// <param name="caseNumber">住院序号</param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetByCaseNumberAsync(string caseNumber)
        {
            return await _dbContext.InpatientDatas.Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*"
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取当前在院病人（不包含删除）
        /// </summary>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientData()
        {
            return await _dbContext.InpatientDatas.Where(m => m.DeleteFlag != "*" && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).ToListAsync();
        }

        /// <summary>
        /// 通过caseNumberList获取患者数据（银川市一同步使用(在院出院)）
        /// 2023-03-23 (宏力出院同步使用)
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDataByCaseNumberListAsync(List<string> caseNumberList)
        {
            return await _dbContext.InpatientDatas.Where(m => caseNumberList.Contains(m.CaseNumber) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取病人基本信息
        /// </summary>
        /// <param name="caseNumbers">住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetInpatientDataByCaseNumbers(IEnumerable<string> caseNumbers)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { n.HospitalID, n.PatientID }
                        join b in _dbContext.BedListInfos on new { m.HospitalID, m.BedID } equals new { b.HospitalID, BedID = b.ID }
                        where caseNumbers.Contains(m.CaseNumber) && m.HospitalID == _config.Value.HospitalID && m.DeleteFlag != "*"
                        && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
                        orderby b.Sort
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            ChartNo = n.ChartNo,
                            CaseNumber = m.CaseNumber,
                            Age = m.Age,
                            DateOfBirth = n.DateOfBirth,
                            Gender = n.Gender,
                            NursingLevel = m.NursingLevel,
                            StationID = m.StationID,
                            DepartmentListID = m.DepartmentListID,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取病人基本信息（包括出院）
        /// </summary>
        /// <param name="caseNumbers">住院号集合</param>
        /// <returns></returns>
        public async Task<List<PatientData>> GetPatientDataByCaseNumbersContainsDischarge(IEnumerable<string> caseNumbers)
        {
            var query = from m in _dbContext.InpatientDatas
                        join n in _dbContext.PatientBasicDatas on new { m.HospitalID, m.PatientID } equals new { n.HospitalID, n.PatientID }
                        join b in _dbContext.BedListInfos on new { m.HospitalID, m.BedID } equals new { b.HospitalID, BedID = b.ID }
                        where caseNumbers.Contains(m.CaseNumber) && m.HospitalID == _config.Value.HospitalID && m.DeleteFlag != "*"
                        orderby b.Sort
                        select new PatientData
                        {
                            InpatientID = m.ID,
                            PatientID = n.PatientID,
                            ChartNo = n.ChartNo,
                            CaseNumber = m.CaseNumber,
                            Age = m.Age,
                            DateOfBirth = n.DateOfBirth,
                            Gender = n.Gender,
                            NursingLevel = m.NursingLevel,
                            StationID = m.StationID,
                            DepartmentListID = m.DepartmentListID,
                            BedID = m.BedID,
                            BedNumber = m.BedNumber,
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 通过caseNumberList获取患者数据（含出院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAllInpatientListByCaseNumberListAsync(List<string> caseNumberList)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in caseNumberList)
            {
                var tempList = await _dbContext.InpatientDatas.Where(m => m.CaseNumber == item && m.DeleteFlag != "*").ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }

        /// <summary>
        /// 通过bedID获取在院患者数据
        /// </summary>
        /// <param name="bedID">床位序号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns>病人住院记录</returns>
        public async Task<InpatientDataInfo> GetByInpatientBedIDAsync(int bedID, string hospitalID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.HospitalID == hospitalID && m.BedID == bedID
            && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取入院次数
        /// </summary>
        /// <param name="inpatientID">住院唯一ID</param>
        /// <returns></returns>
        public async Task<int> GetNumberOfAdmissionsByInpatientID(string inpatientID)
        {
            return await _dbContext.InpatientDatas.Where(m => m.ID == inpatientID && m.DeleteFlag != "*").Select(m => m.NumberOfAdmissions).FirstOrDefaultAsync() ;
        }
    }
}