﻿using InterconnectCore.ViewModels;

namespace InterconnectCore.Service.Interface
{
    public interface ISyncBloodInspectService
    {
        /// <summary>
        /// 同步输血监测数据到护理记录明细表
        /// </summary>
        /// <param name="inputViews"></param>
        /// <returns>(successFlag:成功标记，errorMessage=执行错误信息 )</returns>
        Task<(bool successFlag, string errorMessage)> SyncBloodInspectDataToNursingRecordDetail(List<BloodInspectInputView> inputViews);
    }
}
