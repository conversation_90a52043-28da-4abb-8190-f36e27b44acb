﻿using InterconnectCore.ViewModels;
using Medical.Models;

namespace InterconnectCore.Service.Interface
{
    public interface IPatientDiagnosisService
    {
        /// <summary>
        /// 同步住院病人诊断
        /// </summary>
        /// <param name="patientDiagnosis"></param>
        /// <returns></returns>
        Task<bool> SyncPatientDiagnosisAsync(List<PatientDiagnosisItemView> patientDiagnosis);
    }
}
