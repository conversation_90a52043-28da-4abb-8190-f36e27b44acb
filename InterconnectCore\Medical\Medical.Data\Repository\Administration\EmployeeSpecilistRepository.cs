﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class EmployeeSpecilistRepository : IEmployeeSpecilistRepository
    {
        private readonly MedicalDbContext _medicalDbContext;

        public EmployeeSpecilistRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        public async Task<EmployeeSpecilistInfo> GetByEmployeeBasicIDAsync(int employeeBasicID)
        {
            return await _medicalDbContext.EmployeeSpecilistInfos.Where(
    m => m.DeleteFlag != "*" && m.EmployeeBasicID == employeeBasicID).SingleOrDefaultAsync();
        }

        public async Task<EmployeeSpecilistInfo> GetByEmployeeDataIDAndSpecilistRequiredAsync(int employeeDataID, string specilist)
        {
            return await _medicalDbContext.EmployeeSpecilistInfos.Where(m => m.DeleteFlag != "*"
            && m.EmployeeBasicID == employeeDataID
            && m.SpecilistRequired == specilist).SingleOrDefaultAsync();
        }

        public async Task<List<EmployeeSpecilistInfo>> GetList()
        {
            return await _medicalDbContext.EmployeeSpecilistInfos.Where(
                m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<EmployeeSpecilistInfo> GetList(int ID)
        {
            return await _medicalDbContext.EmployeeSpecilistInfos.Where(
                m => m.DeleteFlag != "*" && m.EmployeeSpecilistID == ID).SingleOrDefaultAsync();
        }

        public async Task<List<EmployeeSpecilistInfo>> GetListByEmployeeDataIDAsync(int employeeDataID)
        {
            return await _medicalDbContext.EmployeeSpecilistInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeBasicID == employeeDataID).ToListAsync();
        }
    }
}