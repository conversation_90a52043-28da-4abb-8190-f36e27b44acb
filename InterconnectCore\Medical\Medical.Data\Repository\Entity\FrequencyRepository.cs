﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class FrequencyRepository : IFrequencyRepository
    {
        private MedicalDbContext _medicalDbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;
        public FrequencyRepository(MedicalDbContext db, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<FrequencyInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.Frequencies.Where(m => m.HospitalID == hospitalID.ToString() && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.Frequency.GetKey(_sessionCommonServer);
        }

        public async Task<List<FrequencyInfo>> GetByIDAsync(int id)
        {
            var data = await GetCacheAsync() as List<FrequencyInfo>;

            if (data != null)
            {
                return data.Where(t => t.ID == id).ToList();
            }
            return new List<FrequencyInfo>();
        }
        public async Task<List<FrequencyInfo>> GetOneFrequency(string frequency, int stationID)
        {
            var data = await GetCacheAsync() as List<FrequencyInfo>;
            if (data != null)
            {
                return data.Where(m => m.Frequency.Trim() == frequency && m.StationID == stationID).ToList();
            }
            return new List<FrequencyInfo>();
        }
        //获取最大的ID
        public int GetMaxFrequencyID()
        {
            return _medicalDbContext.Frequencies.Max(m => m.ID);
        }

        /// <summary>
        /// 获取一条频次数据，包含删除的，电子病历生产使用
        /// 因为病历被生产后可能频次调整，重新异动病历，需要获取历史频次
        /// </summary>
        /// <param name="frequency"></param>
        /// <param name="language"></param>
        /// <param name="hospitalId"></param>
        /// <param name="stationID"></param>
        /// <returns></returns>
        public async Task<FrequencyView> GetFrequencyView(string frequency, int stationID)
        {
            //先从缓存获取
            var frequencyList = await GetCacheAsync() as List<FrequencyInfo>;
            var frequencyListTemp = frequencyList.Where(m => m.Frequency == frequency && m.StationID == stationID).FirstOrDefault();
            if (frequencyListTemp != null)
            {
                return CreatreFrequencyView(frequencyListTemp);
            }
            frequencyListTemp = frequencyList.Where(m => m.Frequency == frequency && m.StationID == 0).FirstOrDefault();
            if (frequencyListTemp != null)
            {
                return CreatreFrequencyView(frequencyListTemp);
            }

            //没有获取现有频次，从删除的频次里面获取
            var frequencyDeleteList = await GetFrequencyViewByDelete(frequency, stationID);
            //只有一个
            if (frequencyDeleteList.Count == 1)
            {
                return frequencyDeleteList[0];
            }

            //有多个，则根据病区获取
            var frequencyDeleteListTemp = frequencyDeleteList.Where(m => m.StationID == m.StationID).ToList();
            if (frequencyDeleteList.Count == 1)
            {
                return frequencyDeleteListTemp[0];
            }

            frequencyDeleteListTemp = frequencyDeleteList.Where(m => m.StationID == 0).ToList();
            if (frequencyDeleteList.Count == 1)
            {
                return frequencyDeleteListTemp[0];
            }
            //如果走到这里可能就是重复数据了，直接获取一个
            if (frequencyDeleteList.Count >= 1)
            {
                return frequencyDeleteList[0];
            }
            //什么都没有获得，返回null
            return null;
        }

        /// <summary>
        /// 创建返回结果集
        /// </summary>
        /// <param name="frequencyInfo"></param>
        /// <returns></returns>
        private FrequencyView CreatreFrequencyView(FrequencyInfo frequencyInfo)
        {
            return new FrequencyView
            {
                ID = frequencyInfo.ID,
                Frequency = frequencyInfo.Frequency,
                FrequencyDescription = frequencyInfo.FrequencyDescription
            };
        }

        //获得已经删除的频次
        private async Task<List<FrequencyView>> GetFrequencyViewByDelete(string frequency, int stationID)
        {
            var session = await _sessionCommonServer.GetSession();
            var result = from m in _medicalDbContext.Frequencies
                         where m.Frequency == frequency
                         && m.HospitalID == session.HospitalID && m.Language == session.Language
                        && (m.StationID == 0 || m.StationID == stationID)
                        && m.DeleteFlag == "*"
                         select new FrequencyView
                         {
                             ID = m.ID,
                             Frequency = m.Frequency,
                             DeleteFlag = m.DeleteFlag,
                             StationID = m.StationID,
                             FrequencyDescription = m.FrequencyDescription

                         };
            return await result.ToListAsync();
        }

        public async Task<List<FrequencyInfo>> GetFrequencyByStation(int stationID)
        {
            var data = await GetCacheAsync() as List<FrequencyInfo>;
            data = data.Where(m => m.StationID == 0 || m.StationID == stationID).OrderBy(m => m.Sort).ToList();
            return data;
        }
        /// <summary>
        /// 根据频次ID组合获取数据
        /// </summary>
        /// <param name="frequencyIDs"></param>
        /// <param name="language"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetDataByIDsAsync(List<int> frequencyIDs)
        {
            var returnList = new List<Dictionary<string, string>>();
            var frequencyList = await GetCacheAsync() as List<FrequencyInfo>;
            var list = frequencyList.Where(m => frequencyIDs.Contains(m.ID)).ToList();
            if (list.Count > 0)
            {
                foreach (var item in list)
                {
                    returnList.Add(new Dictionary<string, string>
                         {
                             {"value",item.ID.ToString() },
                             {"label",item.Frequency }
                         });
                }
            }
            return returnList;
        }
        /// <summary>
        /// 根据主键获取一条数据
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="language"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<FrequencyInfo> GetDataTypeByIDAsync(int ID)
        {
            var data = await GetCacheAsync() as List<FrequencyInfo>;
            return data.Where(m => m.ID == ID).DistinctBy(m => m.LocalCategory).FirstOrDefault();
        }

        public async Task<FrequencyInfo> GetAsync(int frequencyID, int stationID)
        {
            var data = await GetCacheAsync() as List<FrequencyInfo>;
            return data.Find(m => m.ID == frequencyID && (m.StationID == 0 || m.StationID == stationID));
        }
    }
}