﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.Models.Setting;
using MedicalExternalCommon.Service;
using Microsoft.EntityFrameworkCore;
using NLog;
using StackExchange.Redis;

namespace InterconnectCore.Service
{
    public class PatientMedicineSchedule : IPatientMedicineSchedule
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDrugListRepository _drugListRepository;
        private readonly IMedicationRouteRepository _medicationRouteRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientMedicineScheduleRepository _patientMedicineScheduleRepository;
        private readonly StationaShiftCommonService _stationaShiftCommonService;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        private readonly ConnectionMultiplexer _redisConnection;
        private readonly MedicalDbContext _medicalDbContext;

        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="drugListRepository"></param>
        /// <param name="medicationRouteRepository"></param>
        /// <param name="stationListRepository"></param>
        /// <param name="inpatientDataRepository"></param>
        /// <param name="patientMedicineScheduleRepository"></param>
        /// <param name="stationaShiftCommonService"></param>
        public PatientMedicineSchedule(IUnitOfWork<MedicalDbContext> unitOfWork
            , IDrugListRepository drugListRepository
            , IMedicationRouteRepository medicationRouteRepository
            , IStationListRepository stationListRepository
            , IInpatientDataRepository inpatientDataRepository
            , IPatientMedicineScheduleRepository patientMedicineScheduleRepository
            , StationaShiftCommonService stationaShiftCommonService
            , ISettingDescriptionRepository settingDescriptionRepository
            , ConnectionMultiplexer redisConnection
            , MedicalDbContext medicalDbContext)
        {
            _unitOfWork = unitOfWork;
            _drugListRepository = drugListRepository;
            _medicationRouteRepository = medicationRouteRepository;
            _stationListRepository = stationListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientMedicineScheduleRepository = patientMedicineScheduleRepository;
            _stationaShiftCommonService = stationaShiftCommonService;
            _settingDescriptionRepository = settingDescriptionRepository;
            _redisConnection = redisConnection;
            _medicalDbContext = medicalDbContext;
        }

        /// <summary>
        /// 同步单患者用药信息
        /// </summary>
        /// <param name="patientMedicineScheduleViews"></param>
        /// <returns></returns>
        public async Task<bool> SyncSinglePatientMedicineSchedule(List<PatientMedicineScheduleView> patientMedicineScheduleViews)
        {
            if (patientMedicineScheduleViews == null || patientMedicineScheduleViews.Count == 0)
            {
                _logger.Info($"患者用药信息为空patientMedicineScheduleViews");
                return false;
            }
            _logger.Info($"开始同步患者{patientMedicineScheduleViews[0].CaseNumber}的用药信息{ListToJson.ToJson(patientMedicineScheduleViews)}");
            var caseNumber = patientMedicineScheduleViews[0].CaseNumber;
            IDatabase _database = _redisConnection.GetDatabase();
            var lockKey = $"SyncPatientMedicineSchedule_{caseNumber}";

            if (!await TryAcquireLockAsync(_database, lockKey))
            {
                _logger.Error($"同步给药时患者{caseNumber}正在同步中");
                return false;
            }
            try
            {
                var drugList = await _drugListRepository.GetAllAsync<DrugListInfo>();
                var medicineRoute = await _medicationRouteRepository.GetAllAsync<MedicationRouteInfo>();
                var stationList = await _stationListRepository.GetAllAsync();
                var inpatientData = await _inpatientDataRepository.GetByCaseNumberAsNoTracking(patientMedicineScheduleViews[0].CaseNumber);
                if (inpatientData == null)
                {
                    _logger.Error($"同步给药时患者{patientMedicineScheduleViews[0].CaseNumber}不存在");
                    return false;
                }
                var updateScanFlagByDrugSpec = await _settingDescriptionRepository.GetSettingSwitchByTypeCode("UpdateMedicineScanFlagByDrugSpec");
                var oldPatientMedicines = await _patientMedicineScheduleRepository.GetByGroupAsync(patientMedicineScheduleViews.Select(m => m.GroupID).ToArray());
                foreach (var patientMedicineScheduleView in patientMedicineScheduleViews)
                {
                    var newPatientMedicine = await CreatePatientMedicineScheduleInfoAsync(patientMedicineScheduleView, inpatientData, drugList, medicineRoute, stationList, updateScanFlagByDrugSpec);
                    if (newPatientMedicine == null)
                    {
                        continue;
                    }
                    // GroupID + OrderContent 定义唯一数据
                    var oldPatientMedicine = oldPatientMedicines.Where(m => m.GroupID == patientMedicineScheduleView.GroupID
                    && m.OrderContent == patientMedicineScheduleView.OrderContent
                    && m.ScheduleDate == patientMedicineScheduleView.ScheduleDate
                    && m.ScheduleTime == patientMedicineScheduleView.ScheduleTime).FirstOrDefault();
                    if (oldPatientMedicine == null)
                    {
                        //新增
                        await _unitOfWork.GetRepository<PatientMedicineScheduleInfo>().InsertAsync(newPatientMedicine);
                        continue;
                    }
                    //已执行不同步
                    if ((oldPatientMedicine.PerformDate.HasValue && oldPatientMedicine.PerformTime.HasValue && !string.IsNullOrWhiteSpace(oldPatientMedicine.PerformEmployeeID)))
                    {
                        continue;
                    }
                    //修改
                    UpdatePatientMedicineScheduleInfo(oldPatientMedicine, newPatientMedicine, patientMedicineScheduleView.SourceType);
                }
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            finally
            {
                await ReleaseLockAsync(_database, lockKey);
            }
        }

        /// <summary>
        /// 释放锁
        /// </summary>
        /// <param name="_database"></param>
        /// <param name="lockKey"></param>
        /// <returns></returns>
        private static Task<bool> ReleaseLockAsync(IDatabase _database, string lockKey)
        {
            return _database.KeyDeleteAsync(lockKey);
        }

        /// <summary>
        /// 尝试获取锁
        /// </summary>
        /// <param name="_database"></param>
        /// <param name="lockKey"></param>
        /// <returns></returns>
        private static Task<bool> TryAcquireLockAsync(IDatabase _database, string lockKey)
        {
            var isLockAcquired = _database.StringSetAsync(lockKey, 1, new TimeSpan(0, 0, 5), When.NotExists);
            return isLockAcquired;
        }

        /// <summary>
        /// 创建患者用药信息
        /// </summary>
        /// <param name="patientMedicineScheduleView"></param>
        /// <param name="inpatientData"></param>
        /// <param name="drugList"></param>
        /// <param name="medicineRoute"></param>
        /// <param name="stationList"></param>
        /// <returns></returns>
        private async Task<PatientMedicineScheduleInfo> CreatePatientMedicineScheduleInfoAsync(PatientMedicineScheduleView patientMedicineScheduleView
            , InpatientDataInfo inpatientData, List<DrugListInfo> drugList, List<MedicationRouteInfo> medicineRoute, List<StationListInfo> stationList, bool updateScanFlagByDrugSpec)
        {
            var newPatientMedicine = new PatientMedicineScheduleInfo();
            var drugInfo = drugList.Find(m => m.DrugCode == patientMedicineScheduleView.DrugCode);
            newPatientMedicine.PatientMedicineScheduleID = newPatientMedicine.GetId();
            //患者信息,病区以HIS为准
            newPatientMedicine.CaseNumber = inpatientData.CaseNumber;
            newPatientMedicine.ChartNo = inpatientData.ChartNo;
            newPatientMedicine.InpatientID = inpatientData.ID;
            newPatientMedicine.PatientID = inpatientData.PatientID;
            newPatientMedicine.HospitalID = inpatientData.HospitalID;
            newPatientMedicine.NumberOfAdmissions = inpatientData.NumberOfAdmissions;
            newPatientMedicine.BedNumber = inpatientData.BedNumber;
            newPatientMedicine.BedID = inpatientData.BedID;
            newPatientMedicine.StationCode = patientMedicineScheduleView.StationCode;
            var stationInfo = stationList.Find(m => m.StationCode == patientMedicineScheduleView.StationCode);
            if (stationInfo == null)
            {
                return null;
            }
            newPatientMedicine.StationID = stationInfo.ID;
            //药品信息
            newPatientMedicine.PatientOrderMainID = patientMedicineScheduleView.PatientOrderMainID;
            newPatientMedicine.PatientOrderDetailID = patientMedicineScheduleView.PatientOrderDetailID;
            newPatientMedicine.GroupID = patientMedicineScheduleView.GroupID;
            newPatientMedicine.OrderCode = patientMedicineScheduleView.OrderCode;
            newPatientMedicine.HISOrderGroupNo = patientMedicineScheduleView.HISOrderGroupNo;
            newPatientMedicine.HISOrderSort = patientMedicineScheduleView.HISOrderSort;
            newPatientMedicine.HighRiskFlag = patientMedicineScheduleView.HighRiskFlag;
            if (drugInfo != null)
            {
                newPatientMedicine.DrugType = drugInfo.DrugType;
                newPatientMedicine.HighRiskFlag = string.IsNullOrWhiteSpace(drugInfo.HighRiskFlag) ? "" : "1";
            }
            newPatientMedicine.OrderType = patientMedicineScheduleView.OrderType;
            //三查七对一注意
            newPatientMedicine.ScheduleDate = patientMedicineScheduleView.ScheduleDate;
            newPatientMedicine.ScheduleTime = patientMedicineScheduleView.ScheduleTime;
            var shiftInfo = await _stationaShiftCommonService.GetShiftAsync(stationInfo.ID, patientMedicineScheduleView.ScheduleTime);
            if (shiftInfo != null)
            {
                var shiftDate = _stationaShiftCommonService.GetShiftDate(shiftInfo, patientMedicineScheduleView.ScheduleDate.Add(patientMedicineScheduleView.ScheduleTime));
                newPatientMedicine.StationShiftID = shiftInfo.ID;
                newPatientMedicine.ShiftDate = shiftDate;
            }
            newPatientMedicine.OrderContent = patientMedicineScheduleView.OrderContent;
            newPatientMedicine.OrderDescription = patientMedicineScheduleView.OrderDescription;
            newPatientMedicine.AmountText = patientMedicineScheduleView.AmountText;
            newPatientMedicine.DrugSpec = patientMedicineScheduleView.DrugSpec;
            newPatientMedicine.OrderDose = patientMedicineScheduleView.OrderDose;
            newPatientMedicine.Unit = patientMedicineScheduleView.Unit;
            newPatientMedicine.TotalVolume = patientMedicineScheduleView.TotalVolume;
            newPatientMedicine.ConvertedVolume = GetConvertedVolumn(patientMedicineScheduleView, drugList);
            newPatientMedicine.Frequency = patientMedicineScheduleView.Frequency;
            newPatientMedicine.Location = patientMedicineScheduleView.Location;
            newPatientMedicine.OrderRule = patientMedicineScheduleView.OrderRule;
            newPatientMedicine.Speed = patientMedicineScheduleView.Speed;
            newPatientMedicine.DrugAttention = patientMedicineScheduleView.DrugAttention;
            newPatientMedicine.BillingAttribution = patientMedicineScheduleView.BillingAttribution;
            //药品时间状态
            newPatientMedicine.OrderStatus = patientMedicineScheduleView.OrderStatus;
            //闭环状态,对接数据入库时,初始状态为医嘱拆分
            newPatientMedicine.Status = (int)OrderTasksType.SplitOrder;
            //开立
            newPatientMedicine.StartDateTime = patientMedicineScheduleView.StartDateTime;
            //停止
            if (patientMedicineScheduleView.StopDate.HasValue
                && patientMedicineScheduleView.StopDate.Value == DateTime.MinValue)
            {
                newPatientMedicine.StopDate = null;
                newPatientMedicine.StopTime = null;
            }
            else
            {
                newPatientMedicine.StopDate = patientMedicineScheduleView.StopDate;
                newPatientMedicine.StopTime = patientMedicineScheduleView.StopTime;
            }
            newPatientMedicine.StopEmployeeID = patientMedicineScheduleView.StopEmployeeID;
            //todo 临时医嘱停止日期时间和开立日期时间相同,长期医嘱有停止日期时间
            //取消
            newPatientMedicine.CancelDate = patientMedicineScheduleView.CancelDate;
            newPatientMedicine.CancelTime = patientMedicineScheduleView.CancelTime;
            newPatientMedicine.CancelEmployeeID = patientMedicineScheduleView.CancelEmployeeID;
            //已取消
            if (patientMedicineScheduleView.CancelDate.HasValue && patientMedicineScheduleView.CancelTime.HasValue && !string.IsNullOrWhiteSpace(patientMedicineScheduleView.CancelEmployeeID))
            {
                newPatientMedicine.Status = (int)OrderTasksType.Cancel;
            }
            //删除
            newPatientMedicine.DeleteDate = patientMedicineScheduleView.DeleteDate;
            newPatientMedicine.DeleteTime = patientMedicineScheduleView.DeleteTime;
            newPatientMedicine.DeleteFlag = patientMedicineScheduleView.DeleteFlag;
            //新增修改
            newPatientMedicine.AddDate = patientMedicineScheduleView.AddDate;
            newPatientMedicine.AddEmployeeID = patientMedicineScheduleView.AddEmployeeID;//开嘱医生
            //如果来源是口服药包药机，则更新标记
            if (patientMedicineScheduleView.SourceType.HasValue && patientMedicineScheduleView.SourceType == 2)
            {
                newPatientMedicine.PrintFlag = "*";
                newPatientMedicine.Status = (int)OrderTasksType.WardDispense;
            }
            newPatientMedicine.Modify(MODIFYPERSONID);

            SetPDAExecuteFlag(newPatientMedicine, medicineRoute, drugInfo, updateScanFlagByDrugSpec);
            return newPatientMedicine;
        }

        /// <summary>
        /// 更新PDA端执行标记
        /// </summary>
        /// <param name="patientMedicineSchedule"></param>
        /// <param name="medicationRouteInfos"></param>
        /// <param name="drugInfo"></param>
        /// <returns></returns>
        private static PatientMedicineScheduleInfo SetPDAExecuteFlag(PatientMedicineScheduleInfo patientMedicineSchedule, List<MedicationRouteInfo> medicationRouteInfos, DrugListInfo drugInfo, bool updateScanFlagByDrugSpec)
        {
            // 需PDA执行
            patientMedicineSchedule.PDAFlag = true;
            var tmpMedicineRoute = medicationRouteInfos.Find(m => m.RouteType.Trim() == patientMedicineSchedule.OrderRule);
            if (tmpMedicineRoute != null)
            {
                // 药品类型
                patientMedicineSchedule.MedicineType = tmpMedicineRoute.GroupNo;
                // 需PDA扫描
                patientMedicineSchedule.ScanFlag = tmpMedicineRoute.BarcodeReader ? "1" : "0";
            }
            else
            {
                patientMedicineSchedule.MedicineType = "7";
                patientMedicineSchedule.ScanFlag = "0";
            }
            // 判断配置，是否根据药品规格再次比对
            var sameSpecFlag = !updateScanFlagByDrugSpec || drugInfo.DrugSpec == patientMedicineSchedule.DrugSpec;
            //判断DrugList里面是否有配置扫描标记
            if (drugInfo != null && sameSpecFlag && !string.IsNullOrEmpty(drugInfo.ScanFlag))
            {
                patientMedicineSchedule.ScanFlag = drugInfo.ScanFlag;
            }
            return patientMedicineSchedule;
        }

        /// <summary>
        /// 更新患者用药信息
        /// </summary>
        /// <param name="oldPatientMedicine"></param>
        /// <param name="newPatientMedicine"></param>
        /// <param name="sourceType">数据来源，1、给药拆分（默认）、2、口服药包药机</param>
        private void UpdatePatientMedicineScheduleInfo(PatientMedicineScheduleInfo oldPatientMedicine, PatientMedicineScheduleInfo newPatientMedicine, int? sourceType)
        {
            _logger.Info($"开始同步患者{oldPatientMedicine.CaseNumber}的用药信息{ListToJson.ToJson(oldPatientMedicine)}");
            //HIS停止
            if (newPatientMedicine.OrderStatus == 3)
            {
                oldPatientMedicine.OrderStatus = newPatientMedicine.OrderStatus;
                if (newPatientMedicine.StopDate.HasValue && newPatientMedicine.StopDate.Value != DateTime.MinValue)
                {
                    oldPatientMedicine.StopDate = newPatientMedicine.StopDate;
                    oldPatientMedicine.StopTime = newPatientMedicine.StopTime;
                }
                if (!newPatientMedicine.StopDate.HasValue)
                {
                    oldPatientMedicine.StopDate = null;
                    oldPatientMedicine.StopTime = null;
                }
                oldPatientMedicine.StopEmployeeID = newPatientMedicine.StopEmployeeID.Trim();
                oldPatientMedicine.Modify(MODIFYPERSONID);
                return;
            }
            //更新标签
            if (oldPatientMedicine.OrderDose.HasValue != newPatientMedicine.OrderDose.HasValue
                || (oldPatientMedicine.OrderDose.HasValue && newPatientMedicine.OrderDose.HasValue
                && decimal.Round(oldPatientMedicine.OrderDose.Value, 10) != decimal.Round(newPatientMedicine.OrderDose.Value, 10)))
            {
                _logger.Warn($"【更新给药】OrderDose变更，{oldPatientMedicine.OrderDose}=>{newPatientMedicine.OrderDose}，PatientMedicineScheduleID={oldPatientMedicine.PatientMedicineScheduleID}");
                oldPatientMedicine.OrderDose = newPatientMedicine.OrderDose;
            }
            if (oldPatientMedicine.TotalVolume.HasValue != newPatientMedicine.TotalVolume.HasValue
                || (oldPatientMedicine.TotalVolume.HasValue && newPatientMedicine.TotalVolume.HasValue
                    && decimal.Round(oldPatientMedicine.TotalVolume.Value, 10) != decimal.Round(newPatientMedicine.TotalVolume.Value, 10)))
            {
                _logger.Warn($"【更新给药】TotalVolume变更，{oldPatientMedicine.TotalVolume}=>{newPatientMedicine.TotalVolume}，PatientMedicineScheduleID={oldPatientMedicine.PatientMedicineScheduleID}");
                oldPatientMedicine.TotalVolume = newPatientMedicine.TotalVolume;
            }

            oldPatientMedicine.OrderType = newPatientMedicine.OrderType;
            oldPatientMedicine.OrderContent = newPatientMedicine.OrderContent;
            oldPatientMedicine.OrderDescription = newPatientMedicine.OrderDescription;
            oldPatientMedicine.AmountText = newPatientMedicine.AmountText;
            oldPatientMedicine.DrugSpec = newPatientMedicine.DrugSpec;
            oldPatientMedicine.Unit = newPatientMedicine.Unit;
            oldPatientMedicine.Frequency = newPatientMedicine.Frequency;
            oldPatientMedicine.Location = newPatientMedicine.Location;
            oldPatientMedicine.OrderRule = newPatientMedicine.OrderRule;
            oldPatientMedicine.MedicineType = newPatientMedicine.MedicineType;
            oldPatientMedicine.BillingAttribution = newPatientMedicine.BillingAttribution;
            if ((oldPatientMedicine.DrugAttention ?? "") != (newPatientMedicine.DrugAttention ?? ""))
            {
                oldPatientMedicine.DrugAttention = newPatientMedicine.DrugAttention ?? "";
            }
            //重置标签打印状态(不可以变更顺序，以上变更需要重置标签打印状态)
            if (_medicalDbContext.Entry(oldPatientMedicine).State != EntityState.Unchanged)
            {
                PrintChangedPropertiesLog(oldPatientMedicine);
                oldPatientMedicine.Status = (int)OrderTasksType.SplitOrder;
                oldPatientMedicine.PrintFlag = "";
            }
            if ((oldPatientMedicine.HighRiskFlag ?? "") != (newPatientMedicine.HighRiskFlag ?? ""))
            {
                oldPatientMedicine.HighRiskFlag = newPatientMedicine.HighRiskFlag ?? "";
            }
            if ((oldPatientMedicine.DrugType ?? "") != (newPatientMedicine.DrugType ?? ""))
            {
                oldPatientMedicine.DrugType = newPatientMedicine.DrugType ?? "";
            }
            if (oldPatientMedicine.ConvertedVolume.HasValue != newPatientMedicine.ConvertedVolume.HasValue
                || (oldPatientMedicine.ConvertedVolume.HasValue && newPatientMedicine.ConvertedVolume.HasValue
                && decimal.Round(oldPatientMedicine.ConvertedVolume.Value, 2) != decimal.Round(newPatientMedicine.ConvertedVolume.Value, 2)))
            {
                _logger.Warn($"【更新给药】ConvertedVolume变更，{oldPatientMedicine.ConvertedVolume}=>{newPatientMedicine.ConvertedVolume}，PatientMedicineScheduleID={oldPatientMedicine.PatientMedicineScheduleID}");
                oldPatientMedicine.ConvertedVolume = newPatientMedicine.ConvertedVolume;
            }
            oldPatientMedicine.ScanFlag = newPatientMedicine.ScanFlag ?? "";
            oldPatientMedicine.PDAFlag = newPatientMedicine.PDAFlag;
            oldPatientMedicine.StopDate = newPatientMedicine.StopDate;
            oldPatientMedicine.StopTime = newPatientMedicine.StopTime;
            if ((oldPatientMedicine.StopEmployeeID ?? "") != (newPatientMedicine.StopEmployeeID ?? ""))
            {
                oldPatientMedicine.StopEmployeeID = newPatientMedicine.StopEmployeeID ?? "";
            }
            oldPatientMedicine.OrderStatus = newPatientMedicine.OrderStatus;
            //如果来源是口服药包药机，则更新标记
            if (sourceType.HasValue && sourceType == 2)
            {
                oldPatientMedicine.PrintFlag = "*";
                oldPatientMedicine.Status = (int)OrderTasksType.WardDispense;
            }
            if (newPatientMedicine.StopDate.HasValue && newPatientMedicine.StopDate.Value == DateTime.MinValue)
            {
                oldPatientMedicine.StopDate = null;
                oldPatientMedicine.StopTime = null;
            }
            if (_medicalDbContext.Entry(oldPatientMedicine).State != EntityState.Unchanged)
            {
                oldPatientMedicine.Modify(MODIFYPERSONID);
            }
        }

        /// <summary>
        /// 打印变更日志
        /// </summary>
        /// <param name="oldPatientMedicine"></param>
        private void PrintChangedPropertiesLog(PatientMedicineScheduleInfo oldPatientMedicine)
        {
            var entry = _medicalDbContext.Entry(oldPatientMedicine);
            var changedProperties = entry.Properties
                .Where(p => p.IsModified)
                .Select(p => new { PropertyName = p.Metadata.Name, p.OriginalValue, CurrentValue = p.CurrentValue })
                .ToList();

            if (changedProperties.Any())
            {
                _logger.Warn($"【更新给药】标签相关内容变更，PatientMedicineScheduleID={oldPatientMedicine.PatientMedicineScheduleID}");

                foreach (var property in changedProperties)
                {
                    _logger.Warn($"{oldPatientMedicine.PatientMedicineScheduleID}字段 {property.PropertyName} 发生变更，原始值：{property.OriginalValue}，当前值：{property.CurrentValue}");
                }
            }
        }

        /// <summary>
        /// 获取转换后写入出入量的剂量
        /// </summary>
        /// <param name="patientMedicineScheduleView"></param>
        /// <param name="drugList"></param>
        /// <returns></returns>
        private static decimal? GetConvertedVolumn(PatientMedicineScheduleView patientMedicineScheduleView, List<DrugListInfo> drugList)
        {
            DrugListInfo drug = null;
            if (!string.IsNullOrEmpty(patientMedicineScheduleView.DrugSpec))
            {
                drug = drugList.Find(m => m.DrugCode == patientMedicineScheduleView.DrugCode && m.DrugSpec == patientMedicineScheduleView.DrugSpec && m.DoseUnits == patientMedicineScheduleView.Unit);
            }
            else
            {
                var tmp = drugList.Where(m => m.DrugCode == patientMedicineScheduleView.DrugCode && m.DoseUnits == patientMedicineScheduleView.Unit).ToList();
                if (tmp.Count > 0 && tmp.Select(m => m.DrugSpec).Distinct().ToList().Count == 1)
                {
                    drug = tmp[0];
                }
            }
            if (drug == null || !drug.ConvertedVolume.HasValue || drug.ConvertedVolume.Value == 0)
            {
                //如果液体单位为ml，全部使用总剂量，在医嘱标签执行处过滤是否带入出入量
                if (patientMedicineScheduleView.Unit == "ml")
                {
                    return patientMedicineScheduleView.TotalVolume;
                }
                return null;
            }
            if (!patientMedicineScheduleView.Package.HasValue)
            {
                return null;
            }
            var volume = drug.ConvertedVolume.Value * patientMedicineScheduleView.Package.Value;
            return volume;
        }
    }
}