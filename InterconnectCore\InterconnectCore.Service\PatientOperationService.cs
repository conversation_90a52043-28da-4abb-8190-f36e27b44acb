﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.ViewModels;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels.Data;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;

namespace InterconnectCore.Service
{
    public class PatientOperationService : IPatientOperationService
    {
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IPatientOperationRepository _patientOperationRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly CommonHelper _commonHelper;
        private readonly PatientEventCommonService _patientEventCommonService;
        private readonly ExternalCommonService _externalCommonService;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 术前患者事件settingID
        /// </summary>
        private const int PREOP_EVENTSETTINGID = 1860;

        public PatientOperationService(
            IUnitOfWork<MedicalDbContext> unitOfWork,
            IPatientOperationRepository patientOperationRepository,
            IInpatientDataRepository inpatientDataRepository,
            IPatientEventRepository patientEventRepository,
            IAppConfigSettingRepository appConfigSettingRepository,
            CommonHelper commonHelper,
            PatientEventCommonService patientEventCommonService,
            ExternalCommonService externalCommonService,
            IOptions<SystemConfig> config
            )
        {
            _unitOfWork = unitOfWork;
            _patientOperationRepository = patientOperationRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _patientEventRepository = patientEventRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _commonHelper = commonHelper;
            _patientEventCommonService = patientEventCommonService;
            _externalCommonService = externalCommonService;
            _config = config;
        }
        public async Task<bool> SyncPatientOperation(OperationParamView operationViewList)
        {
            if (operationViewList.InsertOperations.Count > 0) await InsertPatientOperation(operationViewList.InsertOperations);
            if (operationViewList.UpdateOperations.Count > 0) await UpdatePatientOperation(operationViewList.UpdateOperations);
            if (operationViewList.DeleteOperations.Count > 0) await DeletePatientOperation(operationViewList.DeleteOperations);

            return _unitOfWork.SaveChanges() > 0;
        }
        /// <summary>
        /// 新增患者手术
        /// </summary>
        /// <param name="operationViewList"></param>
        /// <returns></returns>
        private async Task InsertPatientOperation(List<PatientOperationView> operationViewList)
        {
            var inpatientDatas = await _inpatientDataRepository.GetInpatientListByCaseNumberListAsync(operationViewList.Select(x => x.CaseNumber).Distinct().ToList());
            var operationList = await _patientOperationRepository.GetByInpatientIDList(inpatientDatas.Select(x => x.ID).ToList());
            foreach (var operationView in operationViewList)
            {
                var patientData = inpatientDatas.FirstOrDefault(x => x.CaseNumber == operationView.CaseNumber);
                if (patientData == null) continue;
                var operation = operationList.FirstOrDefault(x => x.CaseNumber == operationView.CaseNumber && x.HISOperationNo == operationView.HISOperationNo);
                if (operation == null)
                {
                    operation = new PatientOperationInfo()
                    {
                        InpatientID = patientData.ID,
                        CaseNumber = operationView.CaseNumber,
                        ChartNo = operationView.ChartNo,
                        OperationName = operationView.OperationName,
                        ScheduledDatetime = operationView.ScheduledDateTime,
                        AnesthesiaMethod = operationView.AnesthesiaMethod,
                        HISOperationNo = operationView.HISOperationNo,
                        AddEmployeeID = operationView.ModifyPersonID,
                        AddDate = DateTime.Now
                    };
                    operation.PatientOperationID = operation.GetId();
                    await _unitOfWork.GetRepository<PatientOperationInfo>().InsertAsync(operation);
                }
                else
                {
                    if (operationView.ScheduledDateTime != operation.ScheduledDatetime) operation.ScheduledDatetime = operationView.ScheduledDateTime;
                    if (operationView.OperationName != operation.OperationName) operation.OperationName = operationView.OperationName;
                    if (operationView.AnesthesiaMethod != operation.AnesthesiaMethod) operation.AnesthesiaMethod = operationView.AnesthesiaMethod;
                }
                operation.Modify(operationView.ModifyPersonID);
                //添加术前
                if (operationView.PreOP && operationView.ScheduledDateTime.HasValue) await HandlePreOperation(patientData, operation, operationView.HISOperationNo, operationView.ScheduledDateTime.Value, PREOP_EVENTSETTINGID);
            }
        }
        /// <summary>
        /// 更新患者手术
        /// </summary>
        /// <param name="operationViewList"></param>
        /// <returns></returns>
        private async Task UpdatePatientOperation(List<PatientOperationView> operationViewList)
        {
            var inpatientDatas = await _inpatientDataRepository.GetInpatientListByCaseNumberListAsync(operationViewList.Select(x => x.CaseNumber).Distinct().ToList());
            var operationList = await _patientOperationRepository.GetByInpatientIDList(inpatientDatas.Select(x => x.ID).ToList());
            foreach (var operationView in operationViewList)
            {
                var patientData = inpatientDatas.FirstOrDefault(x => x.CaseNumber == operationView.CaseNumber);
                if (patientData == null) continue;
                var operation = operationList.FirstOrDefault(x => x.CaseNumber == operationView.CaseNumber && x.HISOperationNo == operationView.HISOperationNo);
                if (operation == null) continue;

                if (operationView.OperationDate != operation.OperationDate) operation.OperationDate = operationView.OperationDate;
                operation.Modify(operationView.ModifyPersonID);
                //添加术前
                if (operationView.PreOP && operationView.ScheduledDateTime.HasValue) await HandlePreOperation(patientData, operation, operationView.HISOperationNo, operationView.ScheduledDateTime.Value, PREOP_EVENTSETTINGID);
            }
        }
        /// <summary>
        /// 删除患者手术
        /// </summary>
        /// <param name="operationViewList"></param>
        /// <returns></returns>
        private async Task DeletePatientOperation(List<PatientOperationView> operationViewList)
        {
            foreach (var operationView in operationViewList)
            {
                var operation = await _patientOperationRepository.GetPatientOperateByOperationNo(operationView.HISOperationNo);
                if (operation == null) continue;
                operation.Delete(operationView.ModifyPersonID);
                if (operation.ScheduledDatetime.HasValue) await DeleteEventAndCluster(operation.InpatientID, operation.CaseNumber, operation.ScheduledDatetime.Value, operationView.ModifyPersonID);
            }
        }

        /// <summary>
        /// 删除事件和集束
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="hISOperationNo"></param>
        /// <param name="scheduledDatetime"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task DeleteEventAndCluster(string inpatientID, string caseNumber, DateTime scheduledDatetime, string modifyPersonID)
        {
            await _patientEventCommonService.DelInpatientEvent(inpatientID, PREOP_EVENTSETTINGID, scheduledDatetime.Date, scheduledDatetime.TimeOfDay, modifyPersonID);
            //停止集束护嘱
            await _externalCommonService.StopPreClusterOrder(_config.Value.HospitalID, modifyPersonID, inpatientID, caseNumber);
        }

        /// <summary>
        /// 处理术前
        /// </summary>
        /// <param name="patient"></param>
        /// <param name="operation"></param>
        /// <param name="souceID"></param>
        /// <param name="scheduledDateTime"></param>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        private async Task HandlePreOperation(InpatientDataInfo patient, PatientOperationInfo operation, string souceID, DateTime scheduledDateTime, int assessListID)
        {
            var oldEvent = await _patientEventRepository.GetEventByInpatientIDAndSourceIDAsync(patient.ID, souceID);
            if (oldEvent != null && oldEvent.OccurDate.Add(oldEvent.OccurTime) == scheduledDateTime) return;

            await _patientEventCommonService.CallEventAPI(patient, patient.StationID, patient.DepartmentListID, patient.BedID, patient.BedNumber, scheduledDateTime
                , assessListID, assessListID.ToString(), assessListID.ToString(), souceID, oldEvent?.PatientEventID);

            var apiUrl = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "PreOperationAPI");
            if (string.IsNullOrEmpty(apiUrl)) return;

            var preOperation = new List<PatientOperation>
            {
                new() {
                    CaseNumber = operation.CaseNumber,
                    ChartNo = operation.ChartNo,
                    Name = operation.OperationName,
                    ScheduledDateTime = operation.ScheduledDatetime,
                    AnesthesiaMethod = operation.AnesthesiaMethod,
                    HISOperationNo = operation.HISOperationNo
                }
            };

            await _commonHelper.CallPostAPI(apiUrl, patient.ID, patient.CaseNumber, preOperation);
        }
    }
}