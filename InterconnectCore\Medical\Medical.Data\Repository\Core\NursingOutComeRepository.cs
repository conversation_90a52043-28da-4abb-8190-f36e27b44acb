﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class NursingOutComeRepository : INursingOutComeRepository
    {
        private MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public NursingOutComeRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService

            )
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据ID查找记录
        /// </summary>
        /// <param name="id">序号</param>
        /// <param name="language">语言码</param>
        /// <returns></returns>
        public async Task<NursingOutComeInfo> GetOneAsync(int id)
        {
            var datas = (List<NursingOutComeInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas.Where(t => t.ID == id).SingleOrDefault();
            }
            return new NursingOutComeInfo();
        }
        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<NursingOutComeInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("language", out var language);
            return await _medicalDbContext.NursingOutComes.Where(m => m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.NursingOutCome.GetKey(_sessionCommonServer);
        }

        public async Task<List<NursingOutComeInfo>> GetAsync()
        {
            var datas = (List<NursingOutComeInfo>)await GetCacheAsync();

            if (datas != null)
            {
                return datas;
            }

            return new List<NursingOutComeInfo>();
        }
    }
}