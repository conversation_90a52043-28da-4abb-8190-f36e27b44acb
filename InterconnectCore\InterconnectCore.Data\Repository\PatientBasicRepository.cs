﻿using InterconnectCore.Data.Context;
using InterconnectCore.Data.Interface;
using InterconnectCore.Models;

namespace InterconnectCore.Data.Repository
{
    public class PatientBasicRepository : IPatientBasicRepository
    {
        private DataOutContext _DataOutConnection = null;

        public PatientBasicRepository(DataOutContext db)
        {
            _DataOutConnection = db;
        }
        public List<PatientBasicInfo> GetPatientBaseData(int tongbuCount)
        {
            return _DataOutConnection.PatientBasicInfos.Where(m => m.DataPumpFlag != "*"
            && ((m.Counts ?? 0) < tongbuCount)).ToList();
        }
    }
}
