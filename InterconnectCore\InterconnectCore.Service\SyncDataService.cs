﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using InterconnectCore.Service.Interface;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.ViewModels.View;

namespace InterconnectCore.Service
{
    public class SyncDataService : ISyncDataService
    {
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly ISynchronizeLogRepository _synchronizeLogRepository;
        private readonly SyncBackPatientScoreService _syncBackPatientScoreService;
        private readonly SyncBackPatientAssessService _syncBackPatientAssessService;
        private readonly SyncBackPatientSkinTestService _syncBackPatientSkinTestService;
        private readonly SyncBackMedicationClosingControlLogService _syncBackMedicationClosingControlLogService;
        public SyncDataService(
            ISynchronizeLogRepository synchronizeLogRepository,
            SyncBackPatientScoreService syncBackPatientScoreService,
            SyncBackPatientAssessService syncBackPatientAssessService,
            IUnitOfWork<MedicalDbContext> unitOfWork,
            SyncBackPatientSkinTestService syncBackPatientSkinTestService,
            SyncBackMedicationClosingControlLogService syncBackMedicationClosingControlLogService)
        {
            _synchronizeLogRepository = synchronizeLogRepository;
            _syncBackPatientScoreService = syncBackPatientScoreService;
            _syncBackPatientAssessService = syncBackPatientAssessService;
            _unitOfWork = unitOfWork;
            _syncBackPatientSkinTestService = syncBackPatientSkinTestService;
            _syncBackMedicationClosingControlLogService = syncBackMedicationClosingControlLogService;
        }
        /// <summary>
        /// 回传设置定时任务
        /// </summary>
        /// <param name="syncData">回传数据</param>
        /// <param name="language">语言类别</param>
        /// <param name="hospitalID">医院类别</param>
        /// <returns></returns>
        public bool SyncBackToHIS(SyncDataBackLog syncData, int language, string hospitalID)
        {
            BackgroundJob.Enqueue(() => SyncBackToHISAsyncJob(syncData, language, hospitalID));
            return true;
        }
        /// <summary>
        /// 回传任务
        /// </summary>
        /// <param name="syncData"></param>
        /// <param name="language"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncBackToHISAsyncJob(SyncDataBackLog syncData, int language, string hospitalID)
        {
            var successFlag = false;
            switch (syncData.DataType)
            {
                case DataType.PatientScore:
                    successFlag = await _syncBackPatientScoreService.SyncDataAsync(syncData, language, hospitalID);
                    break;
                case DataType.PatientAssessment:
                    successFlag = await _syncBackPatientAssessService.SyncDataAsync(syncData, language, hospitalID);
                    break;
                case DataType.PatientMedicineScheduleSkinTest:
                    successFlag = await _syncBackPatientSkinTestService.SyncDataAsync(syncData, hospitalID);
                    break;
                case DataType.PatientMedicationClosingControl:
                    successFlag = await _syncBackMedicationClosingControlLogService.SyncDataAsync(syncData, hospitalID);
                    break;
            };
            if (successFlag) { 
                return true;
            }
            var synchronizeLog  = await _synchronizeLogRepository.GetSynchronizeLogById(syncData.LogID);
            if (synchronizeLog != null)
            {
                synchronizeLog.SuccessFlag ="";
                synchronizeLog.Modify("InterconnectCore");
            }
            _unitOfWork.SaveChanges();
            return successFlag;
        }
    }
}
