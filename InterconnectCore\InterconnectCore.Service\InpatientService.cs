﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Data.Context;
using InterconnectCore.Models;
using InterconnectCore.Service.Interface;
using InterconnectCore.Services;
using InterconnectCore.ViewModels;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.Interface;
using Medical.ViewModels.View;
using MedicalExternalCommon.Service;
using Microsoft.Extensions.Options;
using NLog;
using static InterconnectCore.Common.Enums;

namespace InterconnectCore.Service
{
    public class InpatientService : IInpatientService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        //Medical
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IInpatientLogRepository _inpatientLogRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IBedListRepository _bedListRepository;
        private readonly IPatientListIconRepository _patientListIconRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IEventSettingRepository _eventSettingRepository;
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly IPatientDeliveryCareMainRepository _patientDeliveryCareMainRepository;
        private readonly IPatientDeliveryRecordRepository _patientDeliveryRecordRepository;
        private readonly IUserRepository _userRepository;
        private readonly IAssessMainRepository _assessMainRepository;
        private readonly INewBornRecordRepository _newBornRecordRepository;
        private readonly IPatientEventRepository _patientEventRepository;
        private readonly IAssessListRepository _assessListRepository;
        private readonly PatientProfileMarkService _patientProfileMarkService;
        private readonly ExternalCommonService _externalCommonService;
        private readonly PatientEventCommonService _patientEventCommonService;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly MedicalDbContext _medicalDbContext = null;
        //InterconnectCore
        private readonly IPatientBasicService _patientBasicService;
        private readonly ISyncLogService _syncLogService;
        private readonly IBedService _bedService;
        private readonly IUnitOfWork<DataOutContext> _unitOfOutWork;
        private readonly IOptions<ViewModels.SystemConfig> _config;
        private readonly CommonHelper _commonHelper;
        private readonly IRequestApiService _requestApiService;
        private readonly MQCommonService _mQCommonService;
        private readonly StationaShiftCommonService _stationShiftCommonService;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        private readonly DataTableEditListService _dataTableEditListService;
        /// <summary>
        /// 系统默认操作人
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 医疗院代码
        /// </summary>
        private string HOSPITALID = "";
        /// <summary>
        /// 更新缓存API
        /// </summary>
        private string CACHEUPDATEAPI = "";
        /// <summary>
        /// 患者MarkView
        /// </summary>
        private List<MarkView> MarkViewList = new List<MarkView>();

        /// <summary>
        /// 患者改变类型
        /// </summary>
        private List<InPatientChangeViewInfo> InPatientChangeList = new List<InPatientChangeViewInfo>();
        /// <summary>
        /// 患者Profile
        /// </summary>
        private List<PatientProfile> PatientProfileList = new List<PatientProfile>();
        /// <summary>
        /// 消息列表
        /// </summary>
        private List<MessageModel> SendMessageList = new List<MessageModel>();

        #region --常量配置
        /// <summary>
        /// 转出床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNOUTBED = 5317;
        /// <summary>
        /// 转入床事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TURNINBED = 5318;
        /// <summary>
        /// 转出科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT = 5319;
        /// <summary>
        /// 转入科事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSINDEPT = 5320;
        /// <summary>
        /// 入院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_ADMISSION = 2872;
        /// <summary>
        /// 转入病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSIN = 2874;
        /// <summary>
        /// 转病区事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUT = 2875;
        /// <summary>
        /// 通知出院事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DISCHARGE = 6571;
        /// <summary>
        /// 死亡
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_DEATH = 2876;
        /// <summary>
        /// 入监事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSIN_ICU = 4462;
        /// <summary>
        /// 出监事件ID
        /// </summary>
        private const int EVENTSETTING_ASSESSLISTID_TRANSOUT_ICU = 4463;

        /// <summary>
        /// 转出事件ID集合
        /// </summary>
        private static readonly List<int> EVENTSETTING_ASSESSLISTID_OUT = [5317, 5319, 2875, 4463];

        /// <summary>
        /// 转入事件ID集合
        /// </summary>
        private static readonly List<int> EVENTSETTING_ASSESSLISTID_IN = [5318, 5120, 2874, 4462];
        #endregion

        #region --构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="inpatientDataRepository"></param>
        /// <param name="patientBasicDataRepository"></param>
        /// <param name="inpatientLogRepository"></param>
        /// <param name="stationListRepository"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="bedListRepository"></param>
        /// <param name="patientListIconRepository"></param>
        /// <param name="appConfigSettingRepository"></param>
        /// <param name="eventSettingRepository"></param>
        /// <param name="aPISettingRepository"></param>
        /// <param name="patientDeliveryCareMainRepository"></param>
        /// <param name="patientDeliveryRecordRepository"></param>
        /// <param name="userRepository"></param>
        /// <param name="assessMainRepository"></param>
        /// <param name="newBornRecordRepository"></param>
        /// <param name="patientEventRepository"></param>
        /// <param name="assessListRepository"></param>
        /// <param name="patientProfileMarkService"></param>
        /// <param name="externalCommonService"></param>
        /// <param name="patientEventCommonService"></param>
        /// <param name="patientBasicService"></param>
        /// <param name="syncLogService"></param>
        /// <param name="bedService"></param>
        /// <param name="mQCommonService"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="medicalDbContext"></param>
        /// <param name="unitOfOutWork"></param>
        /// <param name="config"></param>
        /// <param name="commonHelper"></param>
        /// <param name="requestApiService"></param>
        public InpatientService(
            IInpatientDataRepository inpatientDataRepository
            , IPatientBasicDataRepository patientBasicDataRepository
            , IInpatientLogRepository inpatientLogRepository
            , IStationListRepository stationListRepository
            , IDepartmentListRepository departmentListRepository
            , IBedListRepository bedListRepository
            , IPatientListIconRepository patientListIconRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IEventSettingRepository eventSettingRepository
            , IAPISettingRepository aPISettingRepository
            , IPatientDeliveryCareMainRepository patientDeliveryCareMainRepository
            , IPatientDeliveryRecordRepository patientDeliveryRecordRepository
            , IUserRepository userRepository
            , IAssessMainRepository assessMainRepository
            , INewBornRecordRepository newBornRecordRepository
            , IPatientEventRepository patientEventRepository
            , IAssessListRepository assessListRepository
            , PatientProfileMarkService patientProfileMarkService
            , ExternalCommonService externalCommonService
            , PatientEventCommonService patientEventCommonService
            , IPatientBasicService patientBasicService
            , ISyncLogService syncLogService
            , IBedService bedService
            , MQCommonService mQCommonService
            , IUnitOfWork<MedicalDbContext> unitOfWork
            , MedicalDbContext medicalDbContext
            , IUnitOfWork<DataOutContext> unitOfOutWork
            , IOptions<ViewModels.SystemConfig> config
            , CommonHelper commonHelper
            , IRequestApiService requestApiService
            , StationaShiftCommonService stationShiftCommonService
            , ISettingDescriptionRepository settingDescriptionRepository
            , DataTableEditListService dataTableEditListService
            )
        {
            _patientBasicDataRepository = patientBasicDataRepository;
            _inpatientLogRepository = inpatientLogRepository;
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _bedListRepository = bedListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _patientListIconRepository = patientListIconRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _eventSettingRepository = eventSettingRepository;
            _patientProfileMarkService = patientProfileMarkService;
            _externalCommonService = externalCommonService;
            _aPISettingRepository = aPISettingRepository;
            _patientDeliveryCareMainRepository = patientDeliveryCareMainRepository;
            _patientDeliveryRecordRepository = patientDeliveryRecordRepository;
            _patientEventCommonService = patientEventCommonService;
            _patientBasicService = patientBasicService;
            _mQCommonService = mQCommonService;
            _userRepository = userRepository;
            _assessMainRepository = assessMainRepository;
            _newBornRecordRepository = newBornRecordRepository;
            _patientEventRepository = patientEventRepository;
            _assessListRepository = assessListRepository;
            _syncLogService = syncLogService;
            _bedService = bedService;
            _unitOfWork = unitOfWork;
            _medicalDbContext = medicalDbContext;
            _unitOfOutWork = unitOfOutWork;
            _config = config;
            _commonHelper = commonHelper;
            _requestApiService = requestApiService;
            _stationShiftCommonService = stationShiftCommonService;
            _settingDescriptionRepository = settingDescriptionRepository;
            _dataTableEditListService = dataTableEditListService;
        }
        #endregion

        /// <summary>
        /// 同步患者数据
        /// </summary>
        /// <param name="inpatientDataViews"></param>
        /// <returns></returns>

        public async Task<bool> SyncInpatientData(List<InPatientDataView> inpatientDataViews)
        {
            _logger.Info("开始同步住院患者数据" + inpatientDataViews.Count + "条");
            #region "数据加载"
            var hospitalID = _config.Value.HospitalID;
            var syncBaseViewDict = await GetSyncBaseViewAsync(hospitalID);

            var modifyPersonID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "ModifyPersonID");
            #endregion "数据加载"
            //日志,判断执行到第几笔数据
            var count = 0;
            var result = false;
            foreach (var item in inpatientDataViews) //记录作业正在执行日志
            {
                count++;
                _logger.Info("开始同步第" + count + "个患者住院信息 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);
                if (item.InHospitalStatus >= (int)PatientStatus.B)
                {
                    continue;
                }

                if (string.IsNullOrEmpty(item.StationCode))
                {
                    _logger.Warn("住院信息 CaseNumber: " + item.CaseNumber + " ChartNo: " + item.ChartNo + "科室信息为空");
                    continue;
                }

                if (string.IsNullOrEmpty(item.DepartmentCode))
                {
                    _logger.Warn("住院信息 CaseNumber: " + item.CaseNumber + " ChartNo: " + item.ChartNo + "病区信息为空");
                    continue;
                }

                if (string.IsNullOrEmpty(item.BedNumber))
                {
                    _logger.Warn("住院信息 CaseNumber: " + item.CaseNumber + " ChartNo: " + item.ChartNo + "床位信息为空");
                    continue;
                }
                //同步在院病人数据
                try
                {
                    var syncBaseView = syncBaseViewDict.CloneObj();
                    result = await SyncInpatientDetail(item, syncBaseView, hospitalID, modifyPersonID);
                }
                catch (Exception ex)
                {
                    _logger.Error("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步失败！" + ex.ToString());
                }
                _logger.Info("病人CaseNumber" + item.CaseNumber + " ChartNo:" + item.ChartNo + "同步完成");
            }
            return true;
        }
        /// <summary>
        /// 基础数据获取(同步调用使用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<SyncBaseView> GetSyncBaseViewAsync(string hospitalID)
        {
            var patientListIcons = await _patientListIconRepository.GetAllAsync<PatientListIconInfo>();
            var filteredPatientListIcons = patientListIcons
                .Where(icon => icon.HospitalID == hospitalID
                            && icon.IdentifyCategory.Contains("NursingLevel")
                            && icon.DeleteFlag != "*")
                .ToList();

            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var userList = await _userRepository.GetAllAsync<UserInfo>();
            var hospitalBaseDict = await GetHospitalBaseDict();

            return new SyncBaseView
            {
                HospitalBaseDictViews = hospitalBaseDict,
                PatientListIconList = filteredPatientListIcons,
                EventSettingList = eventSettings,
                UserInfos = userList
            };
        }

        /// <summary>
        /// 获取医院基础字典数据
        /// </summary>
        /// <returns></returns>
        private async Task<HospitalBaseDictView> GetHospitalBaseDict()
        {
            var hospitalDictView = new HospitalBaseDictView
            {
                StationList = await _stationListRepository.GetAllAsync<StationListInfo>(),
                BedList = await _bedListRepository.GetAllAsync<BedListInfo>(),//床位信息
                DepartmentList = await _departmentListRepository.GetAllAsync<DepartmentListInfo>() //科室信息
            };
            return hospitalDictView;
        }

        /// <summary>
        /// 同步住院病人详细信息
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="syncBaseView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<bool> SyncInpatientDetail(InPatientDataView hisInpatientData, SyncBaseView syncBaseView, string hospitalID, string modifyPersonID)
        {
            // 初始化
            bool upDataDBFlag = false;
            var patientString = " CaseNumber[" + hisInpatientData.CaseNumber + "]  ChartNo [" + hisInpatientData.ChartNo + "]";
            // 准备数据 
            var (hospitalBaseDict, ageAssessListID, userInfo) = await PrepareData(hisInpatientData, syncBaseView.HospitalBaseDictViews, syncBaseView.UserInfos, hospitalID);
            if (hospitalBaseDict == null)
            {
                _logger.Warn("字典获取失败");
                return false;
            }
            //同步病人基本信息 PatientBasicData  新增||修改
            var patientBasicDatas = await SyncPatientBase(hisInpatientData);
            if (patientBasicDatas == null || patientBasicDatas.Count == 0)
            {
                _logger.Warn(patientString + "||病人基本信息同步失败");
                return false;
            }
            //获取病人基本信息，没有基本信息，不同步数据
            _logger.Info(patientString + "获取病人基本信息");
            var patientBasicData = await _patientBasicDataRepository.GetAsync(hospitalID, hisInpatientData.ChartNo);
            if (patientBasicData == null)
            {
                _logger.Error("Inpatient" + " 表: PatientBasicData 病案号 CaseNumber ["
                + hisInpatientData.CaseNumber + "] ChartNo [" + hisInpatientData.ChartNo + "] 查询病人基本信息错误!");
                return false;
            }
            //获取病人信息（包含出院和删除）
            var tempPatient = await _inpatientDataRepository.GetInpatientAsyncByCaseNumber(hisInpatientData.CaseNumber, hospitalID);
            // 判断是否需要出院处理
            bool isDischargeFlag = await CheckIfDischargeNeeded(hisInpatientData, hospitalBaseDict, hospitalID);
            if (isDischargeFlag)
            {
                if (tempPatient != null && tempPatient.DeleteFlag != "*")
                {
                    (upDataDBFlag, MarkViewList, InPatientChangeList) = await HandleDischargeAsync(hisInpatientData, hospitalBaseDict, syncBaseView.EventSettingList, hospitalID, tempPatient);
                }
                else
                {
                    return false;
                }
            }
            else
            {
                //判断床位上是否是本病人，如果不是先移除
                var inPatientByBed = await _inpatientDataRepository.GetByInpatientBedIDAsync(hospitalBaseDict.BedList[0].ID, hospitalID);
                if (inPatientByBed != null && inPatientByBed.CaseNumber != hisInpatientData.CaseNumber)
                {
                    _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "UPBed"
                        , "床位上患者，CaseNumber：" + inPatientByBed.CaseNumber + "不在病人清单，更新为病人" + hisInpatientData.CaseNumber
                        , "TongBu", false);
                    inPatientByBed.InHospitalStatus = (int)PatientStatus.OUT;
                }
                // 如果病人不存在且不需要出院，处理新增住院病人
                if (tempPatient == null || tempPatient.DeleteFlag == "*")
                {
                    (upDataDBFlag, PatientProfileList, SendMessageList) = await HandleNewInpatientAsync(hisInpatientData, hospitalBaseDict, patientBasicData, tempPatient, hospitalID, syncBaseView.PatientListIconList, ageAssessListID, modifyPersonID, syncBaseView.EventSettingList);
                }
                // 如果病人存在且不需要出院，处理更新住院病人
                else
                {
                    (upDataDBFlag, PatientProfileList, SendMessageList) = await HandleUpdateInpatientAsync(hisInpatientData, hospitalBaseDict, patientBasicData, syncBaseView.EventSettingList, tempPatient, hospitalID, syncBaseView.PatientListIconList, ageAssessListID);
                }
            }

            // 如果没有需要更新的内容，直接返回
            if (!upDataDBFlag)
            {
                _logger.Info("不需要更新返回,CaseNumber=" + hisInpatientData.CaseNumber);
                return true;
            }

            // 提交数据到数据库
            var successFlag = CommitChangesAsync();
            // 处理Mark标记
            await CallMarkAPI(MarkViewList);
            _logger.Info("写PatientProfile");
            await _commonHelper.AddProfile(PatientProfileList);
            _logger.Info("调用病人信息发生变化,调用对应的API");
            //病人信息变化，调用相应的APi进行处理
            await CheckInPatient(InPatientChangeList);
            _logger.Info("发送MQ");
            await _mQCommonService.SendingMessage(SendMessageList);
            return successFlag;
        }

        /// <summary>
        /// 2. 准备数据（提取获取数据的逻辑）
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDictView"></param>
        /// <param name="userList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<(HospitalBaseDictView, int, UserInfo)> PrepareData(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDictView, List<UserInfo> userList, string hospitalID)
        {
            int ageAssessListID = await GetAgeAssessListID();
            if (ageAssessListID == 0)
                return (null, 0, null);

            var hospitalBaseDict = await GetinPatientBaseDictAsync(hisInpatientData, hospitalBaseDictView);
            if (hospitalBaseDict == null || !CheckPatientBaseDict(hospitalBaseDict))
                return (null, 0, null);

            var userInfo = userList.FirstOrDefault(m => m.PhysicianID == hisInpatientData.AttendingPhysicianID);
            if (userInfo != null)
                hisInpatientData.AttendingPhysicianID = userInfo.UserID.ToString();

            return (hospitalBaseDict, ageAssessListID, userInfo);
        }

        /// <summary>
        /// 同步病人基本信息
        /// </summary>
        /// <param name="inPatientData"></param>
        /// <returns></returns>
        private async Task<List<PatientBasicDataInfo>> SyncPatientBase(InPatientDataView inPatientData)
        {
            var patientBasicList = new List<PatientBasicInfo>();
            var patientBasic = new PatientBasicInfo
            {
                ChartNo = inPatientData.ChartNo,
                PatientName = inPatientData.PatientName,
                Gender = inPatientData.Gender,
                DateOfBirth = inPatientData.DateOfBirth.Value,
                IdentityID = inPatientData.IdentityID,
                NativePlace = inPatientData.NativePlace,
                NativePlaceCode = "",
                BloodType = ""
            };
            patientBasicList.Add(patientBasic);
            return await _patientBasicService.SyncPatientBaseDetail(patientBasicList);
        }

        /// <summary>
        /// 3. 获取年龄对应的AssessListID
        /// </summary>
        /// <returns></returns>
        private async Task<int> GetAgeAssessListID()
        {
            var resultAgeAssessListID = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "AgeAssessListID");
            if (!StringCheck.IsNumeric(resultAgeAssessListID))
            {
                _logger.Warn("年龄AssessListID配置错误");
                return 0;
            }

            return int.Parse(resultAgeAssessListID);
        }

        /// <summary>
        /// 4. 判断是否需要处理出院
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> CheckIfDischargeNeeded(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDict, string hospitalID)
        {
            var tempPatient = await _inpatientDataRepository.GetAsyncByCaseNumber(hisInpatientData.CaseNumber, hospitalID);
            return hisInpatientData.InHospitalStatus >= 60 && tempPatient != null && tempPatient.InHospitalStatus != hisInpatientData.InHospitalStatus && hisInpatientData.InHospitalStatus > (int)PatientStatus.I;
        }

        /// <summary>
        /// 5. 处理出院
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<(bool, List<MarkView>, List<InPatientChangeViewInfo>)> HandleDischargeAsync(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDict, List<EventSettingInfo> eventSettings, string hospitalID, InpatientDataInfo tempPatient)
        {
            _logger.Info("住院病人出院处理:" + hisInpatientData.CaseNumber);
            var syncInpatientDataView = await SyncInpatientDischarge(tempPatient, hisInpatientData, eventSettings);
            var upDataDBFlag = syncInpatientDataView.RetureFlag;
            MarkViewList = syncInpatientDataView.MarkViewList;
            InPatientChangeList = syncInpatientDataView.InPatientChangeViewList;
            return (upDataDBFlag, MarkViewList, InPatientChangeList);
        }

        /// <summary>
        /// 7. 处理新增住院病人
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patientBasicData"></param>
        /// <param name="hospitalID"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="ageAssessListID"></param>
        /// <param name="modifyPersonID"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task<(bool, List<PatientProfile>, List<MessageModel>)> HandleNewInpatientAsync(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDict, PatientBasicDataInfo patientBasicData, InpatientDataInfo tempPatient, string hospitalID, List<PatientListIconInfo> patientListIconList, int ageAssessListID, string modifyPersonID, List<EventSettingInfo> eventSettings)
        {
            _logger.Info("新增住院病人:" + hisInpatientData.CaseNumber);
            // tempPatient 要么为空，要么存在是删除状态，删除状态不算新增，要删除旧入院事件
            var deleteOldEventFlag = false;
            DateTime? oldAdmissionDate = null;
            TimeSpan? oldAdmissionTime = null;
            if (tempPatient != null)
            {
                deleteOldEventFlag = true;
                oldAdmissionDate = tempPatient.AdmissionDate;
                oldAdmissionTime = tempPatient.AdmissionTime;
            }
            var cccInpatientData = await NewInpatientData(hisInpatientData, hospitalBaseDict, patientBasicData, hospitalID, tempPatient);

            var inpatientLogs = await _inpatientLogRepository.GetByCaseNumberAsync(cccInpatientData.CaseNumber);

            await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, cccInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
            //处理病人历史Profile
            var param = $"?chartNo={cccInpatientData.ChartNo}&inpatientID={cccInpatientData.ID}";
            await CallMedicalAPIByPostAndParam("DeletePatientProfileOwnDueDay", param);

            var markView = await AddInpatientToProfileMark(cccInpatientData, hisInpatientData, patientBasicData);
            MarkViewList.Add(markView);

            var result = await CreateAddPatientProfile(cccInpatientData, patientListIconList, ageAssessListID, patientBasicData.DateOfBirth, modifyPersonID);
            var patientProfileList = result.Item1; // profile 数据
            var sendMessageList = result.Item2; // 发送消息通知
            // 如果是 撤销入院后再入院，恢复删除标记的，先删除旧的入院患者事件
            if (deleteOldEventFlag)
            {
                await _patientEventCommonService.DelInpatientEvent(cccInpatientData.ID, EVENTSETTING_ASSESSLISTID_ADMISSION, oldAdmissionDate.Value, oldAdmissionTime.Value, MODIFYPERSONID);
            }
            await SetInpatientLogAndEvent(inpatientLogs, cccInpatientData, cccInpatientData.StationID, cccInpatientData.DepartmentListID,
                                           cccInpatientData.BedID, cccInpatientData.BedNumber,
                                           cccInpatientData.AdmissionDate.Add(cccInpatientData.AdmissionTime),
                                           EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);

            var upDataDBFlag = true;
            _logger.Info("新增住院病人信息结束");
            SyncNewDataTrigger(cccInpatientData, hospitalID);
            return (upDataDBFlag, patientProfileList, sendMessageList);
        }

        /// <summary>
        /// 8. 处理修改住院病人
        /// </summary>
        /// <param name="hisInpatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patientBasicData"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="ageAssessListID"></param>
        /// <returns></returns>
        private async Task<(bool, List<PatientProfile>, List<MessageModel>)> HandleUpdateInpatientAsync(InPatientDataView hisInpatientData, HospitalBaseDictView hospitalBaseDict, PatientBasicDataInfo patientBasicData, List<EventSettingInfo> eventSettings, InpatientDataInfo tempPatient, string hospitalID, List<PatientListIconInfo> patientListIconList, int ageAssessListID)
        {
            _logger.Info("修改住院病人 ,CaseNumber=" + hisInpatientData.CaseNumber);
            var result = await UpdateInpatientData(tempPatient, hisInpatientData, hospitalBaseDict, patientListIconList, patientBasicData.PatientID, "Inpatient", eventSettings, hospitalID, ageAssessListID);
            var upDataDBFlag = result.Item1;
            var patientProfileList = result.Item2;
            var sendMessageList = result.Item3;

            var upProfileResult = await _patientProfileMarkService.UPDataPatientProfileMark(hisInpatientData.ChartNo, hisInpatientData.BedNumber, hospitalBaseDict.StationList[0].StationCode, hospitalBaseDict.DepartmentList[0].DepartmentCode);
            if (upProfileResult)
            {
                upDataDBFlag = true;
            }
            _logger.Info("修改住院病人结束,CaseNumber=" + hisInpatientData.CaseNumber);
            return (upDataDBFlag, patientProfileList, sendMessageList);
        }

        /// <summary>
        ///  9. 提交数据库变更
        /// </summary>
        private bool CommitChangesAsync()
        {
            try
            {
                _unitOfWork.SaveChanges();
                _unitOfOutWork.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步失败: {ex.ToString()}");
                return false;
            }
        }

        /// <summary>
        /// 触发患者派班逻辑
        /// </summary>
        /// <param name="inpatientData"></param>
        /// <param name="hospitalID"></param>
        private async void SyncNewDataTrigger(InpatientDataInfo inpatientData, string hospitalID)
        {
            //增加作业，启动派班
            var message = inpatientData.CaseNumber + "新增病人数据，进行自动派班";
            _logger.Info(message + ListToJson.ToJson(inpatientData));
            AddCheckAttendance(inpatientData.ID, inpatientData.ChartNo, inpatientData.CaseNumber
                , inpatientData.BedID, inpatientData.StationID, hospitalID, message);
            //添加二次入院患者同步历史过敏数据
            var param = $"?chartNo={inpatientData.ChartNo}&inpatientID={inpatientData.ID}";
            await CallMedicalAPIByPostAndParam("SyncInPatientHistoryAllergyByChartNoAPI", param);
        }

        /// <summary>
        /// 调用medicalAPI(Url+Param组装方式回传)
        /// </summary>
        /// <param name="settingCode"></param>
        /// <param name="param"></param>
        public async Task CallMedicalAPIByPostAndParam(string settingCode, string param)
        {
            try
            {
                await _requestApiService.RequestAPIPostAndParam(settingCode, param);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
            }
        }
        /// 增加派班数据
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="chartNO"></param>
        /// <param name="caseNumer"></param>
        /// <param name="bedID"></param>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        private async void AddCheckAttendance(string inpatientID, string chartNO, string caseNumer, int bedID, int stationID, string hospitalID, string message)
        {
            var inpatientDataView = new CheckAttendanceView()
            {
                InpatientID = inpatientID,
                BedID = bedID,
                StationID = stationID,
                CaseNumber = caseNumer,
                HospitalID = hospitalID,
                ChartNo = chartNO
            };
            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "CheckAttendance", message, MODIFYPERSONID, true);
            await AddCheckAttendanceJob(inpatientDataView);
        }

        /// <summary>
        /// 检核派班记录（患者新入 或床位变化）
        /// </summary>
        /// <param name="checkAttendanceView"></param>
        /// <returns></returns>
        public async Task<bool> AddCheckAttendanceJob(CheckAttendanceView checkAttendanceView)
        {
            var url = "";
            _logger.Info("患者新入 或床位变化，启动岗位派班(AddCheckAttendanceJob),inpatientID=" + checkAttendanceView.InpatientID);
            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel", checkAttendanceView.CaseNumber + "患者新入 或床位变化，启动岗位派班", MODIFYPERSONID, true);
            try
            {
                try
                {
                    await _requestApiService.RequestAPI("CheckAttendanceAPI", ListToJson.ToJson(checkAttendanceView), checkAttendanceView.InpatientID, checkAttendanceView.CaseNumber);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "自动派班，checkAttendanceView CaseNumber [" + checkAttendanceView.CaseNumber + "]   保存数据失败!");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "检核派班记录（患者新入 或床位变化）AddCheckAttendanceJob失败，API:" + url);
                return false;
            }
        }

        /// <summary>
        /// 获取同步需要的基本字典信息
        /// </summary>
        /// <param name="inPatientData"></param>
        /// <param name="hospitalDictView"></param>
        /// <returns></returns>
        private async Task<HospitalBaseDictView> GetinPatientBaseDictAsync(InPatientDataView inPatientData, HospitalBaseDictView hospitalDictView)
        {
            var hospitalDict = new HospitalBaseDictView();
            var medicalStationList = hospitalDictView.StationList; //病区字典
            var medicalDepartmentList = hospitalDictView.DepartmentList; //科室字典
            var medicalBedList = hospitalDictView.BedList; //床位字典

            // 获取科室信息
            var departments = medicalDepartmentList.Where(m => m.DepartmentCode == inPatientData.DepartmentCode).ToList();
            if (departments == null || departments.Count <= 0)
            {
                return null;
            }

            // 获取病区信息
            var stations = medicalStationList.Where(m => m.StationCode == inPatientData.StationCode).ToList();
            if (stations == null && stations.Count <= 0)
            {
                return null;
            }

            // 如果有婴儿的SN，检查床位
            if (inPatientData.BabySN > 0)
            {
                var bedListInfo = await _bedListRepository.GetByStationIDAndBedNumber(stations[0].ID, inPatientData.BedNumber);
                if (bedListInfo == null)
                {
                    // 如果找不到床位，则使用母床信息创建新床位
                    var bedListMother = await _bedListRepository.GetByStationIDAndBedNumber(stations[0].ID, inPatientData.BedCode);
                    var sort = bedListMother?.Sort ?? 0;
                    var returnFlag = await _bedService.CreateOneBed(stations[0].ID, departments[0].ID, inPatientData.BedNumber, (short)sort);

                    // 刷新床位列表
                    if (returnFlag)
                    {
                        medicalBedList = await _bedListRepository.GetBedListNoCache();
                    }
                }
            }

            // 获取床位信息
            var bedList = medicalBedList
                .Where(m => m.BedNumber == inPatientData.BedNumber && m.StationID == stations[0].ID && m.DepartmentListID == departments[0].ID)
                .ToList();

            if (bedList.Count != 1)
            {
                return null;
            }

            // 填充医院字典数据
            hospitalDict.StationList = stations;
            hospitalDict.DepartmentList = departments;
            hospitalDict.BedList = bedList;

            return hospitalDict;
        }

        /// <summary>
        /// 判断获取的病人病区、科室、床位信息是否正常
        /// </summary>
        /// <param name="hospitalBaseDict"></param>
        /// <returns></returns>
        private static bool CheckPatientBaseDict(HospitalBaseDictView hospitalBaseDict)
        {
            if (hospitalBaseDict == null)
            {
                return false;
            }

            if (hospitalBaseDict.DepartmentList == null || hospitalBaseDict.DepartmentList.Count <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.StationList == null || hospitalBaseDict.StationList.Count <= 0)
            {
                return false;
            }
            if (hospitalBaseDict.BedList == null || hospitalBaseDict.BedList.Count <= 0)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 新增住院病人逻辑
        /// </summary>
        /// <param name="inPatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patient"></param>
        /// <param name="hospitalID"></param>
        /// <param name="tempPatient"></param>
        /// <returns></returns>
        private async Task<InpatientDataInfo> NewInpatientData(InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict, PatientBasicDataInfo patient, string hospitalID, InpatientDataInfo tempPatient)
        {
            var addFlag = tempPatient == null; // 判断是否需要新增

            // 如果需要新增，则初始化新对象；否则直接使用传入的 tempPatient
            var inpatient = addFlag ? new InpatientDataInfo() : tempPatient;

            if (addFlag)
            {
                inpatient.ID = inpatient.GetId(); // 新增时生成唯一 ID
            }

            // 填充病人信息
            inpatient.PatientID = patient.PatientID;
            inpatient.HospitalID = hospitalID;
            inpatient.CaseNumber = inPatientData.CaseNumber ?? "";
            inpatient.LocalCaseNumber = inPatientData.ChartNo ?? "";
            inpatient.ChartNo = inPatientData.ChartNo ?? "";
            inpatient.DepartmentListID = hospitalBaseDict.DepartmentList[0].ID;
            inpatient.StationID = hospitalBaseDict.StationList[0].ID;
            inpatient.BedID = hospitalBaseDict.BedList[0].ID;
            inpatient.BedNumber = inPatientData.BedNumber ?? "";
            inpatient.ICUFlag = hospitalBaseDict.BedList[0].ICUFlag ?? "";
            inpatient.ICDCode = inPatientData.ICDCode ?? ""; // 新增病人数据，不写 ICDCode，为了区分数据是否从诊断接口过来
            inpatient.Diagnosis = inPatientData.Diagnosis ?? "";
            inpatient.AttendingPhysicianID = inPatientData.AttendingPhysicianID ?? "";
            inpatient.Age = inPatientData.Age;
            inpatient.AgeDetail = inPatientData.AgeDetail;
            inpatient.BillingPattern = inPatientData.BillingPattern ?? "";
            inpatient.AdmissionDate = inPatientData.AdmissionDateTime.Date; // 住院日期时间
            inpatient.AdmissionTime = inPatientData.AdmissionDateTime.TimeOfDay; // 住院时间
            inpatient.DeleteFlag = "";
            inpatient.DischargeDate = inPatientData.DischargeDateTime.HasValue ? inPatientData.DischargeDateTime.Value.Date : (DateTime?)null;
            inpatient.DischargeTime = inPatientData.DischargeDateTime.HasValue ? inPatientData.DischargeDateTime.Value.TimeOfDay : (TimeSpan?)null;
            inpatient.NursingLevel = inPatientData.NursingLevel ?? "";
            if (inPatientData.NumberOfAdmissions.HasValue)
            {
                inpatient.NumberOfAdmissions = inPatientData.NumberOfAdmissions.Value;
            }
            else
            {
                // 获取之前的住院次数
                var inpatientDataInfos = await _inpatientDataRepository.GetListByChartNo(inpatient.ChartNo);
                inpatient.NumberOfAdmissions = inpatientDataInfos.Count + 1;
            }

            // 处理新生儿床位
            if (inPatientData.BabySN > 0)
            {
                inpatient.CaseNumber = inPatientData.CaseNumber;
                inpatient.ChartNo = inPatientData.ChartNo;

                var stationListInfo = await _stationListRepository.GetAsync(inpatient.StationID);
                var bedListInfo = await _bedListRepository.GetByStationIDAndBedNumber(stationListInfo.ID, inpatient.BedNumber);

                if (bedListInfo == null)
                {
                    // 如果新生儿床位不存在，则创建新床位
                    var sort = bedListInfo?.Sort ?? 0;
                    await _bedService.CreateOneBed(stationListInfo.ID, hospitalBaseDict.DepartmentList[0].ID, inpatient.BedNumber, (short)sort);
                }
            }

            // 设置在院状态和护理程序码
            inpatient.InHospitalStatus = inPatientData.InHospitalStatus;
            inpatient.NursingProcedureCode = "";

            // 更新修改人和修改时间
            inpatient.ModifyPersonID = MODIFYPERSONID;
            inpatient.ModifyDate = DateTime.Now;

            // 根据 addFlag 决定是新增还是更新
            if (addFlag)
            {
                _unitOfWork.GetRepository<InpatientDataInfo>().Insert(inpatient); // 新增病人信息
            }
            // 存储病人变化信息
            InPatientChangeList.Add(InPatientChangeView(inpatient.ID, inpatient.CaseNumber, "新增/修改", PatientType.PatientInsert));

            return inpatient;
        }

        /// <summary>
        /// 组装住院患者改变对象
        /// </summary>
        /// <param name="inPatientId"></param>
        /// <param name="caseNumber"></param>
        /// <param name="nursingLevel"></param>
        /// <param name="patientType"></param>
        /// <returns></returns>
        private static InPatientChangeViewInfo InPatientChangeView(string inPatientId, string caseNumber, string nursingLevel, PatientType patientType)
        {
            var inpatientChangeViewInfo = new InPatientChangeViewInfo()
            {
                InPatientID = inPatientId,
                CaseNumber = caseNumber,
                PatientType = patientType,
                NursingLevel = nursingLevel
            };
            return inpatientChangeViewInfo;
        }

        /// <summary>
        /// 新增Mark数据组装
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="hisInPatient"></param>
        /// <param name="patient"></param>
        /// <returns></returns>
        private async Task<MarkView> AddInpatientToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInPatient, PatientBasicDataInfo patient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
             { "chartNo", inpatient.ChartNo },
             { "patientName", patient.PatientName },
             { "gender", patient.Gender },
             { "nursingLevel", inpatient.NursingLevel },
             { "bedNumber", inpatient.BedNumber },
             { "stationCode", hisInPatient.StationCode },
             { "departmenCode", hisInPatient.DepartmentCode },
             { "hospitalID", hospitalID },
             { "admissionDate", hisInPatient.AdmissionDateTime.Date.ToString() }
            };
            var inpatientProfileMarkAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "InpatientProfileMarkAPI");
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = inpatientProfileMarkAPI,
                MarkDatas = markData,
                DataType = "新增住院病人"
            };
            return markView;
        }

        /// <summary>
        /// 组装字典对象
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        private static string GetKeyValue(Dictionary<string, string> dict)
        {
            string result;
            List<string> list = new();
            foreach (var item in dict)
            {
                list.Add(item.Key + "=" + item.Value);
            }
            result = string.Join("&", list.ToArray());
            return result;
        }

        /// <summary>
        /// 同步病人信息，组装呼叫Proflie对象
        /// </summary>
        /// <param name="inpatientData"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="ageAssessListID"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<Tuple<List<PatientProfile>, List<MessageModel>>> CreateAddPatientProfile(InpatientDataInfo inpatientData
            , List<PatientListIconInfo> patientListIconList
            , int ageAssessListID
            , DateTime? dateOfBirth
            ,string modifyPersonID)
        {
            var hospitalID = _config.Value.HospitalID;
            var patientProfiles = new List<PatientProfile>();
            var sendMessageList = new List<MessageModel>();
            //增加PatientProfiles数据(出生日期)
            if (dateOfBirth.HasValue)
            {
                patientProfiles.Add(_externalCommonService.CreateProfile(inpatientData, "Age", ageAssessListID
                    , dateOfBirth.Value.ToString("yyyy-MM-dd"), hospitalID, modifyPersonID));
            }
            //增加PatientProfiles数据(护理级别)
            patientProfiles.Add(CreateNursingLeveProfile(inpatientData, patientListIconList, hospitalID));

            //增加主诊断集束护理
            if (!string.IsNullOrEmpty(inpatientData.ICDCode))
            {
                var ids = await _externalCommonService.GetAssessListIDByICDCode(inpatientData.ICDCode, inpatientData.DepartmentListID);

                foreach (var item in ids)
                {
                    var profile = _externalCommonService.CreateProfile(inpatientData, "Diagnosis", item, "", hospitalID, modifyPersonID);
                    patientProfiles.Add(profile);
                }
                if (patientProfiles.Count > 0)
                {
                    sendMessageList.Add(_externalCommonService.CreateDiagnosisMessage(inpatientData));
                }
            }
            return new Tuple<List<PatientProfile>, List<MessageModel>>(patientProfiles, sendMessageList);
        }

        /// <summary>
        /// 创建护理级别Profile
        /// </summary>
        /// <param name="inPatient"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private static PatientProfile CreateNursingLeveProfile(InpatientDataInfo inPatient, List<PatientListIconInfo> patientListIconList, string hospitalID)
        {
            var nursingLevel = "NursingLevel";
            if (!string.IsNullOrEmpty(inPatient.NursingLevel))
            {
                nursingLevel += inPatient.NursingLevel;
            }
            var patientListIcon = patientListIconList.Where(m => m.IdentifyCategory == nursingLevel).FirstOrDefault();
            if (patientListIcon == null)
            {
                return null;
            }
            return CreateProfile(inPatient, nursingLevel, patientListIcon.IdentifyID.Value, "", hospitalID, DateTime.Now, DateTime.Now.TimeOfDay);
        }

        /// <summary>
        /// 创建Profile
        /// </summary>
        /// <param name="inPatient">患者基本信息</param>
        /// <param name="subSerialNumber"></param>
        /// <param name="assessListID"></param>
        /// <param name="assessValue"></param>
        /// <param name="hospitalID"></param>
        /// <param name="profileDate"></param>
        /// <param name="profileTime"></param>
        /// <returns></returns>
        private static PatientProfile CreateProfile(InpatientDataInfo inPatient
            , string subSerialNumber, int assessListID, string assessValue, string hospitalID, DateTime profileDate, TimeSpan profileTime)
        {
            var patientProfile = new PatientProfile
            {
                HospitalID = hospitalID,
                InpatientID = inPatient.ID,
                CaseNumber = inPatient.CaseNumber,
                ChartNo = inPatient.ChartNo,
                PatientID = inPatient.PatientID,
                ModelName = "HIS",
                Source = "I",
                ProfileDate = profileDate,
                ProfileTime = profileTime,
                AutoAddFlag = "",
                Note = "",
                ModifyPersonID = MODIFYPERSONID,
                SerialNumber = inPatient.CaseNumber + "_" + subSerialNumber,
                AssessListID = assessListID,
                AssessValue = assessValue
            };
            return patientProfile;
        }

        /// <summary>
        /// 写同步日志,及病人事件
        /// </summary>
        /// <param name="inpatientLogs">患者日志档</param>
        /// <param name="inpatient">患者信息</param>
        /// <param name="stationID">病区ID</param>
        /// <param name="DepartmentID">科室ID</param>
        /// <param name="bedID">床位ID</param>
        /// <param name="bedNumber">床位号</param>
        /// <param name="eventDateTime">事件发生时间</param>
        /// <param name="assessListID">事件对应ID</param>
        /// <returns></returns>
        private async Task SetInpatientLogAndEvent(List<InpatientLogInfo> inpatientLogs, InpatientDataInfo inpatient
            , int stationID, int DepartmentID, int bedID, string bedNumber, DateTime eventDateTime
            , int assessListID, List<EventSettingInfo> eventSettings)
        {
            var eventSetting = eventSettings.Where(m => m.AssessListID == assessListID)
                                           .Select(t => new { t.ShowName, t.LogCode }).FirstOrDefault();
            if (eventSetting == null)
            {
                _logger.Warn("未能获取到EventSetting中AssessListID={0}的配置！", assessListID);
                return;
            }

            if (assessListID == EVENTSETTING_ASSESSLISTID_DEATH)
            {
                var patientEvents = await _patientEventRepository.GetByInpatientID(inpatient.ID);
                var oldDeathEvent = patientEvents.Find(m => m.AssessListID == EVENTSETTING_ASSESSLISTID_DEATH);
                if (oldDeathEvent != null)
                {
                    if (oldDeathEvent.OccurDate.Add(oldDeathEvent.OccurTime) == eventDateTime || oldDeathEvent.ModifyPersonID != MODIFYPERSONID)
                    {
                        return;
                    }
                    await _patientEventCommonService.DeleteInpatientEventAndLogAsync(oldDeathEvent.InpatientID, oldDeathEvent.PatientEventID, _config.Value.HospitalID, _config.Value.Language, MODIFYPERSONID, oldDeathEvent);
                }
            }

            //判断记录是否存在
            inpatientLogs = inpatientLogs.Where(m => m.StationID == stationID && m.LogDateTime == eventDateTime
            && m.LogCode == eventSetting.LogCode).ToList();

            if (inpatientLogs.Count >= 1)
            {
                return;
            }
            //远程调用MedicalAPI中的保存患者事件的公共方法
            await _patientEventCommonService.CallEventAPI(inpatient, stationID, DepartmentID, bedID, bedNumber
                , eventDateTime, assessListID, eventSetting.LogCode, "", inpatient.ID + eventSetting.LogCode);
        }

        /// <summary>
        /// 处理病人正常出院院逻辑
        /// </summary>
        /// <param name="medicalInPatientData"></param>     
        /// <param name="inPatientData"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task<SyncInpatientDataView> SyncInpatientDischarge(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, List<EventSettingInfo> eventSettings)
        {
            var patientProfileList = new List<PatientProfile>();
            var inpatientChangeList = new List<InPatientChangeViewInfo>();
            var markViewList = new List<MarkView>();
            medicalInPatientData.DischargeDate = inPatientData.DischargeDateTime.HasValue ? inPatientData.DischargeDateTime.Value.Date : (DateTime?)null;//出院日期
            medicalInPatientData.DischargeTime = inPatientData.DischargeDateTime.HasValue ? inPatientData.DischargeDateTime.Value.TimeOfDay : (TimeSpan?)null;//出院时间
            if (!medicalInPatientData.InHospitalStatus.HasValue || medicalInPatientData.InHospitalStatus.Value < (int)PatientStatus.B)
            {
                medicalInPatientData.InHospitalStatus = inPatientData.InHospitalStatus;
            }
            //病人出院，更新Mark
            var markView = await DischargeToProfileMark(medicalInPatientData);
            markViewList.Add(markView);
            _logger.Info("出院病人信息：" + ListToJson.ToJson(medicalInPatientData));
            var dischargeDateTime = CreateDateTime(medicalInPatientData.DischargeDate, medicalInPatientData.DischargeTime);
            if (dischargeDateTime != null)
            {
                //写InpatientLog和PatientEvent
                await InsertInpatientEventInfo(medicalInPatientData, "Discharge", dischargeDateTime.Value, eventSettings);
            }
            //处理病人出院，呼叫信息
            var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientDischarged);
            inpatientChangeList.Add(inPatientChange);
            var syncInpatientDataView = new SyncInpatientDataView
            {
                RetureFlag = true,
                PatientProfileList = patientProfileList,
                InPatientChangeViewList = inpatientChangeList,
                MarkViewList = markViewList
            };
            return syncInpatientDataView;
        }

        /// <summary>
        /// 出院ProfileMark
        /// </summary>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private async Task<MarkView> DischargeToProfileMark(InpatientDataInfo inpatient)
        {
            Dictionary<string, string> dict = new()
            {
              { "chartNo", inpatient.ChartNo }
            };
            var dischargeProfileMarkAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "DischargeProfileMarkAPI");
            //dischargeProfileMarkAPI = "http://localhost:56194/api/ProfileMark/Discharge";
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = dischargeProfileMarkAPI,
                MarkDatas = markData,
                DataType = "出院"
            };
            return markView;
        }

        /// <summary>
        /// 生成DateTime
        /// </summary>
        /// <param name="dates"></param>
        /// <param name="times"></param>
        /// <returns></returns>
        private static DateTime? CreateDateTime(DateTime? dates, TimeSpan? times)
        {
            if (times.HasValue)
            {
                _logger.Info("转换时间:" + times.ToString());
                times = times.Value;
            }
            else
            {
                _logger.Info("时间为空");
            }

            if (dates.HasValue)
            {
                _logger.Info("转换日期" + dates.ToString());
                dates = dates.Value.Date;
                dates = dates.Value.Date.Add(times.Value);
                return dates.Value;
            }
            return null;
        }
        /// <summary>
        /// 插入患者事件 --写患者事件的同时，会写一笔InpatientLog日志到表中
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="logCode"></param>
        /// <param name="logDateTime"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task InsertInpatientEventInfo(InpatientDataInfo inpatient, string logCode, DateTime logDateTime
            , List<EventSettingInfo> eventSettings)
        {
            var setting = eventSettings.Find(m => m.LogCode == logCode);
            if (setting == null)
            {
                _logger.Warn("病人事件LogCode配置异常,LogCode:" + logCode + "请确认EventSetting是否已经配置");
                return;
            }
            var assessListIDs = new List<int>
            {
              EVENTSETTING_ASSESSLISTID_ADMISSION
                //出院事件逻辑暂时屏蔽
               , EVENTSETTING_ASSESSLISTID_DISCHARGE
                ,EVENTSETTING_ASSESSLISTID_DEATH
            };
            //判断出院事件是否存在
            if (assessListIDs.Contains(setting.AssessListID))
            {
                var patientEvents = await _patientEventRepository.GetEventByAssessListIDAsync(inpatient.ID, setting.AssessListID);
                foreach (var item in patientEvents)
                {
                    await _patientEventCommonService.DeleteInpatientEventAndLogAsync(item.InpatientID, item.PatientEventID, _config.Value.HospitalID, _config.Value.Language, MODIFYPERSONID);
                }

                //远程调用MedicalAPI中的保存患者事件的公共方法
                await _patientEventCommonService.CallEventAPI(inpatient, inpatient.StationID, inpatient.DepartmentListID, inpatient.BedID, inpatient.BedNumber
                    , logDateTime, setting.AssessListID, logCode, "", inpatient.ID + logCode);
            }
        }

        /// <summary>
        /// 修改病人信息
        /// </summary>
        /// <param name="medicalInPatientData"></param>
        /// <param name="inPatientData"></param>
        /// <param name="hospitalBaseDict"></param>
        /// <param name="patientListIconList"></param>
        /// <param name="patientID"></param>
        /// <param name="tableName"></param>
        /// <param name="eventSettings"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<Tuple<bool, List<PatientProfile>, List<MessageModel>>> UpdateInpatientData(InpatientDataInfo medicalInPatientData
            , InPatientDataView inPatientData, HospitalBaseDictView hospitalBaseDict
            , List<PatientListIconInfo> patientListIconList
            , string patientID, string tableName, List<EventSettingInfo> eventSettings
            , string hospitalID, int ageAssessListID)
        {
            var patientProfileList = new List<PatientProfile>();
            var upDataDBFlag = false;
            bool otherFlag = false;
            var messageModelList = new List<MessageModel>();

            if (hospitalBaseDict == null)
            {
                return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(false, null, null);
            }

            //从字典中获取数据,外层已经判断了是否有数据
            var departmentInfo = hospitalBaseDict.DepartmentList[0];
            var stationInfo = hospitalBaseDict.StationList[0];
            var bedInfo = hospitalBaseDict.BedList[0];
            var oldSataionID = medicalInPatientData.StationID;
            var oldDepartmentID = medicalInPatientData.DepartmentListID;
            var oldBedID = medicalInPatientData.BedID;
            var oldBedNumber = medicalInPatientData.BedNumber;
            //换床信息需要，取最近的患者事件的ID和Number
            if (oldBedID == 0 && oldBedNumber == "")
            {
                var eventBedInfo = await _patientEventRepository.GetLastPatientEvent(medicalInPatientData.ID);
                oldBedID = eventBedInfo.BedID;
                oldBedNumber = eventBedInfo.BedNumber;
            }
            var inpatientLog = await _inpatientLogRepository.GetByCaseNumberAsync(medicalInPatientData.CaseNumber);

            _logger.Info(tableName + " 更新住院病人CaseNumber[" + medicalInPatientData.CaseNumber + "]信息!");
            //先进行Mark处理,床位,科室发生变化
            if (medicalInPatientData.StationID != stationInfo.ID
               || medicalInPatientData.BedNumber != inPatientData.BedNumber)
            {
                var markView = await ModifyInpatientToProfileMark(medicalInPatientData, inPatientData);
                MarkViewList.Add(markView);

                _logger.Info(medicalInPatientData.CaseNumber + "|病区或床位发生变化，从stationID[" + medicalInPatientData.StationID + "]更新为[" + stationInfo.ID
                    + "],BedNumber[CCC:" + medicalInPatientData.BedNumber + "]->[HIS:" + inPatientData.BedNumber + "]"
                    , MODIFYPERSONID, false);

                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|病区或床位发生变化，从stationID[" + medicalInPatientData.StationID + "]更新为[" + stationInfo.ID
                    + "],BedNumber[CCC:" + medicalInPatientData.BedNumber + "]->[HIS:" + inPatientData.BedNumber + "]"
                    , MODIFYPERSONID, false);
            }
            if (medicalInPatientData.AdmissionDate != inPatientData.AdmissionDateTime.Date
             || medicalInPatientData.AdmissionTime != inPatientData.AdmissionDateTime.TimeOfDay)
            {
                var tempOldAdmissionDate = medicalInPatientData.AdmissionDate;
                var tempOldAdmissionTime = medicalInPatientData.AdmissionTime;
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "AdmissionDate"
                   , medicalInPatientData.CaseNumber + "|入院日期发生改变，从 [" + medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime) +
                   "]更新为[" + inPatientData.AdmissionDateTime
                   , MODIFYPERSONID, false);
                medicalInPatientData.AdmissionDate = inPatientData.AdmissionDateTime.Date;
                medicalInPatientData.AdmissionTime = inPatientData.AdmissionDateTime.TimeOfDay;
                //写入院事件
                await _patientEventCommonService.DelInpatientEvent(medicalInPatientData.ID, EVENTSETTING_ASSESSLISTID_ADMISSION, tempOldAdmissionDate, tempOldAdmissionTime, MODIFYPERSONID);

                await SetInpatientLogAndEvent(inpatientLog, medicalInPatientData, medicalInPatientData.StationID, medicalInPatientData.DepartmentListID
                     , medicalInPatientData.BedID, medicalInPatientData.BedNumber, medicalInPatientData.AdmissionDate.Add(medicalInPatientData.AdmissionTime), EVENTSETTING_ASSESSLISTID_ADMISSION, eventSettings);
            }
            if (medicalInPatientData.StationID != stationInfo.ID)
            {
                _logger.Info("病人CaseNumber[" + medicalInPatientData.CaseNumber + "|病区发生变化" +
                 "，从[" + medicalInPatientData.StationID + "]更新为[" + stationInfo + "]"
                 + "转科，停止护理问题");
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|病区发生变化，从[" + medicalInPatientData.StationID + "]更新为[" + stationInfo + "]"
                    , MODIFYPERSONID, false);
                //如果病区发生变化，停止护理问题
                var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientTransfer);
                InPatientChangeList.Add(inPatientChange);//存储病人变化信息
                medicalInPatientData.StationID = stationInfo.ID;
                //转科 ，护理流程码置空
                medicalInPatientData.NursingProcedureCode = "";

                var apiSettings = await _aPISettingRepository.GetAllAsync<APISettingInfo>();
                var url = apiSettings.Find(m => m.SettingCode == "MQSolutionServer")?.SettingValue;
                var path = apiSettings.Find(m => m.SettingCode == "PushMessage")?.SettingValue;
                // 发送患者转科消息
                var sendMsgView = new SendMessageView
                {
                    MessageType = MessageType.BROADCAST,
                    ExchangeName = "PatientTransfer",
                    Body = medicalInPatientData.ID
                };
                await HttpHelper.HttpPostAsync(url + path, ListToJson.ToJson(sendMsgView), "application/json");
            }

            if (medicalInPatientData.BedID != bedInfo.ID)
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "BedID变化,CCCbedID:" + medicalInPatientData.BedID + "需要更新的BedID:" + bedInfo.ID);
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|床位发生变化，BedID从[" + medicalInPatientData.BedID + "]更新为[" + bedInfo.ID + "]", MODIFYPERSONID, false);
                medicalInPatientData.BedID = bedInfo.ID;
                otherFlag = true;
            }
            if (medicalInPatientData.BedNumber != (inPatientData.BedNumber ?? ""))
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + " BedNumber变化,CCCBedNumber:" + medicalInPatientData.BedNumber + "需要更新的BedNumber:" + inPatientData.BedNumber ?? "");
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                   , medicalInPatientData.CaseNumber + "|床位发生变化，BedNumber从[" + medicalInPatientData.BedNumber + "]更新为[" + inPatientData.BedNumber ?? "" + "]"
                   , MODIFYPERSONID, false);
                medicalInPatientData.BedNumber = inPatientData.BedNumber ?? "";
                otherFlag = true;
            }
            if (medicalInPatientData.DepartmentListID != departmentInfo.ID)
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "DepartmentListID变化");
                medicalInPatientData.DepartmentListID = departmentInfo.ID;
                //转科 ，护理流程码置空
                //medicalInPatientData.NursingProcedureCode = "";
                otherFlag = true;
            }

            if (medicalInPatientData.ChartNo != (inPatientData.ChartNo ?? ""))
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "ChartNo变化");
                medicalInPatientData.ChartNo = inPatientData.ChartNo ?? "";
                otherFlag = true;
            }
            if (medicalInPatientData.LocalCaseNumber != (inPatientData.ChartNo ?? ""))
            {
                _logger.Info("CaseNumber:" + medicalInPatientData.CaseNumber + "LocalCaseNumber变化");
                medicalInPatientData.LocalCaseNumber = inPatientData.ChartNo ?? "";
                otherFlag = true;
            }
            if (medicalInPatientData.PatientID != (patientID ?? ""))
            {
                _logger.Info("PatientID变化");
                medicalInPatientData.PatientID = patientID ?? "";
                otherFlag = true;
            }

            if (medicalInPatientData.ICUFlag != (bedInfo.ICUFlag ?? ""))
            {
                _logger.Info("ICUFlag变化");
                medicalInPatientData.ICUFlag = bedInfo.ICUFlag ?? "";
                otherFlag = true;
            }
            //病人转科后，会将其状态置成50，转科后更新病人信息会调用该方法，所以在此处将不是“在科”的病人状态重置为30
            if (medicalInPatientData.InHospitalStatus != (int)PatientStatus.I)
            {
                medicalInPatientData.InHospitalStatus = inPatientData.InHospitalStatus;
            }
            var iCDCode = (medicalInPatientData.ICDCode ?? "").Trim();
            //病人入院后，如果诊断编码为空，才进行诊断更新，说明可能没有从诊断接口获取诊断数据
            //如果ICDCode存在，说明已经从诊断接口获得了数据，以诊断接口的数据为准
            if (medicalInPatientData.Diagnosis != (inPatientData.Diagnosis ?? "") && string.IsNullOrEmpty(iCDCode))
            {
                medicalInPatientData.Diagnosis = inPatientData.Diagnosis ?? "";
                otherFlag = true;
            }

            if (medicalInPatientData.BillingPattern != (inPatientData.BillingPattern ?? ""))
            {
                medicalInPatientData.BillingPattern = inPatientData.BillingPattern ?? "";
                otherFlag = true;
            }
            if (medicalInPatientData.Age != inPatientData.Age)
            {
                medicalInPatientData.Age = inPatientData.Age;
                patientProfileList.Add(_externalCommonService.CreateProfile(medicalInPatientData, "Age", ageAssessListID
                , inPatientData.Age.ToString(), hospitalID, MODIFYPERSONID));
                otherFlag = true;
            }

            if (medicalInPatientData.AgeDetail != (inPatientData.AgeDetail ?? ""))
            {
                medicalInPatientData.AgeDetail = inPatientData.AgeDetail;
                otherFlag = true;
            }

            if (!string.IsNullOrEmpty(inPatientData.AttendingPhysicianID) && medicalInPatientData.AttendingPhysicianID != inPatientData.AttendingPhysicianID)
            {
                medicalInPatientData.AttendingPhysicianID = inPatientData.AttendingPhysicianID;
                otherFlag = true;
            }
            //护理级别进行转换
            if ((medicalInPatientData.NursingLevel ?? "") != (inPatientData.NursingLevel ?? ""))
            {
                var oldNursingLevel = medicalInPatientData.NursingLevel;
                _logger.Info("NursingLevel"
                 , medicalInPatientData.CaseNumber + "|护理级别发生变化，从[" + medicalInPatientData.NursingLevel + "]更新为[" + inPatientData.NursingLevel + "]"
                 , MODIFYPERSONID, false);
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "NursingLevel"
                    , medicalInPatientData.CaseNumber + "|护理级别发生变化，从[" + medicalInPatientData.NursingLevel + "]更新为[" + inPatientData.NursingLevel + "]"
                    , MODIFYPERSONID, false);
                medicalInPatientData.NursingLevel = inPatientData.NursingLevel ?? "";
                //增加PatientProfiles数据(护理级别变化)
                patientProfileList.Add(CreateNursingLeveProfile(medicalInPatientData, patientListIconList, hospitalID));
                //如果护理级别发生变化，停止护理问题
                _logger.Info("病人CaseNumber[" + medicalInPatientData.CaseNumber + "]" + "护理级别发生变化，停止护理问题API");
                var inPatientChange = InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel ?? "", PatientType.PatientLevelChange);
                InPatientChangeList.Add(inPatientChange);//存储病人变化信息                    
                await UpdateNursingLevelOfAdminssionAssessAsync(medicalInPatientData.ID, inPatientData.NursingLevel ?? "", DateTime.Now, medicalInPatientData.CaseNumber, InPatientChangeList);
            }
            //处理，出院后，又不出的问题
            if ((!inPatientData.DischargeDateTime.HasValue && medicalInPatientData.DischargeDate.HasValue) || (inPatientData.InHospitalStatus < medicalInPatientData.InHospitalStatus && medicalInPatientData.InHospitalStatus >= (int)PatientStatus.B))
            {
                var tempOldDischargeDate = medicalInPatientData.DischargeDate;
                var tempOldDischargeTime = medicalInPatientData.DischargeTime;
                _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "AdmissionDate"
                   , medicalInPatientData.CaseNumber + "|患者取消出院，出院时间 [" + medicalInPatientData.DischargeDate.Value.Add(medicalInPatientData.DischargeTime.Value) +
                   "]更新为未出院状态,出院事件删除"
                   , MODIFYPERSONID, false);
                //患者撤销出院院,删除出院事件
                await _patientEventCommonService.DelInpatientEvent(medicalInPatientData.ID, EVENTSETTING_ASSESSLISTID_DISCHARGE, tempOldDischargeDate.Value, tempOldDischargeTime.Value, MODIFYPERSONID);
                medicalInPatientData.DischargeDate = null;
                medicalInPatientData.DischargeTime = null;
                medicalInPatientData.EMRArchivingFlag = "";
            }
            //确认数据是否发生改变
            upDataDBFlag = _medicalDbContext.Entry(medicalInPatientData).State != Microsoft.EntityFrameworkCore.EntityState.Unchanged;
            if (!upDataDBFlag)
            {
                return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(false, null, null);
            }

            medicalInPatientData.ModifyPersonID = MODIFYPERSONID;
            medicalInPatientData.ModifyDate = DateTime.Now;
            _logger.Info("更新在院病人数据:ChartNo" + medicalInPatientData.ChartNo);

            if (otherFlag)
            {
                InPatientChangeList.Add(InPatientChangeView(medicalInPatientData.ID, medicalInPatientData.CaseNumber, medicalInPatientData.NursingLevel, PatientType.PatientOtherChange)); //存储更新的病人信息
            }
            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "UpdateInpatientData"
                    , medicalInPatientData.CaseNumber + "|更新病人信息"
                    , MODIFYPERSONID, false);
            return new Tuple<bool, List<PatientProfile>, List<MessageModel>>(upDataDBFlag, patientProfileList, messageModelList);
        }

        /// <summary>
        /// 转科转床,Mark数据组装
        /// </summary>
        /// <param name="inpatient"></param>
        /// <param name="hisInpatient"></param>
        /// <returns></returns>
        private async Task<MarkView> ModifyInpatientToProfileMark(InpatientDataInfo inpatient
            , InPatientDataView hisInpatient)
        {
            var hospitalID = _config.Value.HospitalID;
            Dictionary<string, string> dict = new Dictionary<string, string>
            {
              { "chartNo", inpatient.ChartNo },
              { "nursingLevel", inpatient.NursingLevel },
              { "bedNumber", hisInpatient.BedNumber },
              { "stationCode", hisInpatient.StationCode },
              { "departmenCode", hisInpatient.DepartmentCode },
              { "hospitalID", hospitalID }
            };

            //从配置当中获取数据
            var transferProfileMarkAPI = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "TransferProfileMarkAPI");
            //transferProfileMarkAPI = "http://localhost:56194/api/ProfileMark/Transfer";
            var markData = GetKeyValue(dict);
            var markView = new MarkView
            {
                InpatientData = inpatient,
                Url = transferProfileMarkAPI,
                MarkDatas = markData,
                DataType = "转科、转床、护理级别发生变化"
            };
            return markView;
        }

        /// <summary>
        /// Mark数据呼叫API
        /// </summary>
        /// <param name="markViews"></param>
        private async Task CallMarkAPI(List<MarkView> markViews)
        {
            if (markViews == null || markViews.Count <= 0)
            {
                return;
            }
            foreach (var markView in markViews)
            {
                _logger.Info("异动Mark,CaseNumber=" + markView.InpatientData.CaseNumber + " 异动类型DataType:" + markView.DataType);
                if (markView.Url == "")
                {
                    _logger.Warn("呼叫Mark，API为空,数据类型：" + markView.DataType);
                    return;
                }

                var url = markView.Url + "?" + markView.MarkDatas;
                _logger.Info("更新MarkUrl:" + url);
                try
                {
                    //写同步日志
                    var syncLog = _commonHelper.SaveLog(url,
                        "", markView.InpatientData.ID, markView.InpatientData.CaseNumber, _unitOfWork, true, true);
                    WebRequestSugar wrs = new WebRequestSugar();
                    string result = await wrs.SendObjectAsJsonInBody(url, null);
                    //更新同步日志状态
                    _commonHelper.GetAPIExecResult(result, syncLog, _unitOfWork);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "写ProfileMark失败，API" + url);
                    return;
                }
            }
        }

        /// <summary>
        /// 病人信息变化，调用相应的APi进行处理
        /// </summary>
        /// <param name="inPatientChangeList"></param>
        /// <returns></returns>
        private async Task CheckInPatient(List<InPatientChangeViewInfo> inPatientChangeList)
        {
            foreach (var item in inPatientChangeList)
            {
                switch (item.PatientType)
                {
                    case PatientType.PatientInsert:
                    case PatientType.PatientOtherChange:
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, false); //病人信息发生变化，验证排程中的病人信息（延迟5分钟执行）
                        break;

                    case PatientType.PatientLevelChange:
                        await _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.NursingLevelChange);
                        break;

                    case PatientType.PatientTransfer:
                        await _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Transfer);
                        _commonHelper.CheckSchedulePatientInfo(item.InPatientID, item.CaseNumber, true);
                        break;

                    case PatientType.PatientDischarged:
                        await _commonHelper.StopProblem(item.InPatientID, item.NursingLevel, true, MedicalEnumUtility.StopProblemType.Discharge);
                        break;

                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 同步患者主诉信息
        /// </summary>
        /// <param name="stringKeyValue">主诉信息</param>
        /// <returns></returns>
        public async Task<bool> SyncInpatientChiefComplaintAsync(StringKeyValueView stringKeyValue)
        {
            var inpatient = await _inpatientDataRepository.GetByCaseNumberAsync(stringKeyValue.Key);
            if (inpatient == null)
            {
                _logger.Warn($"未能获取到患者信息CaseNumber={stringKeyValue.Key}");
                return false;
            }
            inpatient.ChiefComplaint = stringKeyValue.Value;
            inpatient.Modify(MODIFYPERSONID);
            return _unitOfWork.SaveChanges() >= 0;
        }

        /// <summary>
        /// 同步出院患者信息
        /// </summary>
        /// <param name="inpatientList"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> SyncDischargeInpatientData(List<InPatientDataView> inpatientList)
        {
            var hospitalID = _config.Value.HospitalID;
            _logger.Info("同步出院病人[" + inpatientList.Count + "]人");
            var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
            var caseNumberArray = inpatientList.Select(m => m.CaseNumber).ToArray();
            //获取当前同步出院病人的患者事件日志集合
            var inpatientLogs = await _inpatientLogRepository.GetByCaseNumbersAsync(caseNumberArray);
            var caseNumbers = inpatientList.Select(m => m.CaseNumber).Distinct().ToList();
            var medicalInpatientList = await _inpatientDataRepository.GetAllInpatientListByCaseNumberListAsync(caseNumbers);
            //获得病人标签
            foreach (var item in inpatientList) //记录作业正在执行日志
            {
                _logger.Info("开始同步住院号为:" + item.CaseNumber + "的患者出院信息");
                var inpatientDataInfo = medicalInpatientList.Where(m => m.CaseNumber == item.CaseNumber).FirstOrDefault();
                if (inpatientDataInfo == null)
                {
                    continue;
                }
                if (inpatientDataInfo.LocalCaseNumber != inpatientDataInfo.ChartNo)
                {
                    inpatientDataInfo.LocalCaseNumber = inpatientDataInfo.ChartNo;
                }
                //添加判断出院事件是否存在，存在患者出院接口重事件还没有的情况
                if (inpatientDataInfo.InHospitalStatus <= (int)PatientStatus.OUT || inpatientDataInfo.DischargeDate == null)
                {
                    var syncInpatientDataView = await SyncInpatientDischarge(inpatientDataInfo, item, eventSettings);
                    //异动Mark
                    await CallMarkAPI(syncInpatientDataView.MarkViewList);
                    await CheckInPatient(syncInpatientDataView.InPatientChangeViewList);
                }
                //筛出当前病人的患者事件日志
                var ipLogs = inpatientLogs.Where(m => m.CaseNumber == item.CaseNumber).ToList();
                //出院事件逻辑先暂停,先走医嘱
                await SyncInpatientDischargeEvent(ipLogs, item, inpatientDataInfo, eventSettings);
                try
                {
                    _unitOfWork.SaveChanges();
                }
                catch (Exception ex)
                {
                    _logger.Error("SyncDischargedPatient病人CaseNumber" + item.CaseNumber + "出院数据同步失败||" + ex.ToString());
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 出院,更新病人事件
        /// </summary>
        /// <param name="inpatientLogs"></param>
        /// <param name="item"></param>
        /// <param name="inpatientDataInfo"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task<bool> SyncInpatientDischargeEvent(List<InpatientLogInfo> inpatientLogs, InPatientDataView item, InpatientDataInfo inpatientDataInfo, List<EventSettingInfo> eventSettings)
        {
            _logger.Info("同步出院病人 CaseNumber:" + item.CaseNumber + " ChartNo:" + item.ChartNo);

            //如果有出院事件，或者死亡事件不再增加
            var assessListIds = new List<int>
            {
             //出院事件逻辑先暂停,先走医嘱
             EVENTSETTING_ASSESSLISTID_DISCHARGE,
             EVENTSETTING_ASSESSLISTID_DEATH
            };
            var patientEvent = await _patientEventRepository.GetEventByAssessListIDs(inpatientDataInfo.ID, assessListIds);

            if (patientEvent != null && patientEvent.Count > 0)
            {
                var oldEvent = patientEvent.Find(m => m.OccurDate == item.DischargeDateTime.Value.Date && m.OccurTime == item.DischargeDateTime.Value.TimeOfDay);
                if (oldEvent == null)
                {
                    patientEvent.ForEach(m =>
                    {
                        if (m.AssessListID == EVENTSETTING_ASSESSLISTID_DISCHARGE)
                        {
                            m.DeleteFlag = "*";
                            m.ModifyDate = DateTime.Now;
                        }
                    });
                }
            }
            if (item.DeathTime.HasValue)
            {
                //处理病人事件
                await SetInpatientLogAndEvent(inpatientLogs, inpatientDataInfo, inpatientDataInfo.StationID, inpatientDataInfo.DepartmentListID
                           , inpatientDataInfo.BedID
                          , inpatientDataInfo.BedNumber, item.DeathTime.Value, EVENTSETTING_ASSESSLISTID_DEATH, eventSettings);
            }
            else
            {
                //处理病人事件
                await SetInpatientLogAndEvent(inpatientLogs, inpatientDataInfo, inpatientDataInfo.StationID, inpatientDataInfo.DepartmentListID
                           , inpatientDataInfo.BedID
                          , inpatientDataInfo.BedNumber, item.DischargeDateTime.Value, EVENTSETTING_ASSESSLISTID_DISCHARGE, eventSettings);
            }
            return true;
        }

        /// <summary>
        /// 同步无费退院患者信息
        /// </summary>
        /// <param name="inpatientListView"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> SyncNoFeeDischargeInpatientData(List<InPatientDataView> inpatientListViews)
        {
            if (inpatientListViews == null || inpatientListViews.Count <= 0)
            {
                _logger.Warn("删除患者信息失败");
                return false;
            }
            var caseNumberList = inpatientListViews.Select(m => m.CaseNumber).ToList();
            var inpatientDataList = await _inpatientDataRepository.GetInpatientDataByCaseNumberListAsync(caseNumberList);
            if (inpatientDataList.Count <= 0)
            {
                return true;
            }
            foreach (var item in inpatientDataList)
            {
                item.Delete(MODIFYPERSONID);
                item.InHospitalStatus = item.InHospitalStatus;
                var eventLog = new EventLogInfo
                {
                    CID = item.CaseNumber,
                    Content = "取消入院患者信息删除，DeleteFlag->*",
                    EventType = EnumUtility.EventType.InPatientDataSync.ToString("d"),
                    Target = "InpatientData",
                    CreateTime = DateTime.Now
                };
                eventLog.ID = eventLog.GetId();
                await _unitOfWork.GetRepository<EventLogInfo>().InsertAsync(eventLog);

                _unitOfWork.ExecuteSqlCommand("EXEC [dbo].[P_Delete_PatientCancelAdmissionData_ByInpatientID] {0} ", item.ID);
            }
            try
            {
                return _unitOfWork.SaveChanges() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步保存无费退院数据异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 同步住院转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        public async Task<bool> SyncInpatientTransferDeptData(InpatientTransferDataView transferData)
        {
            if (transferData == null)
            {
                _logger.Warn("转科数据视图为空");
                return false;
            }
            try
            {
                var eventSettings = await _eventSettingRepository.GetAllAsync<EventSettingInfo>();
                var inpatient = await _inpatientDataRepository.GetInPatientData(transferData.InpatientID);
                var stationList = await _stationListRepository.GetAllAsync();
                if (inpatient == null)
                {
                    _logger.Warn($"未找到住院信息，住院ID：{transferData.InpatientID}");
                    return false;
                }
                // 处理转出事件
                if (transferData.inpatientTransferOutDataView != null)
                {
                    await TransferOutData(transferData, stationList, inpatient, eventSettings);
                }
                // 处理转入事件
                if (transferData.inpatientTransferInDataView != null)
                {
                    await TransferInData(transferData, stationList, inpatient, eventSettings);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步转科数据异常：{ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 处理转出相关事件
        /// </summary>
        /// <param name="transferData"></param>
        /// <param name="stationList"></param>
        /// <param name="inpatient"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task TransferOutData(InpatientTransferDataView transferData, List<StationListInfo> stationList, InpatientDataInfo inpatient, List<EventSettingInfo> eventSettings)
        {
            var outView = transferData.inpatientTransferOutDataView;
            var eventTypes = new List<int>
                    {
                      EVENTSETTING_ASSESSLISTID_TRANSOUT,
                      EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT,
                      EVENTSETTING_ASSESSLISTID_TURNOUTBED
                    };
            if (stationList.Any(m => m.ID == outView.TransferOutStationID && m.ICUFlag == "1"))
            {
                eventTypes.Add(EVENTSETTING_ASSESSLISTID_TRANSOUT_ICU);
            }
            foreach (var eventType in eventTypes)
            {
                await ProcessTransferEvent(
                    transferData,
                    eventType,
                    outView.TransferOutDatatime,
                    outView.TransferOutStationID,
                    outView.TransferOutDepartmentID,
                    outView.TransferOutBedID,
                    outView.TransferOutBedNumber,
                    inpatient,
                    eventSettings
                );
            }
        }
        /// <summary>
        /// 处理转入相关事件
        /// </summary>
        /// <param name="transferData"></param>
        /// <param name="stationList"></param>
        /// <param name="inpatient"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task TransferInData(InpatientTransferDataView transferData, List<StationListInfo> stationList, InpatientDataInfo inpatient, List<EventSettingInfo> eventSettings)
        {
            var inView = transferData.inpatientTransferInDataView;
            var eventTypes = new List<int>
                    {
                     EVENTSETTING_ASSESSLISTID_TRANSIN,
                     EVENTSETTING_ASSESSLISTID_TRANSINDEPT,
                     EVENTSETTING_ASSESSLISTID_TURNINBED
                    };
            if (stationList.Any(m => m.ID == inView.TransferInStationID && m.ICUFlag == "1"))
            {
                eventTypes.Add(EVENTSETTING_ASSESSLISTID_TRANSIN_ICU);
            }
            foreach (var eventType in eventTypes)
            {
                await ProcessTransferEvent(
                    transferData,
                    eventType,
                    inView.TransferInDatatime,
                    inView.TransferInStationID,
                    inView.TransferInDepartmentID,
                    inView.TransferInBedID,
                    inView.TransferInBedNumber,
                    inpatient,
                    eventSettings
                );
            }
        }
        /// <summary>
        /// transfer事件处理
        /// </summary>
        /// <param name="transferDataView"></param>
        /// <param name="assessListID"></param>
        /// <param name="eventDateTime"></param>
        /// <param name="stationID"></param>
        /// <param name="departmentID"></param>
        /// <param name="bedID"></param>
        /// <param name="bedNumber"></param>
        /// <param name="inpatient"></param>
        /// <param name="eventSettings"></param>
        /// <returns></returns>
        private async Task ProcessTransferEvent(
            InpatientTransferDataView transferDataView,
            int assessListID,
            DateTime eventDateTime,
            int stationID,
            int departmentID,
            int bedID,
            string bedNumber,
            InpatientDataInfo inpatient,
            List<EventSettingInfo> eventSettings)
        {
            var eventSetting = eventSettings
                .FirstOrDefault(m => m.AssessListID == assessListID);

            if (eventSetting == null)
            {
                _logger.Warn("未能获取到EventSetting中AssessListID={0}的配置！", assessListID);
                return;
            }
            var patientEvent = await _patientEventRepository.GetPatientEventByIDAndDateTime(transferDataView.InpatientID, assessListID, eventDateTime.Date, eventDateTime.AddMinutes(-20).TimeOfDay, eventDateTime.AddMinutes(20).TimeOfDay);
            if (patientEvent == null)
            {
                await _patientEventCommonService.CallEventAPI(
                    inpatient,
                    stationID,
                    departmentID,
                    bedID,
                    bedNumber,
                    eventDateTime,
                    assessListID,
                    eventSetting.LogCode,
                    "",
                    inpatient.ID + eventSetting.LogCode
                );
            }
            else
            {
                await _patientEventCommonService.CallEventAPI(
                    inpatient,
                    stationID,
                    departmentID,
                    bedID,
                    bedNumber,
                    eventDateTime,
                    assessListID,
                    eventSetting.LogCode,
                    "",
                    inpatient.ID + eventSetting.LogCode,
                    patientEvent.PatientEventID
                );
            }
        }

        /// <summary>
        /// 同步住院转科取消数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        public async Task<bool> SyncCancelInpatientTransferDeptData(InpatientTransferDataView inpatientTransferDataView)
        {
            if (inpatientTransferDataView == null) return false;

            var assessListOutIDs = new List<int>
            {
                EVENTSETTING_ASSESSLISTID_TRANSOUT,
                EVENTSETTING_ASSESSLISTID_TRANSOUTDEPT,
                EVENTSETTING_ASSESSLISTID_TURNOUTBED,
                EVENTSETTING_ASSESSLISTID_TRANSOUT_ICU
            };
            var assessListInIDs = new List<int>
            {
                EVENTSETTING_ASSESSLISTID_TRANSIN,
                EVENTSETTING_ASSESSLISTID_TRANSINDEPT,
                EVENTSETTING_ASSESSLISTID_TURNINBED,
                EVENTSETTING_ASSESSLISTID_TRANSIN_ICU
            };

            // 转科
            if (inpatientTransferDataView.inpatientTransferOutDataView != null)
            {
                var patientEventOutList = await _patientEventRepository.GetPatientEventByIDAndEventsAndDateTime(
                    inpatientTransferDataView.InpatientID,
                    EVENTSETTING_ASSESSLISTID_OUT,
                    inpatientTransferDataView.inpatientTransferOutDataView.TransferOutDatatime.Date,
                    inpatientTransferDataView.inpatientTransferOutDataView.TransferOutDatatime.TimeOfDay
                );

                // 使用字典提高查找效率
                var patientOutEventDict = patientEventOutList.ToDictionary(m => m.AssessListID);

                foreach (var assessListID in assessListOutIDs)
                {
                    if (patientOutEventDict.TryGetValue(assessListID, out var patientOutEvent))
                    {
                        await _patientEventCommonService.DeleteInpatientEventAndLogAsync(
                            patientOutEvent.InpatientID,
                            patientOutEvent.PatientEventID,
                            _config.Value.HospitalID,
                            _config.Value.Language,
                            MODIFYPERSONID
                        );
                    }
                }
            }

            // 转入数据
            if (inpatientTransferDataView.inpatientTransferInDataView != null)
            {
                var patientEventInList = await _patientEventRepository.GetPatientEventByIDAndEventsAndDateTime(
                    inpatientTransferDataView.InpatientID,
                    EVENTSETTING_ASSESSLISTID_IN,
                    inpatientTransferDataView.inpatientTransferInDataView.TransferInDatatime.Date,
                    inpatientTransferDataView.inpatientTransferInDataView.TransferInDatatime.TimeOfDay
                );

                // 使用字典提高查找效率
                var patientInEventDict = patientEventInList.ToDictionary(m => m.AssessListID);

                foreach (var assessListID in assessListInIDs)
                {
                    if (patientInEventDict.TryGetValue(assessListID, out var patientInEvent))
                    {
                        await _patientEventCommonService.DeleteInpatientEventAndLogAsync(
                            patientInEvent.InpatientID,
                            patientInEvent.PatientEventID,
                            _config.Value.HospitalID,
                            _config.Value.Language,
                            MODIFYPERSONID
                        );
                    }
                }
            }

            return true;
        }

        #region 同步新生儿数据

        /// <summary>
        /// 同步新生儿数据
        /// </summary>
        /// <param name="newBornRecordViews"></param>
        /// <returns></returns>
        public async Task<bool> SyncNewBornData(List<NewBornRecordView> newBornRecordViews)
        {
            var hospitalID = _config.Value.HospitalID;
            try
            {
                foreach (var view in newBornRecordViews)
                {
                    (InpatientDataInfo motherInpatient, InpatientDataInfo bornInpatient, NewBornRecordInfo newBornRecord) = await GetNewBornRecord(hospitalID, view);
                    if (motherInpatient == null || bornInpatient == null || newBornRecord == null)
                    {
                        _logger.Warn($"新生儿记录或母亲住院信息获取失败, CaseNumber: {view.CaseNumber}, ParentCaseNumber: {view.ParentCaseNumber}");
                        continue;
                    }

                    // 1. 判断是否需要同步分娩信息
                    bool needSyncDelivery = string.IsNullOrEmpty(newBornRecord.PatientDeliveryCareMainID)
                        || (await _patientDeliveryCareMainRepository.GetByID(newBornRecord.PatientDeliveryCareMainID)) == null;

                    if (!needSyncDelivery)
                    {
                        continue;
                    }
                    // 2. 添加新生儿分娩护理新生儿记录 
                    await AddNewBornDeliveryCareMain(newBornRecord, motherInpatient, bornInpatient);

                    try
                    {
                        if (_unitOfWork.SaveChanges() > 0)
                        {
                            var bornTime = newBornRecord.DeliveryTime ?? DateTime.Now;
                            await _patientEventCommonService.CallEventAPI(
                                motherInpatient,
                                motherInpatient.StationID,
                                motherInpatient.DepartmentListID,
                                motherInpatient.BedID,
                                motherInpatient.BedNumber,
                                bornTime,
                                2877,
                                "Delivery",
                                "",
                                motherInpatient.ID + "_Delivery"
                            );
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"新生儿信息同步失败, 错误信息: {ex.Message}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"同步新生儿数据失败, 错误信息: {ex}", ex);
                return false;
            }

            return true;
        }
        /// <summary>
        /// 获取新生儿记录和母亲住院信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task<(InpatientDataInfo motherInpatient, InpatientDataInfo bornInpatient, NewBornRecordInfo newBornRecord)> GetNewBornRecord(string hospitalID, NewBornRecordView newBornRecordView)
        {
            // 1. 获取新生儿住院信息
            var babyInpatient = await _inpatientDataRepository.GetByCaseNumberAsync(newBornRecordView.CaseNumber);
            if (babyInpatient == null)
            {
                _logger.Info($"新生儿住院信息不存在, CaseNumber: {newBornRecordView.CaseNumber}");
                return (null, null, null);
            }

            // 2. 获取母亲住院信息
            var motherInpatient = await _inpatientDataRepository.GetByCaseNumberAsync(newBornRecordView.ParentCaseNumber);
            if (motherInpatient == null)
            {
                _logger.Info($"母亲住院信息不存在, CaseNumber: {newBornRecordView.ParentCaseNumber}, 新生儿信息: {newBornRecordView.CaseNumber}");
                return (null, null, null);
            }

            // 3. 获取或创建新生儿档案
            var newBornRecord = await _newBornRecordRepository.GetDataByCasenumber(newBornRecordView.CaseNumber);
            if (newBornRecord == null)
            {
                newBornRecord = CreateNewBornRecord(newBornRecordView, motherInpatient.ID);
                if (newBornRecord == null)
                {
                    return (null, null, null);
                }
                // 新增新生儿Profile
                await CreatePatientProfile(babyInpatient, newBornRecord, newBornRecordView, hospitalID);
            }
            else
            {
                newBornRecord = await UpdateNewBornRecord(babyInpatient, newBornRecord, newBornRecordView, hospitalID);
                if (newBornRecord == null)
                {
                    return (null, null, null);
                }

            }
            return (motherInpatient, babyInpatient, newBornRecord);
        }

        /// <summary>
        /// 更新新生儿记录
        /// </summary>
        /// <param name="newBornData"></param>
        /// <param name="newBornRecordView"></param>
        /// <returns></returns>
        private async Task<NewBornRecordInfo> UpdateNewBornRecord(InpatientDataInfo babyInpatient, NewBornRecordInfo newBornData, NewBornRecordView newBornRecordView, string hospitalID)
        {
            var profiles = new List<PatientProfile>();
            if (newBornData.AddEmployeeID != newBornRecordView.RecordPersonID)
            {
                newBornData.AddEmployeeID = newBornRecordView.RecordPersonID;
            }
            if (newBornData.ModifyPersonID != newBornRecordView.RecordPersonID)
            {
                newBornData.ModifyPersonID = newBornRecordView.RecordPersonID;
                newBornData.ModifyDate = DateTime.Now;
            }
            decimal? length = TryParseDecimal(newBornRecordView.Height, out decimal babyLength) ? babyLength : null;
            if (newBornData.Length != length)
            {
                newBornData.Length = length;
                //身高
                profiles.Add(CreateProfile(babyInpatient, newBornData.NewBornID, 1954, Convert.ToString(newBornData.Length), hospitalID, babyInpatient.AdmissionDate, babyInpatient.AdmissionTime));
                //身长
                profiles.Add(CreateProfile(babyInpatient, newBornData.NewBornID, 1107, Convert.ToString(newBornData.Length), hospitalID, babyInpatient.AdmissionDate, babyInpatient.AdmissionTime));
            }
            decimal? weightKG = TryParseDecimal(newBornRecordView.Weight, out decimal babyWeightKG) ? babyWeightKG : null;
            if (newBornData.WeightKG != weightKG)
            {
                newBornData.WeightKG = weightKG;
                //体重kg
                profiles.Add(CreateProfile(babyInpatient, newBornData.NewBornID, 1340, Convert.ToString(newBornData.WeightKG), hospitalID, babyInpatient.AdmissionDate, babyInpatient.AdmissionTime));
            }

            int? weight = TryParseDecimal(newBornRecordView.Weight, out decimal babyWeight) ? (int)Math.Round(babyWeight * 1000) : (int?)null;
            if (newBornData.Weight != weight)
            {
                newBornData.Weight = weight;
            }
            int result = _unitOfWork.SaveChanges();
            if (result < 0)
            {
                _logger.Error("更新新生儿记录失败");
                return null;
            }

            if (profiles.Count > 0)
                await _commonHelper.AddProfile(profiles);

            return newBornData;
        }

        /// <summary>
        /// 添加新生儿数据
        /// </summary>
        /// <param name="newBorn"></param>
        /// <param name="parentInfo"></param>
        /// <param name="bornInpatient"></param>
        /// <returns></returns>
        private async Task AddNewBornDeliveryCareMain(NewBornRecordInfo newBorn, InpatientDataInfo parentInfo, InpatientDataInfo bornInpatient)
        {
            if (parentInfo == null)
            {
                return;
            }
            if (bornInpatient == null)
            {
                return;
            }
            var deliveryRecord = await _patientDeliveryRecordRepository.GetLastByInpatientID(parentInfo.ID);
            if (deliveryRecord == null)
            {
                return;
            }
            newBorn.PatientDeliveryRecordID = deliveryRecord.PatientDeliveryRecordID;
            var shiftView = await _stationShiftCommonService.GetNowStationShiftData(parentInfo.StationID);
            var nowTime = DateTime.Now;
            var count = await _patientDeliveryCareMainRepository.GetNumberOfAssessment(parentInfo.ID, "DeliveryNewbornStart");
            var lastAssessMain = await _assessMainRepository.GetLastAssessID(parentInfo.ID, nowTime);

            var careMain = new PatientDeliveryCareMainInfo
            {
                PatientDeliveryRecordID = deliveryRecord.PatientDeliveryRecordID,
                InpatientID = bornInpatient.ID,
                PatientID = bornInpatient.PatientID,
                CaseNumber = newBorn.Casenumber,
                ChartNo = newBorn.ChartNO,
                StationID = parentInfo.StationID,
                DepartmentListID = parentInfo.DepartmentListID,
                BedID = bornInpatient.BedID,
                BedNumber = bornInpatient.BedNumber,
                AssessDate = newBorn.DeliveryTime.HasValue ? newBorn.DeliveryTime.Value.Date : nowTime.Date,
                AssessTime = newBorn.DeliveryTime.HasValue ? newBorn.DeliveryTime.Value.TimeOfDay : nowTime.TimeOfDay,
                NumberOfAssessment = count++,
                ShiftDate = shiftView.ShiftDate,
                Shift = shiftView.NowShift.Shift,
                RecordsCode = "DeliveryNewbornStart",
                NursingLevel = bornInpatient.NursingLevel,
                ClientType = "1",
                AddDate = nowTime,
                ModifyDate = nowTime,
                AddEmployeeID = newBorn.AddEmployeeID,
                ModifyPersonID = newBorn.AddEmployeeID,
                DeleteFlag = "",
                InformPhysician = false,
                BringToNursingRecord = false,
                PatientAssessMainID = lastAssessMain
            };

            careMain.PatientDeliveryCareMainID = careMain.GetId();
            _unitOfWork.GetRepository<PatientDeliveryCareMainInfo>().Insert(careMain);
            newBorn.PatientDeliveryCareMainID = careMain.PatientDeliveryCareMainID;

            _unitOfWork.GetRepository<PatientDeliveryCareDetailInfo>().Insert(
                GetDeliveryCareDetail(parentInfo, careMain.PatientDeliveryCareMainID, 4747, 1104, newBorn.NewBornNum.ToString(), newBorn.AddEmployeeID));
            if (!string.IsNullOrEmpty(newBorn.Gender))
                _unitOfWork.GetRepository<PatientDeliveryCareDetailInfo>().Insert(
                    GetDeliveryCareDetail(parentInfo, careMain.PatientDeliveryCareMainID, newBorn.Gender == "1" ? 1105 : 1106, 1104, "", newBorn.AddEmployeeID));
            if (newBorn.WeightKG.HasValue)
                _unitOfWork.GetRepository<PatientDeliveryCareDetailInfo>().Insert(
                    GetDeliveryCareDetail(parentInfo, careMain.PatientDeliveryCareMainID, 1340, 1104, newBorn.WeightKG.ToString(), newBorn.AddEmployeeID));
            if (newBorn.Length.HasValue)
                _unitOfWork.GetRepository<PatientDeliveryCareDetailInfo>().Insert(
                    GetDeliveryCareDetail(parentInfo, careMain.PatientDeliveryCareMainID, 1107, 1104, newBorn.Length.ToString(), newBorn.AddEmployeeID));
        }
        private PatientDeliveryCareDetailInfo GetDeliveryCareDetail(InpatientDataInfo inpatient, string careMainID, int assessListID, int assessListGoupID, string assessValue, string userID)
        {
            var careDetail = new PatientDeliveryCareDetailInfo
            {
                PatientDeliveryCareMainID = careMainID,
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                AssessListID = assessListID,
                AssessListGroupID = assessListGoupID,
                AssessValue = assessValue,
                BookMarkID = "Newborn",
                DeleteFlag = ""
            };
            careDetail.PatientDeliveryCareDetailID = careDetail.GetId();
            careDetail.Modify(userID);
            return careDetail;
        }

        /// <summary>
        /// 写入新生儿数据
        /// </summary>
        /// <param name="newBornRecordView"></param>
        /// <param name="parentID"></param>
        /// <returns></returns>
        private NewBornRecordInfo CreateNewBornRecord(NewBornRecordView newBornRecordView, string parentID)
        {
            _logger.Info($"开始构造NewBornRecord实体,{newBornRecordView.CaseNumber}");

            // 检查必填字段是否为空
            if (string.IsNullOrEmpty(newBornRecordView.CaseNumber) ||
                string.IsNullOrEmpty(newBornRecordView.ChartNo) ||
                string.IsNullOrEmpty(newBornRecordView.ParentCaseNumber))
            {
                _logger.Error("必填字段为空，无法构造数据");
                return null;
            }

            try
            {
                NewBornRecordInfo newBornRecordInfo = new NewBornRecordInfo()
                {
                    NewBornID = Guid.NewGuid().ToString("N"),
                    ChartNO = newBornRecordView.ChartNo,
                    Casenumber = newBornRecordView.CaseNumber,
                    NewBornNum = (byte)newBornRecordView.OrderSN,
                    Gender = newBornRecordView.Gender,
                    DeliveryMode = "",
                    ParentCasenumber = newBornRecordView.ParentCaseNumber,
                    ParentChartNO = newBornRecordView.ParentChartNo,
                    ParentID = parentID,
                    AddDate = DateTime.Now,
                    ModifyDate = DateTime.Now,
                    ModifyPersonID = newBornRecordView.RecordPersonID,
                    DeliveryPosition = "",
                    RecordCode = "DeliveryMaintain",
                    PatientDeliveryCareMainID = "",
                    PatientDeliveryRecordID = "",
                    BookMarkID = "",
                    AddEmployeeID = newBornRecordView.RecordPersonID,
                    DeleteFlag = "",
                    Apgar1 = null,
                    Apgar5 = null,
                    Apgar10 = null
                };
                newBornRecordInfo.Length = TryParseDecimal(newBornRecordView.Height, out decimal tempLength) ? tempLength : null;
                newBornRecordInfo.WeightKG = TryParseDecimal(newBornRecordView.Weight, out decimal tempWeightKG) ? tempWeightKG : null;
                newBornRecordInfo.Weight = TryParseDecimal(newBornRecordView.Weight, out decimal tempWeight) ? (int)Math.Round(tempWeight * 1000) : (int?)null;
                _unitOfWork.GetRepository<NewBornRecordInfo>().Insert(newBornRecordInfo);
                int result = _unitOfWork.SaveChanges();

                if (result >= 0)
                {
                    return newBornRecordInfo;
                }
                else
                {
                    _logger.Error("保存新生儿记录失败");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"创建新生儿记录时发生异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 尝试将字符串转换为decimal类型。
        /// </summary>
        /// <param name="value">待转换的字符串。</param>
        /// <param name="result">转换成功时，包含转换后的值；转换失败时，包含默认值。</param>
        /// <returns>如果转换成功，则返回true；否则返回false。</returns>
        private bool TryParseDecimal(string value, out decimal result)
        {
            return decimal.TryParse(value, out result);
        }

        /// <summary>
        /// 写入ProFile
        /// </summary>
        /// <param name="newBornRecord"></param>
        /// <param name="syncDatasLogInfo"></param>
        /// <returns></returns>
        public async Task<bool> CreatePatientProfile(InpatientDataInfo patientData, NewBornRecordInfo newBornRecord, NewBornRecordView newBornRecordView, string hospitalID)
        {
            var list = new List<PatientProfile>();
            //产出情况和分娩方式AssessListID
            List<int> idArr = new List<int> { 5719, 5720, 5721, 3251, 5722, 5723, 5724, 2666, 2668, 5699, 1087 };
            var assessList = await _assessListRepository.GetAssessListSelectView(idArr);
            if (newBornRecord == null)
            {
                return false;
            }
            //新手儿接口数据写Patientprofile
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5205, "新生儿登记", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //体重
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 1108, Convert.ToString(newBornRecord.Weight), hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //身高
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 1954, Convert.ToString(newBornRecord.Length), hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //身长
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 1107, Convert.ToString(newBornRecord.Length), hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //母亲姓名
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5680, newBornRecordView.ParentName ?? "", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //新生儿姓名
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5696, newBornRecordView.BabyName ?? "", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //婴儿性别
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5686, newBornRecordView.Gender ?? "", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //婴儿出生日期
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 23, newBornRecordView.Birthday.HasValue ? Convert.ToString(newBornRecordView.Birthday.Value.Date) : null, hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            ////新生儿出生时间
            //list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 24, newBornRecordView.birthTime.HasValue ? Convert.ToString(newBornRecordView.birthTime) : null, hospitalID));
            //母亲住院号
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5679, newBornRecordView.ParentCaseNumber ?? "", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            //胎序
            list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5694, newBornRecordView.OrderSN.ToString() ?? "", hospitalID, patientData.AdmissionDate, patientData.AdmissionTime));
            ////总胎数
            //list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5695, hisNewBornData.BORN_COUNT ?? "", hospitalID));
            //婴儿死亡日期
            if (newBornRecordView.DeathTime.HasValue)
            {
                list.Add(CreateProfile(patientData, newBornRecord.NewBornID, 5724, newBornRecordView.DeathTime.HasValue ? Convert.ToString(newBornRecordView.DeathTime.Value) : null,
                    hospitalID, newBornRecordView.DeathTime.Value.Date, newBornRecordView.DeathTime.Value.TimeOfDay));
            }
            try
            {
                await _commonHelper.AddProfile(list);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("写入PatientProfile失败" + ex.ToString());
                return false;
            }
        }
        #endregion

        #region 同步患者仪器数据
        /// <summary>
        /// 同步患者机器数据
        /// </summary>
        /// <param name="clinicDataList">临床数据视图列表</param>
        /// <returns>返回操作是否成功</returns>
        public async Task<bool> SyncPatientMachineDataAsync(List<ViewModels.ClinicDataView> clinicDataList)
        {
            if (clinicDataList == null || clinicDataList.Count == 0 || string.IsNullOrEmpty(clinicDataList.First().CaseNumber))
            {
                return false;
            }
            // 根据第一个病例号获取住院数据
            var inpatient = await _inpatientDataRepository.GetByCaseNumberAsync(clinicDataList.First().CaseNumber);
            var clinicInfoList = new List<ClinicDataInfo>();
            // 遍历临床数据列表并创建ClinicDataInfo对象
            foreach (var clinicData in clinicDataList)
            {
                ClinicDataInfo clinicInfo = CreateClinicDataInfo(inpatient, clinicData);
                clinicInfoList.Add(clinicInfo);
            }
            // 批量插入临床数据
            await _unitOfWork.GetRepository<ClinicDataInfo>().InsertAsync(clinicInfoList);
            // 保存更改并返回结果
            return _unitOfWork.SaveChanges() >= 0;
        }

        /// <summary>
        /// 创建临床数据信息对象
        /// </summary>
        /// <param name="inpatient">住院数据信息</param>
        /// <param name="clinicData">临床数据视图</param>
        /// <returns>返回创建的临床数据信息对象</returns>
        private static ClinicDataInfo CreateClinicDataInfo(InpatientDataInfo inpatient, ViewModels.ClinicDataView clinicData)
        {
            var clinicInfo = new ClinicDataInfo
            {
                InpatientID = inpatient.ID,
                PatientID = inpatient.PatientID,
                StationID = inpatient.StationID,
                BedID = inpatient.BedID,
                System = string.Empty,
                Source = string.Empty,
                SourceID = string.Empty,
                DataDate = clinicData.DataDate,
                DataTime = clinicData.DataTime,
                AssessListID = clinicData.AssessListID,
                DataValue = clinicData.DataValue,
                Unit = clinicData.Unit,
                DeleteFlag = string.Empty
            };
            // 设置修改人信息
            clinicInfo.Modify(MODIFYPERSONID);
            return clinicInfo;
        }
        #endregion
        /// <summary>
        /// 更新患者护理评估中的护理级别
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="nursingLevel"></param>
        /// <param name="nursingLevelDateTime"></param>
        /// <param name="caseNumber"></param>
        /// <param name="inPatientChangeList"></param>
        /// <returns></returns>
        private async Task<bool> UpdateNursingLevelOfAdminssionAssessAsync(string inpatientID, string nursingLevel, DateTime nursingLevelDateTime, string caseNumber, List<InPatientChangeViewInfo> inPatientChangeList)
        {
            if (string.IsNullOrEmpty(nursingLevel) || string.IsNullOrEmpty(inpatientID))
            {
                _logger.Error($"更新入院评估护理级别失败，参数为空InpatientID={inpatientID}，nursingLevel=null");
                return false;
            }
            //获取护理评估更新护理级别的配置(多长时间更新护理级别)
            var internalMinutes = await _settingDescriptionRepository.GetTypeValue("UpdateAssessNursingLevel");
            if (!int.TryParse(internalMinutes, out var internalTime))
            {
                _logger.Warn($"settingDescription中没有获取到配置UpdateAssessNursingLevel");
                return false;
            }
            var dateTime = nursingLevelDateTime.AddMinutes(-internalTime);

            _logger.Info($"更新病人护理评估评估的时间点是{dateTime}后{internalTime}分钟");
            //存在间隔时间范围内的护理评估，更新对应的护理级别
            var assessMain = await _assessMainRepository.GetAssessMainByDatetimeAsync(inpatientID, dateTime);
            if (assessMain.Count <= 0)
            {
                _logger.Info($"CaseNumber={caseNumber}的患者{internalTime}分钟内，没有进行护理评估，护理级别发生变化，需停止护理问题");
                var inPatientChange = InPatientChangeView(inpatientID, caseNumber, nursingLevel, PatientType.PatientLevelChange);
                inPatientChangeList.Add(inPatientChange);
                return false;
            }
            assessMain = assessMain.Where(m => m.AssessDate.Add(m.AssessTime) < nursingLevelDateTime).OrderByDescending(m => m.AssessDate).ThenByDescending(m => m.AssessTime).ToList();
            if (assessMain.Count() < 0)
            {
                _logger.Info("没有找到一小时内的评估数据" + inpatientID + "护理级别变更时间" + nursingLevelDateTime.ToString());
                return false;
            }
            _syncLogService.InsertSyncLog(3, "1", "SyncInpatient", "UpdateInpatientData", caseNumber + "|更新病人信息", "TongBu", false);
            var nursingLevelNullFlag = false;
            foreach (var item in assessMain)
            {
                if (string.IsNullOrEmpty(item.NursingLevel))
                {
                    nursingLevelNullFlag = true;
                }
                else
                {
                    nursingLevelNullFlag = false;
                }
                item.NursingLevel = nursingLevel;
                if (!nursingLevelNullFlag)
                {
                    break;
                }
            }
            await _dataTableEditListService.AddEditLog(assessMain[0].InpatientID, 0, "NursingRecord", 1, "", null);
            return true;
        }
    }
}