﻿using Arch.EntityFrameworkCore.UnitOfWork;
using InterconnectCore.Data.Context;
using InterconnectCore.Models;
using InterconnectCore.Service.Interface;
using NLog;

namespace InterconnectCore.Services
{
    public class SyncLogService : ISyncLogService
    {
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public SyncLogService(IUnitOfWork<DataOutContext> unitOfWork)
        {
            _unitOfWorkOut = unitOfWork;
        }

        public bool InsertSyncLog(byte logLevel, string logGroupID, string logTypeCode, string logTypeName, string contents, string addPersonID, bool commit)
        {
            var syncLogInfo = new SyncLogInfo
            {
                LogLevel = logLevel,
                LogGroupID = logGroupID,
                LogTypeCode = logTypeCode,
                LogTypeName = logTypeName,
                Contents = contents,
                AddPersonID = addPersonID,
                AddDateTime = DateTime.Now
            };
            //使用同步方法，跨库异步有问题
            _unitOfWorkOut.GetRepository<SyncLogInfo>().Insert(syncLogInfo);
            if (commit)
            {
                return _unitOfWorkOut.SaveChanges() > 0;
            }
            return true;
        }

    }
}