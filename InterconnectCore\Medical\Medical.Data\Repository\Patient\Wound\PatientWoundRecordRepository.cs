﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Medical.ViewModels.View;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientWoundRecordRepository : IPatientWoundRecordRepository
    {
        private MedicalDbContext _medicalDbContext = null;

        /// <summary>
        ///  压力性损伤类伤口
        /// </summary>
        private readonly string[] WoundPressureTypes = new string[] { "1397", "1798", "1799" };

        /// <summary>
        /// 压力性损伤类伤口 分期，依次为 二期，三期，四期，不可分期，深部组织损伤
        /// </summary>
        private readonly int[] WoundSoreStages = new int[] { 1406, 1407, 1408, 1409, 1410 };

        public PatientWoundRecordRepository(MedicalDbContext db)
        {
            _medicalDbContext = db;
        }

        /// <summary>
        /// /根据ID获取伤口记录
        /// </summary>
        /// <param name="woundRecordID"></param>
        /// <returns></returns>
        public async Task<PatientWoundRecordInfo> GetByIDAsync(string woundRecordID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.PatientWoundRecordID == woundRecordID && t.DeleteFlag != "*").SingleOrDefaultAsync();
        }

        /// <summary>
        /// 根据病人序号获取伤口记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundRecordInfo>> GetListByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HandoverWoundCareIntervention>> GetWoundHandoverByAssessNum(string inpatientID, string num)
        {
            var datas = await (from a in _medicalDbContext.PatientWoundCareMainInfos
                               join b in _medicalDbContext.PatientWoundRecordInfos on a.PatientWoundRecordID equals b.PatientWoundRecordID
                               where a.InpatientID == inpatientID && b.AssessMainID == num && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverWoundCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   WoundKind = b.WoundKind,
                                   BodyPartID = b.BodyPartID,
                                   BodyPartName = b.BodyShowName,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   WoundCode = b.WoundCode,
                                   PatientWoundRecordID = a.PatientWoundRecordID,
                                   SeepageVolume = a.SeepageVolume,
                                   AssessMainID = b.AssessMainID,
                                   RecordsCode = a.RecordsCode
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.GroupBy(m => m.PatientWoundRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate).ThenByDescending(n => n.AssessTime).FirstOrDefault()).ToList();

            return datas;

        }

        public async Task<List<PatientWoundRecordInfo>> GetByHandoverAsync(string inpatientID, string handoverID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.InpatientID == inpatientID && t.HandoverID == handoverID && t.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundRecordInfo>> GetByDataPumpDate(DateTime yesterday, DateTime today)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(
                                m => m.DataPumpDate >= yesterday && m.DataPumpDate < today && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundRecordInfo>> GetUnFinishedAsync(string inpatientID)
        {
            var data = await GetListByInpatientIDAsync(inpatientID);
            return data.Where(m => m.EndDate == null).ToList();
        }

        public async Task<List<PatientWoundRecordInfo>> GetWoundRecordsByTimeAsync(DateTime endDate)
        {
            var list = await _medicalDbContext.PatientWoundRecordInfos.Where(
                                m => (m.EndDate >= endDate || m.EndDate == null) && !string.IsNullOrEmpty(m.WoundCode) && m.WoundCode != "1" && m.DeleteFlag != "*").ToListAsync();
            return list;
        }
        public async Task<List<PatientWoundRecordInfo>> GetOccuredAsync(DateTime startDate, DateTime endDate)
        {
            var list = await _medicalDbContext.PatientWoundRecordInfos.Where(
                                m => m.StartDate >= startDate.Date
                                && m.StartDate <= endDate.Date
                                && m.DeleteFlag != "*").ToListAsync();
            return list;
        }

        public async Task<List<PatientWoundRecordInfo>> GetPressureAdverseEventAsync(DateTime startDate, DateTime endDate)
        {
            var list = await (from w in _medicalDbContext.PatientWoundRecordInfos
                              join m in _medicalDbContext.PatientWoundCareMainInfos on w.PatientWoundRecordID equals m.PatientWoundRecordID
                              join d in _medicalDbContext.PatientWoundCareDetailInfos.Where(m => m.DeleteFlag != "*") on m.PatientWoundCareMainID equals d.PatientWoundCareMainID
                              where w.StartDate >= startDate.Date && w.StartDate <= endDate.Date && w.DeleteFlag != "*" && WoundPressureTypes.Contains(w.WoundKind)
                                    && m.NumberOfAssessment == 1 && m.DeleteFlag != "*" && WoundSoreStages.Contains(d.AssessListID)
                              select new PatientWoundRecordInfo()
                              {
                                  InpatientID = w.InpatientID,
                                  WoundKind = w.WoundKind,
                                  StartDate = w.StartDate,
                                  StartTime = w.StartTime,
                                  StationID = w.StationID,
                                  // 借字段回传AssessListID
                                  BodyPartID = d.AssessListID.ToString()
                              }).ToListAsync();
            return list;
        }

        public async Task<List<HandoverWoundCareIntervention>> GetPatientWoundCareIntervention(string inpatientID, DateTime startDate, TimeSpan startTime, DateTime endDate, TimeSpan endTime)
        {
            var datas = await (from a in _medicalDbContext.PatientWoundCareMainInfos
                               join b in _medicalDbContext.PatientWoundRecordInfos on a.PatientWoundRecordID equals b.PatientWoundRecordID
                               where a.InpatientID == inpatientID && a.AssessDate >= startDate && a.AssessDate <= endDate && a.BringToShift == true && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverWoundCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   WoundKind = b.WoundKind,
                                   BodyPartID = b.BodyPartID,
                                   BodyPartName = b.BodyShowName,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   WoundCode = b.WoundCode,
                                   PatientWoundRecordID = a.PatientWoundRecordID,
                                   SeepageVolume = a.SeepageVolume,
                                   AssessMainID = b.AssessMainID,
                                   RecordsCode = a.RecordsCode,
                                   PatientWoundCareMainID = a.PatientWoundCareMainID,
                                   StationID = a.StationID,
                                   PUSH = a.PUSH.HasValue ? a.PUSH.Value.ToString() : ""
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.Where(m => m.AssessDate.Add(m.AssessTime) >= startDate.Date.Add(startTime) && m.AssessDate.Add(m.AssessTime) <= endDate.Date.Add(endTime))
                .GroupBy(m => m.PatientWoundRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }

        public async Task<List<HandoverWoundCareIntervention>> GetPatientAllWoundCareIntervention(string inpatientID)
        {
            var datas = await (from a in _medicalDbContext.PatientWoundCareMainInfos
                               join b in _medicalDbContext.PatientWoundRecordInfos on a.PatientWoundRecordID equals b.PatientWoundRecordID
                               where a.InpatientID == inpatientID && b.EndDate == null && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                               select new HandoverWoundCareIntervention
                               {
                                   InpatientID = a.InpatientID,
                                   AssessDate = a.AssessDate,
                                   AssessTime = a.AssessTime,
                                   WoundKind = b.WoundKind,
                                   BodyPartID = b.BodyPartID,
                                   BodyPartName = b.BodyShowName,
                                   CareIntervention = a.CareIntervention,
                                   Sort = 0,
                                   WoundCode = b.WoundCode,
                                   PatientWoundRecordID = a.PatientWoundRecordID,
                                   SeepageVolume = a.SeepageVolume,
                                   AssessMainID = b.AssessMainID,
                                   RecordsCode = a.RecordsCode,
                                   PatientWoundCareMainID = a.PatientWoundCareMainID,
                                   StationID = a.StationID,
                                   PUSH = a.PUSH.HasValue ? a.PUSH.Value.ToString() : "",
                                   SourceDataID = b.SourceDataID
                               }).ToListAsync();

            if (datas.Count == 0)
            {
                return datas;
            }

            datas = datas.GroupBy(m => m.PatientWoundRecordID)
                .Select(m => m.OrderByDescending(n => n.AssessDate.Date.Add(n.AssessTime)).FirstOrDefault()).ToList();

            return datas;
        }

        public async Task<List<PatientWoundRecordInfo>> GetByAssessNumAsync(string inpatientID, string num)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.InpatientID == inpatientID && m.AssessMainID == num && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<PatientWoundRecordInfo>> GetByStartDate(DateTime startDate)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.StartDate == startDate.Date && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundRecordInfo>> GetPaitentWound(string inpatientID, int stationID, string wonundKind)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.InpatientID == inpatientID
                           && m.StationID == stationID && m.WoundKind == wonundKind && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<PatientWoundRecordInfo>> GetNeedDataColumnByInpatientIDAsync(string inpatientID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*")
                .Select(m => new PatientWoundRecordInfo
                {
                    BodyPartID = m.BodyPartID,
                    PatientWoundRecordID = m.PatientWoundRecordID
                })
                .ToListAsync();
        }
        public async Task<AssessTimeView> GetAssessTimeByID(string assessMainID)
        {
            var assessMain = await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.PatientWoundRecordID == assessMainID && m.DeleteFlag != "*")
                     .Select(m => new AssessTimeView { StartDate = m.StartDate, StartTime = m.StartTime })
                     .FirstOrDefaultAsync();
            return assessMain;
        }

        /// <summary>
        /// 获取CDR病人压伤记录
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="languge"></param>
        /// <param name="lastTranslateDate"></param>
        /// <returns>List<CDR_SkinUlcerRecView></returns>
        public async Task<List<CDA_SkinUlcerRecView>> GetCDA_SkinUlcerRecViewAsync(string hospitalID, int languge, DateTime lastTranslateDate)
        {
            var query = from woundRecord in _medicalDbContext.PatientWoundRecordInfos.Where(m => m.WoundKind == "1397")
                        join inpatient in _medicalDbContext.InpatientDatas on woundRecord.InpatientID equals inpatient.ID
                        join patientBasic in _medicalDbContext.PatientBasicDatas.Where(m => m.DeleteFlag != "*") on inpatient.PatientID equals patientBasic.PatientID
                        join department in _medicalDbContext.departmentListInfos.Where(m => m.DeleteFlag != "*") on woundRecord.DepartmentListID equals department.ID
                        join station in _medicalDbContext.StationListInfos.Where(m => m.DeleteFlag != "*") on woundRecord.StationID equals station.ID
                        //on inpatient.ID equals diagnose.InpatientID
                        where (woundRecord.ModifyDate.HasValue && woundRecord.ModifyDate.Value >= lastTranslateDate)
                        || (!woundRecord.ModifyDate.HasValue && woundRecord.AddDate >= lastTranslateDate)
                        && department.HospitalID == hospitalID && station.HospitalID == hospitalID
                        && inpatient.HospitalID == hospitalID && patientBasic.HospitalID == hospitalID
                        select new CDA_SkinUlcerRecView
                        {
                            DCID = woundRecord.PatientWoundRecordID,
                            InpatientID = woundRecord.InpatientID,
                            ChartNo = woundRecord.ChartNo,
                            CaseNumber = woundRecord.CaseNumber,
                            PatientID = inpatient.PatientID,
                            PatientType = "04",
                            VisitID = inpatient.CaseNumber,
                            EffectiveFlag = woundRecord.DeleteFlag == "*" ? "0" : "1",
                            HospizationId = inpatient.CaseNumber,
                            Name = patientBasic.PatientName,
                            Sex = patientBasic.Gender ?? "9",
                            Age = (inpatient.Age ?? 0).ToString(),
                            DeptCode = department.DepartmentCode,
                            DeptName = department.Department,
                            WardAreaCode = station.StationCode,
                            WardAreaName = station.StationName,
                            SickbedId = woundRecord.BedNumber,
                            AdmissionDate = inpatient.AdmissionDate.Add(inpatient.AdmissionTime),
                            DiagnoseCode = inpatient.ICDCode,
                            DiagnoseName = inpatient.Diagnosis,
                            OccurDate = woundRecord.StartDate.Add(woundRecord.StartTime),
                            FillDate = woundRecord.AddDate,
                            FillNurseSign = woundRecord.AddEmployeeID,
                            RecordId = "",
                            ConfirmedNurseSign = woundRecord.AddEmployeeID,
                            ConfirmDate = woundRecord.ModifyDate ?? woundRecord.AddDate,
                            SkinUlcerLevelBeforeAdmission = string.Empty,
                            SkinUlcerSourceBeforeAdmission = "0",
                            FirstLevelUlcerFlagInHospital = "0",
                            SecondLevelUlcerFlagInHospital = "0",
                            ThirdLevelUlcerFlagInHospital = "0",
                            FourthLevelUlcerFlagInHospital = "0",
                            TimeStamp = woundRecord.ModifyDate ?? woundRecord.AddDate,
                            BodyPartName = woundRecord.BodyPartID,
                            BirthDate = patientBasic.DateOfBirth,
                            EndDate = woundRecord.EndDate,
                        };
            return await query.ToListAsync();
        }
        /// <summary>
        /// 获取伤口部位和伤口类型
        /// </summary>
        /// <param name="patientWoundRecordID"></param>
        /// <returns></returns>
        public async Task<PatientWoundRecordInfo> GetWoundKindAndBodyPartByIDAsync(string patientWoundRecordID)
        {
            if (string.IsNullOrWhiteSpace(patientWoundRecordID))
            {
                return null;
            }
            return await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.PatientWoundRecordID == patientWoundRecordID && m.DeleteFlag != "*")
                .Select(m => new PatientWoundRecordInfo
                {
                    BodyShowName = m.BodyShowName,
                    WoundKind = m.WoundKind
                }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 统计患者同部位同种伤口的个数
        /// </summary>
        /// <param name="inpatientID">病人序号</param>
        /// <returns></returns>
        public async Task<Dictionary<(string, string), int>> GetWoundKindNumbersByInpatientID(string inpatientID)
        {
            var data = await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*")
                .Select(m => new { m.WoundKind, m.BodyPartID }).ToListAsync();
            return data.GroupBy(m => new { m.WoundKind, m.BodyPartID }).ToDictionary(m => (m.Key.WoundKind, m.Key.BodyPartID), n => n.Count());
        }
        /// <summary>
        /// 根据主记录ID获取伤口种类（不过滤删除|因为主键唯一）
        /// </summary>
        /// <param name="woundRecordID">伤口主记录ID</param>
        /// <returns></returns>
        public async Task<string> GetWoundKindByIDAsync(string woundRecordID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(m => m.PatientWoundRecordID == woundRecordID).Select(m => m.WoundKind).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据病人住院唯一号或者主键获取伤口记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<PatientWoundRecordInfo>> GetListByInpatientIDOrRecordIDAsync(string inpatientID, string recordID = null)
        {
            var list = await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.InpatientID == inpatientID && t.DeleteFlag != "*").ToListAsync();
            if (string.IsNullOrEmpty(recordID))
            {
                return list;
            }
            return list.Where(m => m.PatientWoundRecordID == recordID).ToList();
        }
        /// <summary>
        /// 根据伤口类型和部位查看伤口数量
        /// </summary>
        /// <param name="inpatientID">唯一ID</param>
        /// <param name="woundKind">伤口类别</param>
        /// <param name="bodyPartID">部位ID</param>
        /// <returns></returns>
        public async Task<int> GetCountByWoundKindAndBodyPartIDAsync(string inpatientID, string woundKind, string bodyPartID)
        {
            return await _medicalDbContext.PatientWoundRecordInfos.Where(t => t.InpatientID == inpatientID &&
                t.WoundKind == woundKind && t.BodyPartID == bodyPartID && t.DeleteFlag != "*").CountAsync();
        }
    }
}