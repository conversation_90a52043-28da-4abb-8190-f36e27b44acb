﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using NLog;

namespace MedicalExternalCommon.Service
{

    public class ExternalProfileCommonService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IPatientProfileRepository _patientProfileRepository;
        private readonly IUnitOfWork<MedicalDbContext> _unitOfWork;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
 
        public ExternalProfileCommonService(IPatientProfileRepository patientProfileRepository,
            IUnitOfWork<MedicalDbContext> unitOfWork,
            IAppConfigSettingRepository appConfigSettingRepository
            )
        {
            _unitOfWork = unitOfWork;
            _patientProfileRepository = patientProfileRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
        }
 

        /// <summary>
        /// 删除病人profile
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <param name="profiles"></param>
        public void DeletePatientProfile(string inpatientID, List<int> profileAssessListIDs, bool autoSave = false)
        {
            if (profileAssessListIDs.Count == 0 || inpatientID == "")
            {
                return;
            }
            var currProfiles = _patientProfileRepository.GetAsync(inpatientID, true).Result;

            foreach (var profile in currProfiles)
            {

                if (profileAssessListIDs.Contains(profile.AssessListID))
                {
                    _unitOfWork.GetRepository<PatientProfileInfo>().Delete(profile);
                }
            }
            if (autoSave)
            {
                _unitOfWork.SaveChanges();
            }

        }

        /// <summary>
        /// 删除profile 此刻新入院病人最新一次入院信息还没有存在ccc中（根据InpatientID判断）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<bool> DeletePatientProfileOwnDueDayAsync(string chartNo, string inpatientID)
        {
            var responseResult = "";
            if (string.IsNullOrEmpty(chartNo))
            {
                return false;
            }
            var deletePatientProfileOwnDueDayUrl = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "DeletePatientProfileOwnDueDay");
            //deletePatientProfileOwnDueDayUrl = "http://localhost:56194/api/PatientProfile/DeletePatientProfileOwnDueDay";
            if (string.IsNullOrEmpty(deletePatientProfileOwnDueDayUrl))
            {
                _logger.Warn("获取SendMQMessageAPI失败,发送地址为空");
                return false;
            }
            var url = deletePatientProfileOwnDueDayUrl + $"?ChartNo={chartNo}&InpatientID={inpatientID}";
            try
            {
                responseResult = await HttpHelper.HttpPostAsync(url, null, "application/json");
            }
            catch (System.Exception ex)
            {
                _logger.Error("新入院病人删除旧的有到期时间的profile数据失败" + ex.ToString());
                return false;
            }
            if (!string.IsNullOrEmpty(responseResult))
            {
                _logger.Info("新入院病人删除旧的有到期时间的profile结果:" + responseResult);
                var ret = ListToJson.ToList<ResponseResult>(responseResult);

                if (ret == null || ret.Code != 1)
                {
                    _logger.Error("新入院病人删除旧的有到期时间的profile失败");
                    return false;
                }
            }
            return true;
        }
    }
}

