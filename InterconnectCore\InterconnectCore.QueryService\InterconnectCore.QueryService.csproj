﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <BaseOutputPath>..\InterconnectCore.API\bin</BaseOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\InterconnectCore.Common\InterconnectCore.Common.csproj" />
    <ProjectReference Include="..\InterconnectCore.Data.Interface\InterconnectCore.Data.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.Data\InterconnectCore.Data.csproj" />
    <ProjectReference Include="..\InterconnectCore.Models\InterconnectCore.Models.csproj" />
    <ProjectReference Include="..\InterconnectCore.QueryService.Interface\InterconnectCore.QueryService.Interface.csproj" />
    <ProjectReference Include="..\InterconnectCore.ViewModels\InterconnectCore.ViewModels.csproj" />
    <ProjectReference Include="..\Medical\Medical.Common\Medical.Common.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data.Interface\Medical.Data.Interface.csproj" />
    <ProjectReference Include="..\Medical\Medical.Data\Medical.Data.csproj" />
    <ProjectReference Include="..\Medical\Medical.Models\Medical.Models.csproj" />
    <ProjectReference Include="..\Medical\Medical.ViewModels\Medical.ViewModels.csproj" />
  </ItemGroup>

</Project>
