﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class ICDToAssessRepository : IICDToAssessRepository
    {
        private readonly MedicalDbContext _medicalDbContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        public ICDToAssessRepository(
              MedicalDbContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService)
        {
            _medicalDbContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<ICDToAssessInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            return await _medicalDbContext.ICDToAssessInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*")
                                .Select(m => new ICDToAssessView
                                {
                                    ICDCode = m.ICDCode,
                                    DepartmentListID = m.DepartmentListID,
                                    AssessListID = m.AssessListID
                                }).ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.ICDToAssess.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 获取ICDToAssess
        /// </summary>
        /// <returns></returns>
        public async Task<List<ICDToAssessView>> GetICDToAssessListView()
        {
            return await GetCacheAsync() as List<ICDToAssessView>;
        }
    }
}