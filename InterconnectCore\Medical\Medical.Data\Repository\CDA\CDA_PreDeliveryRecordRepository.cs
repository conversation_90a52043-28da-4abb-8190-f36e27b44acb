﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_PreDeliveryRecordRepository : ICDAPreDeliveryRecordRepository
    {
        private readonly CDADBContext _cDADBContext;

        public CDA_PreDeliveryRecordRepository(CDADBContext cDADBContext)
        {
            _cDADBContext = cDADBContext;
        }

        /// <summary>
        /// 取得最后一笔数据异动时间
        /// </summary>
        /// <returns>DateTime</returns>
        public async Task<DateTime> GetLastSyncTime()
        {
            var lastDateTime = await _cDADBContext.CDA_PreDeliveryRecordInfos
                .Select(m => (DateTime?)m.ModifyDateTime)
                .MaxAsync();
            return lastDateTime ?? DateTime.MinValue;
        }
        /// <summary>
        /// 根据主键获取
        /// </summary>
        /// <param name="dCIDs">主键</param>
        /// <returns></returns>
        public async Task<CDA_PreDeliveryRecordInfo[]> GetRecordBydCIDs(string[] dCIDs)
        {
            return await _cDADBContext.CDA_PreDeliveryRecordInfos.Where(m => dCIDs.Contains(m.DCID))
                .ToArrayAsync();
        }
    }
}
