﻿using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class PatientScheduleJsonRepository : IPatientScheduleJsonRepository
    {
        private MedicalDbContext _dbContext = null;

        public PatientScheduleJsonRepository(MedicalDbContext db)
        {
            _dbContext = db;
        }

        public async Task<PatientScheduleJsonInfo> GetByScheduleMainID(string scheduleMainID)
        {
            return await _dbContext.PatientScheduleJsonInfos.Where(m =>
               m.SourceID == scheduleMainID
             && m.DeleteFlag != "*").SingleOrDefaultAsync();
        }
    }
}
