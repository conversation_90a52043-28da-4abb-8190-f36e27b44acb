﻿using Medical.Models;
using Microsoft.EntityFrameworkCore;

namespace Medical.Data.Context
{
    public partial class CADBContext : DbContext
    {
        public CADBContext(DbContextOptions<CADBContext> options)
            : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<SealRecordDetailInfo>().HasKey(m => (new { m.SealRecordID, m.DataKey }));

            base.OnModelCreating(builder);
        }

        public DbSet<SealRecordInfo> SealRecordInfos { get; set; }

        public DbSet<SealRecordDetailInfo> SealRecordDetailInfos { get; set; }
    }
}