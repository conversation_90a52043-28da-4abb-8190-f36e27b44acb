﻿using Medical.Common;
using Medical.Data.Context;
using Medical.Data.Interface;
using Medical.Models.CDADocument;
using Medical.ViewModels;
using Microsoft.EntityFrameworkCore;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medical.Data.Repository
{
    public class CDA_GeneralNursingRecRepository : ICDA_GeneralNursingRecRepository
    {
        private readonly CDADBContext _cDADBConnect = null;
        private readonly static Logger _logger = LogManager.GetCurrentClassLogger();

        public CDA_GeneralNursingRecRepository(CDADBContext cDADBConnect)
        {
            _cDADBConnect = cDADBConnect;
        }

        public async Task<CDA_GeneralNursingRecInfo> GetByDCID(string DCID)
        {
            return await _cDADBConnect.CDA_GeneralNursingRecInfos.Where(m => m.DCID == DCID).FirstOrDefaultAsync();
        }

        public async Task<Dictionary<string, string>> GetIDByDCID(string DCID)
        {
            return await _cDADBConnect.CDA_GeneralNursingRecInfos.Where(m => m.DCID == DCID).Select(m =>
            new Dictionary<string, string> {
                {"id", m.DCID},
                {"deleteFlag",m.DeleteFlag??"" }
            }).FirstOrDefaultAsync();
        }

        public async Task<DateTime?> GetLastTimeStamp()
        {
            return await _cDADBConnect.CDA_GeneralNursingRecInfos.Select(m => m.TimeStamp).MaxAsync();
        }

        public async Task<bool> Save(CDAGeneralNursingRecView data, CDA_GeneralNursingRecInfo attachedObj, bool autoDetectedSave = false)
        {
            if (autoDetectedSave)
            {
                try
                {
                    await _cDADBConnect.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.Error("efcore跟踪的实体改变字段被监测到后保存失败{一般护理记录CDAService}" + ex.ToString());
                    return false;
                }
            }

            var cDA_GeneralNursingRec = data.CDA_GeneralNursingRec;
            if (cDA_GeneralNursingRec == null)
            {
                _logger.Error("保存记录时，所传参数为null");
                return true;
            }
            //如果是更新
            if (data.UPDataFlag)
            {
                _cDADBConnect.Entry(attachedObj).CurrentValues.SetValues(cDA_GeneralNursingRec);
            }
            else
            {
                _cDADBConnect.Add(cDA_GeneralNursingRec);
            }
            try
            {
                await _cDADBConnect.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("保存的数据" + ListToJson.ToJson(cDA_GeneralNursingRec));
                _logger.Error("新增generalNursingRec数据，更新数据库失败" + ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 获取妇幼未同步数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<CDA_GeneralNursingRecInfo>> GetDataByDataPumpFlag()
        {
            return await _cDADBConnect.CDA_GeneralNursingRecInfos.Where(m => m.DataPumpFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据起止时间获取妇幼未同步数据
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<List<CDA_GeneralNursingRecInfo>> GetByDate(DateTime? startDateTime, DateTime? endDateTime)
        {
            var now = DateTime.Now.Date;
            if (!startDateTime.HasValue)
            {
                startDateTime = now;
            }
            if (!endDateTime.HasValue)
            {
                endDateTime = now.AddDays(1);
            }
            // 开始结束传的是同一天，则取这一天（24小时）的数据
            if (startDateTime.Value.Date == endDateTime.Value.Date)
            {
                endDateTime = endDateTime.Value.AddSeconds(86399);
            }

            return await _cDADBConnect.CDA_GeneralNursingRecInfos.Where(m => m.DataPumpFlag != "*"
            && m.TimeStamp >= startDateTime && m.TimeStamp <= endDateTime)
                // 暂时过掉
                .Where(m => !string.IsNullOrEmpty(m.LocalChartNO)).Take(1000).ToListAsync();
        }
    }
}
